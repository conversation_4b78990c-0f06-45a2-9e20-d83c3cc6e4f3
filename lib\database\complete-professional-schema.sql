-- COMPLETE PROFESSIONAL JEWELRY MANAGEMENT SYSTEM DATABASE SCHEMA
-- Version: 3.0.0 - Full-Fledged Professional Implementation
-- Date: January 31, 2025
-- Description: Comprehensive, uncompromised schema for professional jewelry business

-- Create database with proper character set and collation
CREATE DATABASE IF NOT EXISTS jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jewellers_db;

-- Enable foreign key checks and strict mode
SET FOREIGN_KEY_CHECKS = 1;
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ============================================================================
-- CORE BUSINESS CONFIGURATION TABLES
-- ============================================================================

-- 1. BUSINESS SETTINGS - Complete business configuration
CREATE TABLE business_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  business_name VARCHAR(255) NOT NULL,
  business_type ENUM('retail', 'wholesale', 'manufacturing', 'mixed') DEFAULT 'retail',
  business_registration_number VARCHAR(100),
  establishment_date DATE,
  
  -- Address Information
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- Contact Information
  phone VARCHAR(20),
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  website VARCHAR(255),
  
  -- Legal Information
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  tan_number VARCHAR(20),
  license_number VARCHAR(100),
  fssai_number VARCHAR(50),
  
  -- Banking Information
  bank_name VARCHAR(255),
  bank_branch VARCHAR(255),
  bank_account_number VARCHAR(50),
  bank_ifsc VARCHAR(20),
  bank_swift_code VARCHAR(20),
  
  -- Business Configuration
  logo_url VARCHAR(500),
  signature_url VARCHAR(500),
  currency VARCHAR(10) DEFAULT 'INR',
  timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
  financial_year_start ENUM('april', 'january') DEFAULT 'april',
  
  -- Tax Configuration
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 3.00,
  cess_rate DECIMAL(5,2) DEFAULT 0.00,
  tcs_rate DECIMAL(5,2) DEFAULT 0.00,
  tds_rate DECIMAL(5,2) DEFAULT 0.00,
  
  -- Business Rules
  allow_negative_stock BOOLEAN DEFAULT FALSE,
  auto_generate_barcodes BOOLEAN DEFAULT TRUE,
  enable_loyalty_program BOOLEAN DEFAULT TRUE,
  enable_scheme_management BOOLEAN DEFAULT TRUE,
  enable_repair_tracking BOOLEAN DEFAULT TRUE,
  
  -- Pricing Configuration
  default_making_charge_percentage DECIMAL(5,2) DEFAULT 15.00,
  default_wastage_percentage DECIMAL(5,2) DEFAULT 8.00,
  default_margin_percentage DECIMAL(5,2) DEFAULT 25.00,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. SYSTEM SETTINGS - Application configuration
CREATE TABLE system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_category ENUM('general', 'security', 'notification', 'integration', 'reporting') DEFAULT 'general',
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json', 'date', 'time', 'datetime') DEFAULT 'string',
  description TEXT,
  is_system BOOLEAN DEFAULT FALSE,
  is_encrypted BOOLEAN DEFAULT FALSE,
  validation_rules JSON,
  updated_by VARCHAR(36),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_setting_category (setting_category),
  INDEX idx_setting_key (setting_key),
  INDEX idx_is_system (is_system)
);

-- 3. USERS - Complete user management
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  employee_code VARCHAR(50) UNIQUE,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  
  -- Personal Information
  title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
  first_name VARCHAR(100) NOT NULL,
  middle_name VARCHAR(100),
  last_name VARCHAR(100) NOT NULL,
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  
  -- Contact Information
  phone VARCHAR(20),
  alternate_phone VARCHAR(20),
  emergency_contact_name VARCHAR(255),
  emergency_contact_phone VARCHAR(20),
  
  -- Address Information
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- Employment Information
  role ENUM('super_admin', 'admin', 'manager', 'sales_manager', 'sales_staff', 'accountant', 'cashier', 'security', 'viewer') NOT NULL DEFAULT 'sales_staff',
  department ENUM('management', 'sales', 'accounts', 'operations', 'security', 'support') DEFAULT 'sales',
  designation VARCHAR(100),
  joining_date DATE,
  salary DECIMAL(12,2),
  commission_percentage DECIMAL(5,2) DEFAULT 0.00,
  
  -- System Access
  permissions JSON,
  allowed_modules JSON,
  access_level ENUM('full', 'limited', 'read_only') DEFAULT 'limited',
  max_discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  max_transaction_amount DECIMAL(15,2) DEFAULT 0.00,
  
  -- Security
  is_active BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  last_login TIMESTAMP NULL,
  login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,
  password_reset_token VARCHAR(255) NULL,
  password_reset_expires TIMESTAMP NULL,
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  two_factor_secret VARCHAR(255),
  
  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_employee_code (employee_code),
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_department (department),
  INDEX idx_is_active (is_active),
  INDEX idx_joining_date (joining_date),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 4. CUSTOMERS - Comprehensive customer management
CREATE TABLE customers (
  id VARCHAR(36) PRIMARY KEY,
  customer_code VARCHAR(50) UNIQUE NOT NULL,
  customer_type ENUM('individual', 'business', 'corporate') DEFAULT 'individual',
  
  -- Personal Information
  title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
  first_name VARCHAR(100) NOT NULL,
  middle_name VARCHAR(100),
  last_name VARCHAR(100),
  business_name VARCHAR(255),
  date_of_birth DATE,
  anniversary_date DATE,
  gender ENUM('male', 'female', 'other'),
  occupation VARCHAR(100),
  
  -- Contact Information
  phone VARCHAR(20) NOT NULL,
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  whatsapp_number VARCHAR(20),
  
  -- Address Information
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  landmark VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- Legal Information
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  aadhar_number VARCHAR(20),
  passport_number VARCHAR(50),
  driving_license VARCHAR(50),
  
  -- Business Information
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  credit_days INT DEFAULT 0,
  payment_terms VARCHAR(255),
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  
  -- Financial Tracking
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  total_payments DECIMAL(15,2) DEFAULT 0.00,
  last_payment_date DATE,
  
  -- Loyalty Program
  loyalty_points INT DEFAULT 0,
  loyalty_tier ENUM('bronze', 'silver', 'gold', 'platinum', 'diamond') DEFAULT 'bronze',
  membership_number VARCHAR(50),
  membership_date DATE,
  
  -- Preferences
  preferred_contact ENUM('phone', 'email', 'sms', 'whatsapp') DEFAULT 'phone',
  preferred_language ENUM('english', 'hindi', 'regional') DEFAULT 'english',
  preferred_metal ENUM('gold', 'silver', 'platinum', 'diamond') DEFAULT 'gold',
  preferred_occasions JSON,
  
  -- Additional Information
  notes TEXT,
  special_instructions TEXT,
  referral_source VARCHAR(255),
  referred_by VARCHAR(36),
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  is_vip BOOLEAN DEFAULT FALSE,
  is_blacklisted BOOLEAN DEFAULT FALSE,
  blacklist_reason TEXT,
  last_visit TIMESTAMP NULL,
  visit_count INT DEFAULT 0,
  
  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_customer_code (customer_code),
  INDEX idx_customer_type (customer_type),
  INDEX idx_phone (phone),
  INDEX idx_email (email),
  INDEX idx_business_name (business_name),
  INDEX idx_city (city),
  INDEX idx_loyalty_tier (loyalty_tier),
  INDEX idx_is_active (is_active),
  INDEX idx_is_vip (is_vip),
  INDEX idx_last_visit (last_visit),
  FOREIGN KEY (referred_by) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 5. SUPPLIERS - Complete supplier management
CREATE TABLE suppliers (
  id VARCHAR(36) PRIMARY KEY,
  supplier_code VARCHAR(50) UNIQUE NOT NULL,
  supplier_type ENUM('manufacturer', 'wholesaler', 'artisan', 'refiner', 'importer', 'other') DEFAULT 'wholesaler',
  
  -- Business Information
  company_name VARCHAR(255) NOT NULL,
  trade_name VARCHAR(255),
  business_type ENUM('proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp') DEFAULT 'proprietorship',
  establishment_date DATE,
  
  -- Contact Person
  contact_person VARCHAR(255),
  contact_designation VARCHAR(100),
  contact_phone VARCHAR(20) NOT NULL,
  contact_email VARCHAR(255),
  
  -- Additional Contacts
  alternate_contact_person VARCHAR(255),
  alternate_contact_phone VARCHAR(20),
  alternate_contact_email VARCHAR(255),
  
  -- Address Information
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  landmark VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  
  -- Legal Information
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  tan_number VARCHAR(20),
  cin_number VARCHAR(50),
  msme_number VARCHAR(50),
  
  -- Banking Information
  bank_name VARCHAR(255),
  bank_branch VARCHAR(255),
  bank_account_number VARCHAR(50),
  bank_ifsc VARCHAR(20),
  bank_account_type ENUM('savings', 'current', 'cc', 'od') DEFAULT 'current',
  
  -- Business Terms
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  credit_days INT DEFAULT 0,
  payment_terms VARCHAR(255),
  discount_offered DECIMAL(5,2) DEFAULT 0.00,
  minimum_order_amount DECIMAL(15,2) DEFAULT 0.00,
  
  -- Financial Tracking
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  total_payments DECIMAL(15,2) DEFAULT 0.00,
  last_purchase_date DATE,
  last_payment_date DATE,
  
  -- Performance Metrics
  rating DECIMAL(3,2) DEFAULT 0.00,
  quality_rating DECIMAL(3,2) DEFAULT 0.00,
  delivery_rating DECIMAL(3,2) DEFAULT 0.00,
  service_rating DECIMAL(3,2) DEFAULT 0.00,
  
  -- Specialization
  specializes_in JSON, -- metals, categories, services
  certifications JSON,
  licenses JSON,
  
  -- Additional Information
  notes TEXT,
  special_instructions TEXT,
  website VARCHAR(255),
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  is_preferred BOOLEAN DEFAULT FALSE,
  is_blacklisted BOOLEAN DEFAULT FALSE,
  blacklist_reason TEXT,
  
  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_supplier_code (supplier_code),
  INDEX idx_supplier_type (supplier_type),
  INDEX idx_company_name (company_name),
  INDEX idx_contact_phone (contact_phone),
  INDEX idx_city (city),
  INDEX idx_gst_number (gst_number),
  INDEX idx_is_active (is_active),
  INDEX idx_is_preferred (is_preferred),
  INDEX idx_rating (rating),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 6. CATEGORIES - Hierarchical product categorization
CREATE TABLE categories (
  id VARCHAR(36) PRIMARY KEY,
  category_code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  parent_id VARCHAR(36),
  category_level INT DEFAULT 1,
  category_path VARCHAR(500), -- For hierarchical queries

  -- Business Configuration
  hsn_code VARCHAR(20),
  sac_code VARCHAR(20),
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,

  -- Visual
  image_url VARCHAR(500),
  icon_class VARCHAR(100),
  color_code VARCHAR(20),

  -- Pricing Configuration
  making_charge_type ENUM('percentage', 'fixed', 'per_gram', 'per_piece') DEFAULT 'percentage',
  making_charge_value DECIMAL(10,2) DEFAULT 0.00,
  wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
  margin_percentage DECIMAL(5,2) DEFAULT 0.00,

  -- Additional Charges
  stone_setting_charge DECIMAL(10,2) DEFAULT 0.00,
  polishing_charge DECIMAL(10,2) DEFAULT 0.00,
  certification_charge DECIMAL(10,2) DEFAULT 0.00,

  -- Business Rules
  requires_hallmarking BOOLEAN DEFAULT FALSE,
  requires_certification BOOLEAN DEFAULT FALSE,
  allows_customization BOOLEAN DEFAULT TRUE,
  minimum_weight DECIMAL(8,3) DEFAULT 0.000,
  maximum_weight DECIMAL(8,3) DEFAULT 999.999,

  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_category_code (category_code),
  INDEX idx_name (name),
  INDEX idx_parent_id (parent_id),
  INDEX idx_category_level (category_level),
  INDEX idx_hsn_code (hsn_code),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order),
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 7. METAL RATES - Comprehensive metal pricing
CREATE TABLE metal_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
  purity VARCHAR(20) NOT NULL,

  -- Pricing Information
  rate_per_gram DECIMAL(12,2) NOT NULL,
  rate_per_10gram DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 10) STORED,
  rate_per_tola DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 11.664) STORED,
  rate_per_ounce DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 31.1035) STORED,

  -- Validity
  effective_date DATE NOT NULL,
  effective_time TIME DEFAULT '00:00:00',
  expiry_date DATE,
  is_active BOOLEAN DEFAULT TRUE,

  -- Source Information
  source ENUM('manual', 'api', 'import', 'market') DEFAULT 'manual',
  source_reference VARCHAR(255),
  exchange_name VARCHAR(100),

  -- Market Information
  opening_rate DECIMAL(12,2),
  closing_rate DECIMAL(12,2),
  high_rate DECIMAL(12,2),
  low_rate DECIMAL(12,2),
  change_amount DECIMAL(12,2),
  change_percentage DECIMAL(5,2),

  -- Additional Information
  notes TEXT,
  currency VARCHAR(10) DEFAULT 'INR',

  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_active_rate (metal_type, purity, effective_date, is_active),
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_effective_date (effective_date),
  INDEX idx_is_active (is_active),
  INDEX idx_source (source),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 8. EXCHANGE RATES - Exchange pricing (can differ from metal rates)
CREATE TABLE exchange_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
  purity VARCHAR(20) NOT NULL,

  -- Exchange Pricing
  rate_per_gram DECIMAL(12,2) NOT NULL,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  effective_rate DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * (100 - discount_percentage) / 100) STORED,

  -- Validity
  effective_date DATE NOT NULL,
  effective_time TIME DEFAULT '00:00:00',
  expiry_date DATE,
  is_active BOOLEAN DEFAULT TRUE,

  -- Source Information
  source ENUM('manual', 'derived', 'import') DEFAULT 'derived',
  base_metal_rate_id VARCHAR(36),

  -- Conditions
  minimum_weight DECIMAL(8,3) DEFAULT 0.000,
  maximum_weight DECIMAL(8,3) DEFAULT 999.999,
  condition_requirements JSON,

  -- Additional Information
  notes TEXT,

  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_active_exchange_rate (metal_type, purity, effective_date, is_active),
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_effective_date (effective_date),
  INDEX idx_is_active (is_active),
  INDEX idx_effective_rate (effective_rate),
  FOREIGN KEY (base_metal_rate_id) REFERENCES metal_rates(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 9. INVENTORY - Comprehensive inventory management
CREATE TABLE inventory (
  id VARCHAR(36) PRIMARY KEY,
  item_code VARCHAR(100) UNIQUE NOT NULL,
  barcode VARCHAR(100) UNIQUE,
  rfid_tag VARCHAR(100) UNIQUE,

  -- Basic Information
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category_id VARCHAR(36),
  subcategory_id VARCHAR(36),
  brand VARCHAR(100),
  model_number VARCHAR(100),

  -- Metal Information
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
  purity VARCHAR(20),
  hallmark_number VARCHAR(100),
  bis_number VARCHAR(100),

  -- Weight Information
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  diamond_weight DECIMAL(8,3) DEFAULT 0.000,
  other_metal_weight DECIMAL(8,3) DEFAULT 0.000,

  -- Wastage Calculation
  wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
  wastage_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight * wastage_percentage / 100) STORED,
  chargeable_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight + wastage_weight) STORED,

  -- Stone Information
  diamond_pieces INT DEFAULT 0,
  colored_stone_pieces INT DEFAULT 0,
  pearl_pieces INT DEFAULT 0,
  stone_details JSON,
  stone_certificate_number VARCHAR(100),

  -- Design Information
  size VARCHAR(50),
  gender ENUM('male', 'female', 'unisex', 'kids') DEFAULT 'unisex',
  age_group ENUM('kids', 'teen', 'adult', 'senior') DEFAULT 'adult',
  occasion VARCHAR(100),
  style VARCHAR(100),
  design_number VARCHAR(100),
  collection_name VARCHAR(100),

  -- Supplier Information
  supplier_id VARCHAR(36),
  supplier_item_code VARCHAR(100),
  purchase_date DATE,
  purchase_invoice_number VARCHAR(100),

  -- Pricing Information
  purchase_rate DECIMAL(12,2) DEFAULT 0.00,
  metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (chargeable_weight * purchase_rate) STORED,
  making_charges DECIMAL(12,2) DEFAULT 0.00,
  stone_charges DECIMAL(12,2) DEFAULT 0.00,
  other_charges DECIMAL(12,2) DEFAULT 0.00,
  total_cost DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges) STORED,

  -- Selling Price Calculation
  margin_percentage DECIMAL(5,2) DEFAULT 0.00,
  margin_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_cost * margin_percentage / 100) STORED,
  selling_price DECIMAL(12,2) DEFAULT 0.00,
  mrp DECIMAL(12,2) DEFAULT 0.00,

  -- Stock Information
  stock_quantity INT DEFAULT 1,
  reserved_quantity INT DEFAULT 0,
  available_quantity INT GENERATED ALWAYS AS (stock_quantity - reserved_quantity) STORED,
  min_stock_level INT DEFAULT 0,
  max_stock_level INT DEFAULT 100,
  reorder_level INT DEFAULT 1,

  -- Location Information
  location VARCHAR(100),
  rack_number VARCHAR(50),
  shelf_number VARCHAR(50),
  bin_location VARCHAR(50),

  -- Status Information
  status ENUM('active', 'sold', 'reserved', 'damaged', 'repair', 'lost', 'stolen', 'inactive') DEFAULT 'active',
  condition_status ENUM('new', 'excellent', 'good', 'fair', 'poor') DEFAULT 'new',

  -- Additional Information
  images JSON,
  videos JSON,
  documents JSON,
  tags JSON,
  features JSON,
  specifications JSON,

  -- Compliance Information
  hsn_code VARCHAR(20),
  country_of_origin VARCHAR(100) DEFAULT 'India',
  requires_hallmarking BOOLEAN DEFAULT FALSE,
  is_hallmarked BOOLEAN DEFAULT FALSE,
  hallmarking_date DATE,
  certification_details JSON,

  -- Business Rules
  is_customizable BOOLEAN DEFAULT FALSE,
  is_returnable BOOLEAN DEFAULT TRUE,
  return_days INT DEFAULT 7,
  warranty_period_months INT DEFAULT 0,

  -- Audit
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_item_code (item_code),
  INDEX idx_barcode (barcode),
  INDEX idx_rfid_tag (rfid_tag),
  INDEX idx_name (name),
  INDEX idx_category (category_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity),
  INDEX idx_supplier (supplier_id),
  INDEX idx_status (status),
  INDEX idx_location (location),
  INDEX idx_selling_price (selling_price),
  INDEX idx_stock_quantity (stock_quantity),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (subcategory_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 10. SALES - Comprehensive sales management
CREATE TABLE sales (
  id VARCHAR(36) PRIMARY KEY,
  invoice_number VARCHAR(100) UNIQUE NOT NULL,
  invoice_date DATE NOT NULL,
  invoice_time TIME DEFAULT CURRENT_TIME,

  -- Customer Information
  customer_id VARCHAR(36),
  customer_name VARCHAR(255), -- Denormalized for quick access
  customer_phone VARCHAR(20),
  customer_address TEXT,

  -- Sale Type and Classification
  sale_type ENUM('cash', 'credit', 'exchange', 'scheme', 'advance', 'return') DEFAULT 'cash',
  sale_category ENUM('retail', 'wholesale', 'corporate', 'online', 'exhibition') DEFAULT 'retail',
  payment_mode ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'dd', 'mixed', 'credit') DEFAULT 'cash',

  -- Financial Calculations
  subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,

  -- Discount Information
  discount_type ENUM('percentage', 'fixed', 'scheme', 'loyalty', 'promotional') DEFAULT 'percentage',
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  discount_reason VARCHAR(255),
  discount_approved_by VARCHAR(36),

  -- Taxable Amount Calculation
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,

  -- Tax Information
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cess_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  cess_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cess_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount + cess_amount) STORED,

  -- Additional Charges
  packing_charges DECIMAL(10,2) DEFAULT 0.00,
  shipping_charges DECIMAL(10,2) DEFAULT 0.00,
  insurance_charges DECIMAL(10,2) DEFAULT 0.00,
  handling_charges DECIMAL(10,2) DEFAULT 0.00,
  other_charges DECIMAL(10,2) DEFAULT 0.00,
  total_additional_charges DECIMAL(15,2) GENERATED ALWAYS AS (packing_charges + shipping_charges + insurance_charges + handling_charges + other_charges) STORED,

  -- Round Off
  round_off DECIMAL(5,2) DEFAULT 0.00,

  -- Grand Total Calculation
  grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + total_additional_charges + round_off) STORED,

  -- Exchange Information
  exchange_deduction DECIMAL(15,2) DEFAULT 0.00,
  exchange_reference VARCHAR(255),

  -- Final Amount
  final_amount DECIMAL(15,2) GENERATED ALWAYS AS (grand_total - exchange_deduction) STORED,

  -- Payment Information
  payment_status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'paid',
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (final_amount - paid_amount) STORED,
  due_date DATE,

  -- Status Information
  status ENUM('draft', 'confirmed', 'packed', 'shipped', 'delivered', 'cancelled', 'returned') DEFAULT 'confirmed',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',

  -- Delivery Information
  delivery_type ENUM('pickup', 'home_delivery', 'courier', 'self') DEFAULT 'pickup',
  delivery_address TEXT,
  delivery_date DATE,
  delivery_time TIME,
  delivery_charges DECIMAL(10,2) DEFAULT 0.00,
  delivery_status ENUM('pending', 'in_transit', 'delivered', 'failed') DEFAULT 'pending',

  -- Staff Information
  sales_person_id VARCHAR(36),
  cashier_id VARCHAR(36),
  manager_approval_id VARCHAR(36),

  -- Additional Information
  notes TEXT,
  internal_notes TEXT,
  terms_conditions TEXT,
  special_instructions TEXT,

  -- Reference Information
  reference_number VARCHAR(100),
  po_number VARCHAR(100),
  quotation_reference VARCHAR(100),

  -- Loyalty and Rewards
  loyalty_points_earned INT DEFAULT 0,
  loyalty_points_redeemed INT DEFAULT 0,

  -- Audit Information
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_invoice_number (invoice_number),
  INDEX idx_invoice_date (invoice_date),
  INDEX idx_customer (customer_id),
  INDEX idx_sale_type (sale_type),
  INDEX idx_payment_status (payment_status),
  INDEX idx_status (status),
  INDEX idx_sales_person (sales_person_id),
  INDEX idx_final_amount (final_amount),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (sales_person_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (cashier_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (manager_approval_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (discount_approved_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 11. SALE ITEMS - Detailed sale item information
CREATE TABLE sale_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  line_number INT NOT NULL,

  -- Item Information
  inventory_id VARCHAR(36),
  item_code VARCHAR(100),
  item_name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),

  -- Metal Information
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
  purity VARCHAR(20),
  hallmark_number VARCHAR(100),

  -- Weight Information
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  diamond_weight DECIMAL(8,3) DEFAULT 0.000,

  -- Wastage Calculation
  wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
  wastage_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight * wastage_percentage / 100) STORED,
  chargeable_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight + wastage_weight) STORED,

  -- Pricing Information
  rate_per_gram DECIMAL(12,2) NOT NULL,
  metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (chargeable_weight * rate_per_gram) STORED,

  -- Charges
  making_charges DECIMAL(15,2) DEFAULT 0.00,
  making_charge_percentage DECIMAL(5,2) DEFAULT 0.00,
  stone_charges DECIMAL(15,2) DEFAULT 0.00,
  other_charges DECIMAL(15,2) DEFAULT 0.00,
  certification_charges DECIMAL(10,2) DEFAULT 0.00,

  -- Total Amount Calculation
  total_amount DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges + certification_charges) STORED,

  -- Discount Information
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,

  -- Final Amount
  final_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - discount_amount) STORED,

  -- Quantity Information
  quantity INT DEFAULT 1,
  unit_price DECIMAL(15,2) GENERATED ALWAYS AS (final_amount / quantity) STORED,

  -- Additional Information
  hsn_code VARCHAR(20),
  size VARCHAR(50),
  color VARCHAR(50),
  design_number VARCHAR(100),

  -- Status
  status ENUM('active', 'cancelled', 'returned', 'exchanged') DEFAULT 'active',

  -- Audit
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_sale_line (sale_id, line_number),
  INDEX idx_sale (sale_id),
  INDEX idx_inventory (inventory_id),
  INDEX idx_item_code (item_code),
  INDEX idx_metal_type (metal_type),
  INDEX idx_final_amount (final_amount),
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE SET NULL
);

-- 12. EXCHANGE TRANSACTIONS - Comprehensive exchange management
CREATE TABLE exchange_transactions (
  id VARCHAR(36) PRIMARY KEY,
  transaction_number VARCHAR(100) UNIQUE NOT NULL,
  transaction_date DATE NOT NULL,
  transaction_time TIME DEFAULT CURRENT_TIME,

  -- Customer Information
  customer_id VARCHAR(36),
  customer_name VARCHAR(255),
  customer_phone VARCHAR(20),

  -- Transaction Details
  total_items INT DEFAULT 0,
  total_gross_weight DECIMAL(10,3) DEFAULT 0.000,
  total_net_weight DECIMAL(10,3) DEFAULT 0.000,
  total_stone_weight DECIMAL(10,3) DEFAULT 0.000,
  total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,

  -- Payment Information
  payment_method ENUM('cash', 'bank_transfer', 'adjustment', 'account_credit', 'cheque') DEFAULT 'cash',
  payment_reference VARCHAR(100),

  -- Status Information
  status ENUM('pending', 'completed', 'cancelled', 'on_hold', 'partial') DEFAULT 'pending',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',

  -- Approval Workflow
  approval_required BOOLEAN DEFAULT FALSE,
  approved_by VARCHAR(36),
  approved_at TIMESTAMP NULL,
  approval_notes TEXT,

  -- Purchase Bill Information
  purchase_bill_generated BOOLEAN DEFAULT FALSE,
  purchase_bill_id VARCHAR(36),
  purchase_bill_number VARCHAR(100),
  purchase_bill_date DATE,
  purchase_bill_amount DECIMAL(15,2) DEFAULT 0.00,

  -- Additional Information
  notes TEXT,
  internal_notes TEXT,
  special_instructions TEXT,
  photos JSON,
  documents JSON,

  -- Reference Information
  reference_number VARCHAR(100),
  source ENUM('walk_in', 'online', 'phone', 'referral') DEFAULT 'walk_in',

  -- Staff Information
  received_by VARCHAR(36),
  evaluated_by VARCHAR(36),
  processed_by VARCHAR(36),

  -- Audit Information
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_transaction_number (transaction_number),
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_customer (customer_id),
  INDEX idx_status (status),
  INDEX idx_total_amount (total_amount),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (received_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (evaluated_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 13. EXCHANGE ITEMS - Detailed exchange item information
CREATE TABLE exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  transaction_id VARCHAR(36) NOT NULL,
  item_number INT NOT NULL,

  -- Item Description
  item_description VARCHAR(255) NOT NULL,
  item_type VARCHAR(100),
  category VARCHAR(100),

  -- Metal Information
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
  purity VARCHAR(20) NOT NULL,
  hallmark_number VARCHAR(100),
  bis_number VARCHAR(100),

  -- Weight Information
  gross_weight DECIMAL(10,3) NOT NULL,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  diamond_weight DECIMAL(8,3) DEFAULT 0.000,
  other_deductions DECIMAL(8,3) DEFAULT 0.000,

  -- Final Weight Calculation
  final_weight DECIMAL(10,3) GENERATED ALWAYS AS (net_weight - other_deductions) STORED,

  -- Pricing Information
  rate_per_gram DECIMAL(12,2) NOT NULL,
  amount DECIMAL(15,2) GENERATED ALWAYS AS (final_weight * rate_per_gram) STORED,

  -- Condition Assessment
  item_condition ENUM('excellent', 'very_good', 'good', 'fair', 'poor') DEFAULT 'good',
  condition_notes TEXT,

  -- Quality Assessment
  hallmark_available BOOLEAN DEFAULT FALSE,
  hallmark_verified BOOLEAN DEFAULT FALSE,
  purity_verified BOOLEAN DEFAULT FALSE,
  purity_test_method ENUM('touchstone', 'electronic', 'xrf', 'fire_assay') DEFAULT 'touchstone',
  actual_purity VARCHAR(20),

  -- Certification Information
  certificate_available BOOLEAN DEFAULT FALSE,
  certificate_number VARCHAR(100),
  certificate_issuer VARCHAR(100),
  certificate_date DATE,

  -- Physical Characteristics
  size VARCHAR(50),
  color VARCHAR(50),
  design_type VARCHAR(100),
  age_estimate VARCHAR(50),
  origin_details VARCHAR(255),

  -- Damage Assessment
  has_damage BOOLEAN DEFAULT FALSE,
  damage_description TEXT,
  damage_impact_percentage DECIMAL(5,2) DEFAULT 0.00,
  repair_required BOOLEAN DEFAULT FALSE,
  repair_cost_estimate DECIMAL(10,2) DEFAULT 0.00,

  -- Additional Information
  photos JSON,
  notes TEXT,
  special_features TEXT,

  -- Status
  status ENUM('pending', 'evaluated', 'approved', 'rejected', 'on_hold') DEFAULT 'pending',
  rejection_reason TEXT,

  -- Audit Information
  evaluated_by VARCHAR(36),
  evaluated_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_transaction_item (transaction_id, item_number),
  INDEX idx_transaction (transaction_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity),
  INDEX idx_item_condition (item_condition),
  INDEX idx_amount (amount),
  INDEX idx_status (status),
  FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (evaluated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 14. EXCHANGE PURCHASE BILLS - Professional purchase bill generation
CREATE TABLE exchange_purchase_bills (
  id VARCHAR(36) PRIMARY KEY,
  bill_number VARCHAR(100) UNIQUE NOT NULL,
  bill_series VARCHAR(20) DEFAULT 'EPB',
  financial_year VARCHAR(20) NOT NULL,

  -- Reference Information
  exchange_transaction_id VARCHAR(36) NOT NULL,
  customer_id VARCHAR(36),

  -- Bill Details
  bill_date DATE NOT NULL,
  bill_time TIME DEFAULT CURRENT_TIME,
  due_date DATE,

  -- Customer Information (Denormalized)
  customer_name VARCHAR(255),
  customer_address TEXT,
  customer_phone VARCHAR(20),
  customer_gst_number VARCHAR(50),
  customer_pan_number VARCHAR(20),

  -- Financial Calculations
  subtotal DECIMAL(15,2) NOT NULL,

  -- Discount Information
  discount_type ENUM('percentage', 'fixed', 'promotional') DEFAULT 'percentage',
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  discount_reason VARCHAR(255),

  -- Taxable Amount
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,

  -- Tax Calculations
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cess_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  cess_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cess_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount + cess_amount) STORED,

  -- Additional Charges
  handling_charges DECIMAL(10,2) DEFAULT 0.00,
  documentation_charges DECIMAL(10,2) DEFAULT 0.00,
  other_charges DECIMAL(10,2) DEFAULT 0.00,

  -- Round Off
  round_off DECIMAL(5,2) DEFAULT 0.00,

  -- Total Calculation
  total_with_tax DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + handling_charges + documentation_charges + other_charges + round_off) STORED,

  -- Payment Information
  payment_method ENUM('cash', 'bank_transfer', 'cheque', 'account_credit', 'adjustment') DEFAULT 'cash',
  payment_status ENUM('pending', 'paid', 'partial', 'overdue') DEFAULT 'paid',
  payment_reference VARCHAR(100),
  payment_date DATE,

  -- Additional Information
  notes TEXT,
  terms_conditions TEXT,

  -- Status Information
  status ENUM('draft', 'generated', 'sent', 'paid', 'cancelled') DEFAULT 'generated',

  -- Staff Information
  generated_by VARCHAR(36),
  approved_by VARCHAR(36),

  -- Audit Information
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_bill_number (bill_number),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_customer (customer_id),
  INDEX idx_bill_date (bill_date),
  INDEX idx_payment_status (payment_status),
  INDEX idx_status (status),
  INDEX idx_total_with_tax (total_with_tax),
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE RESTRICT,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 15. SALES EXCHANGE ITEMS - Sales-Exchange integration
CREATE TABLE sales_exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  sale_item_id VARCHAR(36),
  exchange_transaction_id VARCHAR(36) NOT NULL,
  exchange_item_id VARCHAR(36) NOT NULL,

  -- Deduction Information
  deduction_amount DECIMAL(15,2) NOT NULL,
  applied_rate DECIMAL(12,2) NOT NULL,
  exchange_percentage DECIMAL(5,2) DEFAULT 100.00,

  -- Additional Information
  notes TEXT,
  applied_date DATE DEFAULT (CURDATE()),

  -- Staff Information
  applied_by VARCHAR(36),

  -- Audit Information
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_sale (sale_id),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_exchange_item (exchange_item_id),
  INDEX idx_deduction_amount (deduction_amount),
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (sale_item_id) REFERENCES sale_items(id) ON DELETE SET NULL,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE RESTRICT,
  FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) ON DELETE RESTRICT,
  FOREIGN KEY (applied_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 16. PURCHASES - Comprehensive purchase management
CREATE TABLE purchases (
  id VARCHAR(36) PRIMARY KEY,
  purchase_number VARCHAR(100) UNIQUE NOT NULL,
  purchase_date DATE NOT NULL,
  purchase_time TIME DEFAULT CURRENT_TIME,

  -- Supplier Information
  supplier_id VARCHAR(36) NOT NULL,
  supplier_name VARCHAR(255),
  supplier_invoice_number VARCHAR(100),
  supplier_invoice_date DATE,

  -- Purchase Details
  purchase_type ENUM('inventory', 'raw_material', 'consumables', 'services') DEFAULT 'inventory',
  purchase_category ENUM('regular', 'urgent', 'bulk', 'special_order') DEFAULT 'regular',

  -- Financial Information
  subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,

  -- Tax Information
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount) STORED,

  -- Additional Charges
  freight_charges DECIMAL(10,2) DEFAULT 0.00,
  insurance_charges DECIMAL(10,2) DEFAULT 0.00,
  handling_charges DECIMAL(10,2) DEFAULT 0.00,
  other_charges DECIMAL(10,2) DEFAULT 0.00,

  -- Total Calculation
  grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + freight_charges + insurance_charges + handling_charges + other_charges) STORED,

  -- Payment Information
  payment_method ENUM('cash', 'bank_transfer', 'cheque', 'credit', 'adjustment') DEFAULT 'bank_transfer',
  payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (grand_total - paid_amount) STORED,
  due_date DATE,

  -- Status Information
  status ENUM('draft', 'ordered', 'received', 'partial_received', 'completed', 'cancelled') DEFAULT 'ordered',

  -- Delivery Information
  expected_delivery_date DATE,
  actual_delivery_date DATE,
  delivery_address TEXT,

  -- Additional Information
  notes TEXT,
  terms_conditions TEXT,

  -- Staff Information
  ordered_by VARCHAR(36),
  received_by VARCHAR(36),
  approved_by VARCHAR(36),

  -- Audit Information
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_purchase_number (purchase_number),
  INDEX idx_purchase_date (purchase_date),
  INDEX idx_supplier (supplier_id),
  INDEX idx_status (status),
  INDEX idx_payment_status (payment_status),
  INDEX idx_grand_total (grand_total),
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT,
  FOREIGN KEY (ordered_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (received_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 17. PURCHASE ITEMS - Detailed purchase item information
CREATE TABLE purchase_items (
  id VARCHAR(36) PRIMARY KEY,
  purchase_id VARCHAR(36) NOT NULL,
  line_number INT NOT NULL,

  -- Item Information
  item_description VARCHAR(255) NOT NULL,
  item_code VARCHAR(100),
  category VARCHAR(100),

  -- Metal Information
  metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other'),
  purity VARCHAR(20),

  -- Quantity and Weight
  quantity INT DEFAULT 1,
  unit_of_measure ENUM('pieces', 'grams', 'kilograms', 'tola', 'ounce') DEFAULT 'pieces',
  weight_per_unit DECIMAL(10,3) DEFAULT 0.000,
  total_weight DECIMAL(10,3) GENERATED ALWAYS AS (quantity * weight_per_unit) STORED,

  -- Pricing Information
  rate_per_unit DECIMAL(12,2) NOT NULL,
  rate_per_gram DECIMAL(12,2) DEFAULT 0.00,
  amount DECIMAL(15,2) GENERATED ALWAYS AS (quantity * rate_per_unit) STORED,

  -- Discount Information
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  final_amount DECIMAL(15,2) GENERATED ALWAYS AS (amount - discount_amount) STORED,

  -- Additional Information
  hsn_code VARCHAR(20),
  specifications TEXT,

  -- Status Information
  status ENUM('ordered', 'received', 'partial_received', 'cancelled') DEFAULT 'ordered',
  received_quantity INT DEFAULT 0,
  pending_quantity INT GENERATED ALWAYS AS (quantity - received_quantity) STORED,

  -- Quality Information
  quality_check_required BOOLEAN DEFAULT FALSE,
  quality_check_status ENUM('pending', 'passed', 'failed', 'conditional') DEFAULT 'pending',
  quality_notes TEXT,

  -- Audit Information
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_purchase_line (purchase_id, line_number),
  INDEX idx_purchase (purchase_id),
  INDEX idx_item_code (item_code),
  INDEX idx_metal_type (metal_type),
  INDEX idx_status (status),
  INDEX idx_final_amount (final_amount),
  FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE
);

-- 18. BILL SEQUENCES - Professional bill numbering
CREATE TABLE bill_sequences (
  id VARCHAR(36) PRIMARY KEY,
  sequence_type VARCHAR(100) NOT NULL,
  sequence_name VARCHAR(255) NOT NULL,
  prefix VARCHAR(20) NOT NULL,
  suffix VARCHAR(20) DEFAULT '',
  current_number INT NOT NULL DEFAULT 0,
  start_number INT DEFAULT 1,
  increment_by INT DEFAULT 1,
  pad_length INT DEFAULT 4,
  financial_year VARCHAR(20) NOT NULL,
  format_pattern VARCHAR(100) DEFAULT '{prefix}/{year}/{number:04d}',

  -- Configuration
  reset_annually BOOLEAN DEFAULT TRUE,
  reset_monthly BOOLEAN DEFAULT FALSE,
  include_month BOOLEAN DEFAULT FALSE,
  include_day BOOLEAN DEFAULT FALSE,

  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  last_used_date DATE,

  -- Audit
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_sequence (sequence_type, financial_year),
  INDEX idx_sequence_type (sequence_type),
  INDEX idx_financial_year (financial_year),
  INDEX idx_is_active (is_active)
);

-- Continue with audit and business workflow tables...
