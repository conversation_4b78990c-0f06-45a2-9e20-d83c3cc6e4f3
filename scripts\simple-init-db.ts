#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'

async function initializeDatabase() {
  console.log('🚀 Initializing database...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  console.log('Database config:')
  console.log('Host:', dbConfig.host)
  console.log('User:', dbConfig.user)
  console.log('Database:', dbConfig.database)
  console.log()

  try {
    // Connect to MySQL server
    console.log('1. Connecting to MySQL server...')
    const connection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
    })
    console.log('✅ Connected to MySQL server')

    // Create database if it doesn't exist
    console.log('2. Creating database if not exists...')
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' ready`)

    // Close the server connection and connect directly to the database
    await connection.end()

    console.log('3. Connecting to the specific database...')
    const dbConnection = await mysql.createConnection(dbConfig)
    console.log(`✅ Connected to database '${dbConfig.database}'`)

    // Read and execute schema
    console.log('4. Creating tables...')
    const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql')
    const schemaSql = fs.readFileSync(schemaPath, 'utf8')

    // Remove comments and split SQL statements
    const cleanSql = schemaSql
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim() !== '')
      .join('\n')

    const statements = cleanSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('CREATE DATABASE') && !stmt.startsWith('USE'))

    console.log(`Found ${statements.length} SQL statements`)

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement) {
        try {
          console.log(`Executing statement ${i + 1}: ${statement.substring(0, 50)}...`)
          await dbConnection.execute(statement)
          console.log(`✅ Statement ${i + 1} executed successfully`)
        } catch (error: any) {
          if (!error.message.includes('already exists')) {
            console.warn(`Warning executing statement ${i + 1}:`, error.message)
          } else {
            console.log(`✅ Statement ${i + 1} already exists (skipped)`)
          }
        }
      }
    }
    console.log('✅ All tables processed')

    // Insert default users
    console.log('5. Creating default users...')

    // Check if users already exist
    const [existingUsers] = await dbConnection.execute('SELECT COUNT(*) as count FROM users')
    const userCount = (existingUsers as any[])[0].count
    
    if (userCount === 0) {
      const bcrypt = (await import('bcryptjs')).default
      
      const users = [
        {
          id: crypto.randomUUID(),
          name: 'Admin User',
          email: '<EMAIL>',
          password_hash: await bcrypt.hash('admin123', 10),
          role: 'admin',
          permissions: JSON.stringify([
            { module: "inventory", actions: ["create", "read", "update", "delete", "export"] },
            { module: "customers", actions: ["create", "read", "update", "delete", "export"] },
            { module: "sales", actions: ["create", "read", "update", "delete", "export"] },
            { module: "purchases", actions: ["create", "read", "update", "delete", "export"] },
            { module: "schemes", actions: ["create", "read", "update", "delete", "export"] },
            { module: "repairs", actions: ["create", "read", "update", "delete", "export"] },
            { module: "reports", actions: ["read", "export"] },
            { module: "settings", actions: ["read", "update"] },
          ])
        },
        {
          id: crypto.randomUUID(),
          name: 'Manager User',
          email: '<EMAIL>',
          password_hash: await bcrypt.hash('manager123', 10),
          role: 'manager',
          permissions: JSON.stringify([
            { module: "inventory", actions: ["create", "read", "update", "export"] },
            { module: "customers", actions: ["create", "read", "update", "export"] },
            { module: "sales", actions: ["create", "read", "update", "export"] },
            { module: "schemes", actions: ["create", "read", "update"] },
            { module: "repairs", actions: ["create", "read", "update"] },
            { module: "reports", actions: ["read", "export"] },
          ])
        }
      ]

      for (const user of users) {
        await dbConnection.execute(
          'INSERT INTO users (id, name, email, password_hash, role, permissions, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
          [user.id, user.name, user.email, user.password_hash, user.role, user.permissions]
        )
        console.log(`✅ Created user: ${user.email}`)
      }
    } else {
      console.log('✅ Users already exist, skipping user creation')
    }

    // Insert default settings
    console.log('6. Creating default settings...')
    const [existingSettings] = await dbConnection.execute('SELECT COUNT(*) as count FROM settings')
    const settingsCount = (existingSettings as any[])[0].count
    
    if (settingsCount === 0) {
      const metalRates = JSON.stringify({
        gold: { "24K": "700", "22K": "642", "18K": "525", "14K": "408" },
        silver: { "999": "85", "925": "78.5" }
      })
      
      await dbConnection.execute(`
        INSERT INTO settings (
          business_name, address, phone, email, gst_number, metal_rates,
          auto_update_rates, cgst_rate, sgst_rate, low_stock_alert, low_stock_threshold,
          scheme_reminders, repair_reminders, currency, date_format, backup_frequency,
          invoice_template, print_logo, print_terms, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        'Shree Jewellers', '123 Main Street, Mumbai', '+91 98765 43210',
        '<EMAIL>', '27ABCDE1234F1Z5', metalRates,
        true, 1.5, 1.5, true, 5, true, true, 'INR', 'DD/MM/YYYY',
        'daily', 'standard', true, true
      ])
      console.log('✅ Default settings created')
    } else {
      console.log('✅ Settings already exist, skipping settings creation')
    }

    await dbConnection.end()
    
    console.log('\n🎉 Database initialization completed successfully!')
    console.log('\nDefault login credentials:')
    console.log('Admin: <EMAIL> / admin123')
    console.log('Manager: <EMAIL> / manager123')
    
    return true
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    return false
  }
}

async function main() {
  const success = await initializeDatabase()
  process.exit(success ? 0 : 1)
}

main()
