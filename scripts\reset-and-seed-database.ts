#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function resetAndSeedDatabase() {
  console.log('🔄 Resetting Database and Seeding Fresh Sample Data...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Clean existing exchange data
    console.log('🧹 Step 1: Cleaning existing exchange data...')
    
    // Disable foreign key checks temporarily for cleanup
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    
    // Drop exchange tables in correct order
    const exchangeTables = [
      'exchange_audit_trail',
      'sales_exchange_items', 
      'exchange_purchase_bills',
      'exchange_items',
      'exchange_transactions',
      'exchange_rates',
      'bill_sequences'
    ]

    for (const table of exchangeTables) {
      try {
        await connection.execute(`DROP TABLE IF EXISTS ${table}`)
        console.log(`   ✅ Dropped table: ${table}`)
      } catch (error) {
        console.log(`   ⚠️  Could not drop ${table}: ${error}`)
      }
    }

    // Re-enable foreign key checks
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
    console.log('✅ Database cleaned\n')

    // Step 2: Create fresh exchange tables with all constraints
    console.log('🏗️  Step 2: Creating fresh exchange tables...')
    
    // Create exchange_rates table
    await connection.execute(`
      CREATE TABLE exchange_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver') NOT NULL,
        purity VARCHAR(10) NOT NULL,
        rate_per_gram DECIMAL(10, 2) NOT NULL,
        effective_date DATE NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_active_rate (metal_type, purity, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date),
        CONSTRAINT chk_exchange_rates_positive_rate CHECK (rate_per_gram > 0),
        CONSTRAINT chk_exchange_rates_valid_purity CHECK (
          (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
          (metal_type = 'silver' AND purity IN ('999', '925', '900', '800'))
        ),
        CONSTRAINT chk_exchange_rates_reasonable_rates CHECK (
          (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 20000) OR
          (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 500)
        )
      )
    `)
    console.log('   ✅ Created exchange_rates table')

    // Create exchange_transactions table
    await connection.execute(`
      CREATE TABLE exchange_transactions (
        id VARCHAR(36) PRIMARY KEY,
        transaction_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id VARCHAR(36),
        transaction_date DATE NOT NULL,
        total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
        notes TEXT,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        purchase_bill_generated BOOLEAN DEFAULT FALSE,
        purchase_bill_id VARCHAR(36),
        purchase_bill_number VARCHAR(50),
        purchase_bill_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer (customer_id),
        INDEX idx_transaction_date (transaction_date),
        INDEX idx_status (status),
        INDEX idx_transaction_number (transaction_number),
        CONSTRAINT chk_exchange_transactions_positive_amount CHECK (total_amount >= 0),
        CONSTRAINT chk_exchange_transactions_valid_date CHECK (
          transaction_date >= '2020-01-01' AND 
          transaction_date <= CURDATE() + INTERVAL 1 DAY
        ),
        CONSTRAINT fk_exchange_transactions_customer 
          FOREIGN KEY (customer_id) REFERENCES customers(id) 
          ON DELETE RESTRICT ON UPDATE CASCADE
      )
    `)
    console.log('   ✅ Created exchange_transactions table')

    // Create exchange_items table
    await connection.execute(`
      CREATE TABLE exchange_items (
        id VARCHAR(36) PRIMARY KEY,
        transaction_id VARCHAR(36) NOT NULL,
        item_description VARCHAR(255) NOT NULL,
        metal_type ENUM('gold', 'silver') NOT NULL,
        purity VARCHAR(10) NOT NULL,
        gross_weight DECIMAL(8, 3) NOT NULL,
        stone_weight DECIMAL(8, 3) DEFAULT 0.000,
        net_weight DECIMAL(8, 3) NOT NULL,
        rate_per_gram DECIMAL(10, 2) NOT NULL,
        amount DECIMAL(12, 2) NOT NULL,
        item_condition ENUM('good', 'fair', 'poor') DEFAULT 'good',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_transaction (transaction_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity (purity),
        CONSTRAINT chk_exchange_items_positive_weights CHECK (
          gross_weight >= 0 AND stone_weight >= 0 AND net_weight >= 0
        ),
        CONSTRAINT chk_exchange_items_positive_rate CHECK (rate_per_gram > 0),
        CONSTRAINT chk_exchange_items_positive_amount CHECK (amount >= 0),
        CONSTRAINT chk_exchange_items_weight_logic CHECK (net_weight <= gross_weight),
        CONSTRAINT chk_exchange_items_valid_purity CHECK (
          (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
          (metal_type = 'silver' AND purity IN ('999', '925', '900', '800'))
        ),
        CONSTRAINT chk_exchange_items_reasonable_rates CHECK (
          (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 20000) OR
          (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 500)
        ),
        CONSTRAINT fk_exchange_items_transaction 
          FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) 
          ON DELETE CASCADE ON UPDATE CASCADE
      )
    `)
    console.log('   ✅ Created exchange_items table')

    // Create bill_sequences table
    await connection.execute(`
      CREATE TABLE bill_sequences (
        id VARCHAR(36) PRIMARY KEY,
        sequence_type VARCHAR(50) NOT NULL,
        prefix VARCHAR(10) NOT NULL,
        current_number INT NOT NULL DEFAULT 0,
        financial_year VARCHAR(10) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_sequence (sequence_type, financial_year),
        CONSTRAINT chk_bill_sequences_positive_number CHECK (current_number >= 0)
      )
    `)
    console.log('   ✅ Created bill_sequences table')

    // Create exchange_purchase_bills table
    await connection.execute(`
      CREATE TABLE exchange_purchase_bills (
        id VARCHAR(36) PRIMARY KEY,
        bill_number VARCHAR(50) UNIQUE NOT NULL,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        customer_id VARCHAR(36),
        bill_date DATE NOT NULL,
        total_amount DECIMAL(12, 2) NOT NULL,
        cgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        sgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        total_with_tax DECIMAL(12, 2) NOT NULL,
        payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
        payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_bill_number (bill_number),
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_customer (customer_id),
        INDEX idx_bill_date (bill_date),
        INDEX idx_payment_status (payment_status),
        CONSTRAINT chk_purchase_bills_positive_amounts CHECK (
          total_amount >= 0 AND cgst_amount >= 0 AND sgst_amount >= 0 AND total_with_tax >= 0
        ),
        CONSTRAINT chk_purchase_bills_tax_logic CHECK (total_with_tax >= total_amount),
        CONSTRAINT chk_purchase_bills_valid_date CHECK (
          bill_date >= '2020-01-01' AND 
          bill_date <= CURDATE() + INTERVAL 1 DAY
        ),
        CONSTRAINT fk_purchase_bills_transaction 
          FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
          ON DELETE RESTRICT ON UPDATE CASCADE,
        CONSTRAINT fk_purchase_bills_customer 
          FOREIGN KEY (customer_id) REFERENCES customers(id) 
          ON DELETE RESTRICT ON UPDATE CASCADE
      )
    `)
    console.log('   ✅ Created exchange_purchase_bills table')

    // Create sales_exchange_items table
    await connection.execute(`
      CREATE TABLE sales_exchange_items (
        id VARCHAR(36) PRIMARY KEY,
        sale_id VARCHAR(36) NOT NULL,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        exchange_item_id VARCHAR(36) NOT NULL,
        deduction_amount DECIMAL(12, 2) NOT NULL,
        applied_rate DECIMAL(10, 2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_sale (sale_id),
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_exchange_item (exchange_item_id),
        CONSTRAINT chk_sales_exchange_positive_deduction CHECK (deduction_amount >= 0),
        CONSTRAINT chk_sales_exchange_positive_rate CHECK (applied_rate > 0),
        CONSTRAINT fk_sales_exchange_sale 
          FOREIGN KEY (sale_id) REFERENCES sales(id) 
          ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_sales_exchange_transaction 
          FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
          ON DELETE RESTRICT ON UPDATE CASCADE,
        CONSTRAINT fk_sales_exchange_item 
          FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) 
          ON DELETE RESTRICT ON UPDATE CASCADE
      )
    `)
    console.log('   ✅ Created sales_exchange_items table')

    // Create exchange_audit_trail table
    await connection.execute(`
      CREATE TABLE exchange_audit_trail (
        id VARCHAR(36) PRIMARY KEY,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        action_type ENUM('created', 'updated', 'billed', 'voucher_generated', 'used_in_sale', 'cancelled') NOT NULL,
        action_description TEXT NOT NULL,
        old_values JSON,
        new_values JSON,
        related_bill_id VARCHAR(36),
        related_sale_id VARCHAR(36),
        performed_by VARCHAR(36),
        performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_action_type (action_type),
        INDEX idx_performed_at (performed_at),
        INDEX idx_performed_by (performed_by),
        CONSTRAINT fk_audit_trail_transaction 
          FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
          ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_audit_trail_bill 
          FOREIGN KEY (related_bill_id) REFERENCES exchange_purchase_bills(id) 
          ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_audit_trail_sale 
          FOREIGN KEY (related_sale_id) REFERENCES sales(id) 
          ON DELETE SET NULL ON UPDATE CASCADE
      )
    `)
    console.log('   ✅ Created exchange_audit_trail table')

    // Add foreign key constraint back to exchange_transactions
    await connection.execute(`
      ALTER TABLE exchange_transactions 
      ADD CONSTRAINT fk_exchange_purchase_bill 
      FOREIGN KEY (purchase_bill_id) REFERENCES exchange_purchase_bills(id) 
      ON DELETE SET NULL ON UPDATE CASCADE
    `)
    console.log('   ✅ Added purchase bill reference constraint')

    console.log('✅ All exchange tables created with validation constraints\n')

    // Step 3: Seed fresh sample data
    console.log('🌱 Step 3: Seeding fresh sample data...')
    await seedFreshSampleData(connection)

    console.log('\n🎉 Database Reset and Seeding Completed Successfully!')

    // Step 4: Verify the setup
    console.log('\n🔍 Step 4: Verifying setup...')
    await verifyDatabaseSetup(connection)

    console.log('\n📊 FRESH DATABASE STATUS:')
    console.log('=' .repeat(50))
    console.log('✅ All exchange tables recreated with validation')
    console.log('✅ Fresh sample data seeded')
    console.log('✅ All constraints active and tested')
    console.log('✅ Data integrity verified')
    console.log('✅ System ready for testing')
    console.log('=' .repeat(50))

  } catch (error) {
    console.error('\n❌ Database reset failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

async function seedFreshSampleData(connection: mysql.Connection) {
  // Insert current exchange rates
  console.log('💰 Inserting current exchange rates...')
  const rates = [
    { id: randomUUID(), metal_type: 'gold', purity: '24K', rate_per_gram: 7200.00 },
    { id: randomUUID(), metal_type: 'gold', purity: '22K', rate_per_gram: 6600.00 },
    { id: randomUUID(), metal_type: 'gold', purity: '18K', rate_per_gram: 5400.00 },
    { id: randomUUID(), metal_type: 'gold', purity: '14K', rate_per_gram: 4200.00 },
    { id: randomUUID(), metal_type: 'silver', purity: '999', rate_per_gram: 90.00 },
    { id: randomUUID(), metal_type: 'silver', purity: '925', rate_per_gram: 83.00 },
    { id: randomUUID(), metal_type: 'silver', purity: '900', rate_per_gram: 81.00 }
  ]

  for (const rate of rates) {
    await connection.execute(`
      INSERT INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active) 
      VALUES (?, ?, ?, ?, CURDATE(), TRUE)
    `, [rate.id, rate.metal_type, rate.purity, rate.rate_per_gram])
  }
  console.log(`   ✅ Inserted ${rates.length} exchange rates`)

  // Initialize bill sequence
  console.log('🔢 Initializing bill sequence...')
  const currentYear = new Date().getFullYear()
  const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
  
  await connection.execute(`
    INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) 
    VALUES (?, 'exchange_purchase', 'EPB', 0, ?)
  `, [randomUUID(), financialYear])
  console.log('   ✅ Bill sequence initialized')

  // Insert comprehensive sample customers (if not exists)
  console.log('👥 Ensuring sample customers exist...')
  const customers = [
    { id: 'cust_001', name: 'Rajesh Kumar', phone: '9876543210', email: '<EMAIL>', address: '123 MG Road, Mumbai, Maharashtra 400001' },
    { id: 'cust_002', name: 'Priya Sharma', phone: '9876543211', email: '<EMAIL>', address: '456 Brigade Road, Bangalore, Karnataka 560001' },
    { id: 'cust_003', name: 'Amit Patel', phone: '9876543212', email: '<EMAIL>', address: '789 CG Road, Ahmedabad, Gujarat 380001' },
    { id: 'cust_004', name: 'Sunita Reddy', phone: '9876543213', email: '<EMAIL>', address: '321 Jubilee Hills, Hyderabad, Telangana 500001' },
    { id: 'cust_005', name: 'Vikram Singh', phone: '9876543214', email: '<EMAIL>', address: '654 Connaught Place, New Delhi, Delhi 110001' },
    { id: 'cust_006', name: 'Meera Joshi', phone: '9876543215', email: '<EMAIL>', address: '987 FC Road, Pune, Maharashtra 411005' }
  ]

  for (const customer of customers) {
    await connection.execute(`
      INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, 200000.00, CURDATE(), NOW(), NOW())
    `, [customer.id, customer.name, customer.phone, customer.email, customer.address])
  }
  console.log(`   ✅ Ensured ${customers.length} sample customers exist`)

  // Insert diverse exchange transactions
  console.log('🔄 Creating diverse exchange transactions...')
  const transactions = [
    {
      id: 'exg_001',
      transaction_number: 'EXG-20250131-001',
      customer_id: 'cust_001',
      transaction_date: '2025-01-31',
      status: 'completed',
      notes: 'High-value gold bar exchange - excellent condition'
    },
    {
      id: 'exg_002', 
      transaction_number: 'EXG-20250130-001',
      customer_id: 'cust_002',
      transaction_date: '2025-01-30',
      status: 'completed',
      notes: 'Silver jewelry set exchange - traditional design'
    },
    {
      id: 'exg_003',
      transaction_number: 'EXG-20250129-001', 
      customer_id: 'cust_003',
      transaction_date: '2025-01-29',
      status: 'completed',
      notes: 'Mixed gold jewelry exchange - various purities'
    },
    {
      id: 'exg_004',
      transaction_number: 'EXG-20250128-001',
      customer_id: 'cust_004', 
      transaction_date: '2025-01-28',
      status: 'pending',
      notes: 'Pending evaluation - customer to return tomorrow'
    },
    {
      id: 'exg_005',
      transaction_number: 'EXG-20250127-001',
      customer_id: 'cust_005',
      transaction_date: '2025-01-27', 
      status: 'completed',
      notes: 'Antique gold necklace - heritage piece'
    },
    {
      id: 'exg_006',
      transaction_number: 'EXG-20250126-001',
      customer_id: 'cust_006',
      transaction_date: '2025-01-26',
      status: 'completed',
      notes: 'Modern silver bangles set - contemporary design'
    }
  ]

  for (const transaction of transactions) {
    await connection.execute(`
      INSERT INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, created_at, updated_at) 
      VALUES (?, ?, ?, ?, 0.00, 'cash', ?, ?, NOW(), NOW())
    `, [transaction.id, transaction.transaction_number, transaction.customer_id, transaction.transaction_date, transaction.notes, transaction.status])
  }
  console.log(`   ✅ Created ${transactions.length} exchange transactions`)

  // Insert diverse exchange items with realistic data
  console.log('💎 Adding diverse exchange items...')
  const items = [
    // Transaction 1: High-value gold bar
    { id: randomUUID(), transaction_id: 'exg_001', description: 'GOLD BAR 24K PURE', metal_type: 'gold', purity: '24K', gross_weight: 50.000, stone_weight: 0.000, net_weight: 50.000, rate_per_gram: 7200.00, amount: 360000.00, condition: 'good' },
    
    // Transaction 2: Silver jewelry set
    { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER BANGLES SET TRADITIONAL', metal_type: 'silver', purity: '925', gross_weight: 180.000, stone_weight: 0.000, net_weight: 180.000, rate_per_gram: 83.00, amount: 14940.00, condition: 'good' },
    { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER ANKLETS PAIR', metal_type: 'silver', purity: '925', gross_weight: 120.000, stone_weight: 0.000, net_weight: 120.000, rate_per_gram: 83.00, amount: 9960.00, condition: 'fair' },
    
    // Transaction 3: Mixed gold jewelry
    { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD CHAIN 22K HEAVY', metal_type: 'gold', purity: '22K', gross_weight: 25.000, stone_weight: 0.000, net_weight: 25.000, rate_per_gram: 6600.00, amount: 165000.00, condition: 'good' },
    { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD EARRINGS 18K DESIGNER', metal_type: 'gold', purity: '18K', gross_weight: 12.000, stone_weight: 4.000, net_weight: 8.000, rate_per_gram: 5400.00, amount: 43200.00, condition: 'good' },
    { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD RING 14K WEDDING', metal_type: 'gold', purity: '14K', gross_weight: 8.000, stone_weight: 2.000, net_weight: 6.000, rate_per_gram: 4200.00, amount: 25200.00, condition: 'fair' },
    
    // Transaction 4: Pending evaluation
    { id: randomUUID(), transaction_id: 'exg_004', description: 'GOLD NECKLACE SET EVALUATION PENDING', metal_type: 'gold', purity: '22K', gross_weight: 35.000, stone_weight: 8.000, net_weight: 27.000, rate_per_gram: 6600.00, amount: 178200.00, condition: 'good' },
    
    // Transaction 5: Antique piece
    { id: randomUUID(), transaction_id: 'exg_005', description: 'ANTIQUE GOLD NECKLACE HERITAGE', metal_type: 'gold', purity: '22K', gross_weight: 45.000, stone_weight: 12.000, net_weight: 33.000, rate_per_gram: 6600.00, amount: 217800.00, condition: 'good' },
    
    // Transaction 6: Modern silver
    { id: randomUUID(), transaction_id: 'exg_006', description: 'MODERN SILVER BANGLES CONTEMPORARY', metal_type: 'silver', purity: '925', gross_weight: 150.000, stone_weight: 0.000, net_weight: 150.000, rate_per_gram: 83.00, amount: 12450.00, condition: 'good' }
  ]

  for (const item of items) {
    await connection.execute(`
      INSERT INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Fresh sample data', NOW(), NOW())
    `, [item.id, item.transaction_id, item.description, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.rate_per_gram, item.amount, item.condition])
  }
  console.log(`   ✅ Added ${items.length} diverse exchange items`)

  // Update transaction totals
  console.log('🔢 Updating transaction totals...')
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 360000.00 WHERE id = 'exg_001'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 24900.00 WHERE id = 'exg_002'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 233400.00 WHERE id = 'exg_003'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 178200.00 WHERE id = 'exg_004'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 217800.00 WHERE id = 'exg_005'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 12450.00 WHERE id = 'exg_006'`)
  console.log('   ✅ Updated all transaction totals')

  // Generate purchase bills for completed transactions
  console.log('📄 Generating purchase bills for completed transactions...')
  const completedTransactions = ['exg_001', 'exg_002', 'exg_003', 'exg_005', 'exg_006']
  const amounts = [360000.00, 24900.00, 233400.00, 217800.00, 12450.00]
  
  for (let i = 0; i < completedTransactions.length; i++) {
    const billId = randomUUID()
    const billNumber = `EPB/${financialYear}/${String(i + 1).padStart(4, '0')}`
    const totalAmount = amounts[i]
    const cgstAmount = (totalAmount * 1.5) / 100
    const sgstAmount = (totalAmount * 1.5) / 100
    const totalWithTax = totalAmount + cgstAmount + sgstAmount
    
    await connection.execute(`
      INSERT INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) 
      VALUES (?, ?, ?, (SELECT customer_id FROM exchange_transactions WHERE id = ?), CURDATE(), ?, ?, ?, ?, 'cash', 'paid', 'Fresh sample bill', NOW(), NOW())
    `, [billId, billNumber, completedTransactions[i], completedTransactions[i], totalAmount, cgstAmount, sgstAmount, totalWithTax])
    
    // Update transaction with bill reference
    await connection.execute(`
      UPDATE exchange_transactions 
      SET purchase_bill_generated = TRUE, purchase_bill_id = ?, purchase_bill_number = ?, purchase_bill_date = CURDATE() 
      WHERE id = ?
    `, [billId, billNumber, completedTransactions[i]])
  }
  
  // Update bill sequence
  await connection.execute(`UPDATE bill_sequences SET current_number = ? WHERE sequence_type = 'exchange_purchase'`, [completedTransactions.length])
  console.log(`   ✅ Generated ${completedTransactions.length} purchase bills`)

  // Create comprehensive audit trail
  console.log('📋 Creating comprehensive audit trail...')
  const auditEntries = [
    { id: randomUUID(), transaction_id: 'exg_001', action: 'created', description: 'High-value gold bar exchange transaction created', values: '{"totalAmount": 360000, "items": 1}' },
    { id: randomUUID(), transaction_id: 'exg_001', action: 'billed', description: 'Purchase bill EPB/2025-26/0001 generated', values: '{"billNumber": "EPB/2025-26/0001", "totalWithTax": 370800}' },
    { id: randomUUID(), transaction_id: 'exg_002', action: 'created', description: 'Silver jewelry set exchange created', values: '{"totalAmount": 24900, "items": 2}' },
    { id: randomUUID(), transaction_id: 'exg_002', action: 'billed', description: 'Purchase bill EPB/2025-26/0002 generated', values: '{"billNumber": "EPB/2025-26/0002", "totalWithTax": 25647}' },
    { id: randomUUID(), transaction_id: 'exg_003', action: 'created', description: 'Mixed gold jewelry exchange created', values: '{"totalAmount": 233400, "items": 3}' },
    { id: randomUUID(), transaction_id: 'exg_003', action: 'billed', description: 'Purchase bill EPB/2025-26/0003 generated', values: '{"billNumber": "EPB/2025-26/0003", "totalWithTax": 240402}' },
    { id: randomUUID(), transaction_id: 'exg_004', action: 'created', description: 'Pending evaluation transaction created', values: '{"totalAmount": 178200, "items": 1, "status": "pending"}' },
    { id: randomUUID(), transaction_id: 'exg_005', action: 'created', description: 'Antique heritage necklace exchange created', values: '{"totalAmount": 217800, "items": 1}' },
    { id: randomUUID(), transaction_id: 'exg_005', action: 'billed', description: 'Purchase bill EPB/2025-26/0004 generated', values: '{"billNumber": "EPB/2025-26/0004", "totalWithTax": 224334}' },
    { id: randomUUID(), transaction_id: 'exg_006', action: 'created', description: 'Modern silver bangles exchange created', values: '{"totalAmount": 12450, "items": 1}' },
    { id: randomUUID(), transaction_id: 'exg_006', action: 'billed', description: 'Purchase bill EPB/2025-26/0005 generated', values: '{"billNumber": "EPB/2025-26/0005", "totalWithTax": 12823.5}' }
  ]

  for (const entry of auditEntries) {
    await connection.execute(`
      INSERT INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, performed_at) 
      VALUES (?, ?, ?, ?, ?, NOW())
    `, [entry.id, entry.transaction_id, entry.action, entry.description, entry.values])
  }
  console.log(`   ✅ Created ${auditEntries.length} audit trail entries`)

  console.log('✅ Fresh sample data seeding completed')
}

async function verifyDatabaseSetup(connection: mysql.Connection) {
  const tables = ['exchange_rates', 'exchange_transactions', 'exchange_items', 'exchange_purchase_bills', 'sales_exchange_items', 'exchange_audit_trail', 'bill_sequences']
  
  for (const table of tables) {
    const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
    const count = (rows as any)[0].count
    console.log(`   ✅ ${table}: ${count} records`)
  }

  // Test constraints
  console.log('\n🔒 Testing validation constraints...')
  try {
    await connection.execute(`INSERT INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount) VALUES ('test', 'exg_001', 'TEST', 'gold', '22K', -1.0, 0.0, -1.0, 6600.00, -6600.00)`)
    console.log('   ❌ Validation constraints not working')
  } catch (error) {
    console.log('   ✅ Validation constraints working correctly')
  }
}

// Run the reset and seed
resetAndSeedDatabase().catch(console.error)
