-- Professional Jewelry Management System - Complete Database Schema
-- Version: 2.0.0
-- Date: January 31, 2025
-- Description: Comprehensive schema for professional jewelry business management

-- Create database with proper character set
CREATE DATABASE IF NOT EXISTS jewellers_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE jewellers_db;

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- CORE BUSINESS TABLES
-- ============================================================================

-- 1. USERS & AUTHENTICATION
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  role ENUM('super_admin', 'admin', 'manager', 'sales_staff', 'accountant', 'viewer') NOT NULL DEFAULT 'sales_staff',
  permissions JSON,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP NULL,
  password_reset_token VARCHAR(255) NULL,
  password_reset_expires TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_is_active (is_active)
);

-- 2. BUSINESS SETTINGS
CREATE TABLE business_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  business_name VARCHAR(255) NOT NULL,
  business_type ENUM('retail', 'wholesale', 'manufacturing', 'mixed') DEFAULT 'retail',
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  phone VARCHAR(20),
  email VARCHAR(255),
  website VARCHAR(255),
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  license_number VARCHAR(100),
  bank_name VARCHAR(255),
  bank_account_number VARCHAR(50),
  bank_ifsc VARCHAR(20),
  logo_url VARCHAR(500),
  currency VARCHAR(10) DEFAULT 'INR',
  timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
  financial_year_start ENUM('april', 'january') DEFAULT 'april',
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 3.00,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. CUSTOMERS
CREATE TABLE customers (
  id VARCHAR(36) PRIMARY KEY,
  customer_code VARCHAR(50) UNIQUE,
  customer_type ENUM('individual', 'business') DEFAULT 'individual',
  title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100),
  business_name VARCHAR(255),
  phone VARCHAR(20) NOT NULL,
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  date_of_birth DATE,
  anniversary_date DATE,
  gender ENUM('male', 'female', 'other'),
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  aadhar_number VARCHAR(20),
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  credit_days INT DEFAULT 0,
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  loyalty_points INT DEFAULT 0,
  preferred_contact ENUM('phone', 'email', 'sms', 'whatsapp') DEFAULT 'phone',
  notes TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  last_visit TIMESTAMP NULL,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_customer_code (customer_code),
  INDEX idx_phone (phone),
  INDEX idx_email (email),
  INDEX idx_customer_type (customer_type),
  INDEX idx_is_active (is_active),
  INDEX idx_last_visit (last_visit),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 4. SUPPLIERS
CREATE TABLE suppliers (
  id VARCHAR(36) PRIMARY KEY,
  supplier_code VARCHAR(50) UNIQUE,
  company_name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),
  phone VARCHAR(20) NOT NULL,
  alternate_phone VARCHAR(20),
  email VARCHAR(255),
  address_line1 VARCHAR(255),
  address_line2 VARCHAR(255),
  city VARCHAR(100),
  state VARCHAR(100),
  postal_code VARCHAR(20),
  country VARCHAR(100) DEFAULT 'India',
  gst_number VARCHAR(50),
  pan_number VARCHAR(20),
  bank_name VARCHAR(255),
  bank_account_number VARCHAR(50),
  bank_ifsc VARCHAR(20),
  credit_limit DECIMAL(15,2) DEFAULT 0.00,
  credit_days INT DEFAULT 0,
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  total_outstanding DECIMAL(15,2) DEFAULT 0.00,
  supplier_type ENUM('manufacturer', 'wholesaler', 'artisan', 'other') DEFAULT 'wholesaler',
  rating DECIMAL(3,2) DEFAULT 0.00,
  notes TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_supplier_code (supplier_code),
  INDEX idx_company_name (company_name),
  INDEX idx_phone (phone),
  INDEX idx_supplier_type (supplier_type),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 5. CATEGORIES
CREATE TABLE categories (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  parent_id VARCHAR(36),
  category_code VARCHAR(50) UNIQUE,
  hsn_code VARCHAR(20),
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INT DEFAULT 0,
  image_url VARCHAR(500),
  making_charge_type ENUM('percentage', 'fixed', 'per_gram') DEFAULT 'percentage',
  making_charge_value DECIMAL(10,2) DEFAULT 0.00,
  wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_name (name),
  INDEX idx_parent_id (parent_id),
  INDEX idx_category_code (category_code),
  INDEX idx_hsn_code (hsn_code),
  INDEX idx_is_active (is_active),
  INDEX idx_sort_order (sort_order),
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- 6. METAL RATES
CREATE TABLE metal_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond') NOT NULL,
  purity VARCHAR(20) NOT NULL, -- 24K, 22K, 18K, 14K, 999, 925, etc.
  rate_per_gram DECIMAL(12,2) NOT NULL,
  rate_per_10gram DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 10) STORED,
  rate_per_tola DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 11.664) STORED,
  effective_date DATE NOT NULL,
  effective_time TIME DEFAULT '00:00:00',
  is_active BOOLEAN DEFAULT TRUE,
  source VARCHAR(100), -- manual, api, market_feed
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_active_rate (metal_type, purity, is_active, effective_date),
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_effective_date (effective_date),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 7. INVENTORY
CREATE TABLE inventory (
  id VARCHAR(36) PRIMARY KEY,
  item_code VARCHAR(100) UNIQUE NOT NULL,
  barcode VARCHAR(100) UNIQUE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category_id VARCHAR(36),
  subcategory_id VARCHAR(36),
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond', 'other') NOT NULL,
  purity VARCHAR(20),
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  diamond_weight DECIMAL(8,3) DEFAULT 0.000,
  diamond_pieces INT DEFAULT 0,
  stone_details JSON,
  size VARCHAR(50),
  gender ENUM('male', 'female', 'unisex', 'kids') DEFAULT 'unisex',
  occasion VARCHAR(100),
  design_number VARCHAR(100),
  supplier_id VARCHAR(36),
  purchase_rate DECIMAL(12,2) DEFAULT 0.00,
  making_charges DECIMAL(12,2) DEFAULT 0.00,
  stone_charges DECIMAL(12,2) DEFAULT 0.00,
  other_charges DECIMAL(12,2) DEFAULT 0.00,
  total_cost DECIMAL(12,2) GENERATED ALWAYS AS (purchase_rate + making_charges + stone_charges + other_charges) STORED,
  margin_percentage DECIMAL(5,2) DEFAULT 0.00,
  selling_price DECIMAL(12,2) DEFAULT 0.00,
  mrp DECIMAL(12,2) DEFAULT 0.00,
  stock_quantity INT DEFAULT 1,
  min_stock_level INT DEFAULT 0,
  max_stock_level INT DEFAULT 100,
  location VARCHAR(100),
  status ENUM('active', 'sold', 'reserved', 'damaged', 'repair', 'inactive') DEFAULT 'active',
  images JSON,
  tags JSON,
  hsn_code VARCHAR(20),
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_item_code (item_code),
  INDEX idx_barcode (barcode),
  INDEX idx_name (name),
  INDEX idx_category (category_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity),
  INDEX idx_supplier (supplier_id),
  INDEX idx_status (status),
  INDEX idx_stock_quantity (stock_quantity),
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (subcategory_id) REFERENCES categories(id) ON DELETE SET NULL,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 8. SALES
CREATE TABLE sales (
  id VARCHAR(36) PRIMARY KEY,
  invoice_number VARCHAR(100) UNIQUE NOT NULL,
  invoice_date DATE NOT NULL,
  customer_id VARCHAR(36),
  sale_type ENUM('cash', 'credit', 'exchange', 'scheme') DEFAULT 'cash',
  subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount) STORED,
  round_off DECIMAL(5,2) DEFAULT 0.00,
  grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + round_off) STORED,
  exchange_deduction DECIMAL(15,2) DEFAULT 0.00,
  final_amount DECIMAL(15,2) GENERATED ALWAYS AS (grand_total - exchange_deduction) STORED,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'mixed') DEFAULT 'cash',
  payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'paid',
  due_date DATE,
  status ENUM('draft', 'confirmed', 'delivered', 'cancelled', 'returned') DEFAULT 'confirmed',
  notes TEXT,
  terms_conditions TEXT,
  sales_person_id VARCHAR(36),
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_invoice_number (invoice_number),
  INDEX idx_invoice_date (invoice_date),
  INDEX idx_customer (customer_id),
  INDEX idx_sale_type (sale_type),
  INDEX idx_payment_status (payment_status),
  INDEX idx_status (status),
  INDEX idx_sales_person (sales_person_id),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (sales_person_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 9. SALE ITEMS
CREATE TABLE sale_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  inventory_id VARCHAR(36),
  item_code VARCHAR(100),
  item_name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond', 'other') NOT NULL,
  purity VARCHAR(20),
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  rate_per_gram DECIMAL(12,2) NOT NULL,
  metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (net_weight * rate_per_gram) STORED,
  making_charges DECIMAL(15,2) DEFAULT 0.00,
  stone_charges DECIMAL(15,2) DEFAULT 0.00,
  other_charges DECIMAL(15,2) DEFAULT 0.00,
  total_amount DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges) STORED,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  final_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - discount_amount) STORED,
  hsn_code VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_sale (sale_id),
  INDEX idx_inventory (inventory_id),
  INDEX idx_item_code (item_code),
  INDEX idx_metal_type (metal_type),
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE SET NULL
);

-- 10. PURCHASES
CREATE TABLE purchases (
  id VARCHAR(36) PRIMARY KEY,
  purchase_number VARCHAR(100) UNIQUE NOT NULL,
  purchase_date DATE NOT NULL,
  supplier_id VARCHAR(36),
  purchase_type ENUM('inventory', 'raw_material', 'making', 'other') DEFAULT 'inventory',
  subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount) STORED,
  round_off DECIMAL(5,2) DEFAULT 0.00,
  grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + round_off) STORED,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'credit') DEFAULT 'cash',
  payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
  due_date DATE,
  status ENUM('draft', 'confirmed', 'received', 'cancelled') DEFAULT 'draft',
  notes TEXT,
  terms_conditions TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_purchase_number (purchase_number),
  INDEX idx_purchase_date (purchase_date),
  INDEX idx_supplier (supplier_id),
  INDEX idx_purchase_type (purchase_type),
  INDEX idx_payment_status (payment_status),
  INDEX idx_status (status),
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 11. PURCHASE ITEMS
CREATE TABLE purchase_items (
  id VARCHAR(36) PRIMARY KEY,
  purchase_id VARCHAR(36) NOT NULL,
  item_name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  metal_type ENUM('gold', 'silver', 'platinum', 'diamond', 'other') NOT NULL,
  purity VARCHAR(20),
  gross_weight DECIMAL(10,3) DEFAULT 0.000,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  rate_per_gram DECIMAL(12,2) NOT NULL,
  metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (net_weight * rate_per_gram) STORED,
  making_charges DECIMAL(15,2) DEFAULT 0.00,
  stone_charges DECIMAL(15,2) DEFAULT 0.00,
  other_charges DECIMAL(15,2) DEFAULT 0.00,
  total_amount DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges) STORED,
  quantity INT DEFAULT 1,
  hsn_code VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_purchase (purchase_id),
  INDEX idx_metal_type (metal_type),
  FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE
);

-- ============================================================================
-- EXCHANGE SYSTEM TABLES (Enhanced)
-- ============================================================================

-- 12. EXCHANGE RATES (Enhanced from existing)
CREATE TABLE exchange_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver', 'platinum') NOT NULL,
  purity VARCHAR(20) NOT NULL,
  rate_per_gram DECIMAL(12,2) NOT NULL,
  effective_date DATE NOT NULL,
  effective_time TIME DEFAULT '00:00:00',
  is_active BOOLEAN DEFAULT TRUE,
  source VARCHAR(100) DEFAULT 'manual',
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_active_rate (metal_type, purity, is_active, effective_date),
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_effective_date (effective_date),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  
  -- Validation constraints
  CONSTRAINT chk_exchange_rates_positive_rate CHECK (rate_per_gram > 0),
  CONSTRAINT chk_exchange_rates_valid_purity CHECK (
    (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
    (metal_type = 'silver' AND purity IN ('999', '925', '900', '800')) OR
    (metal_type = 'platinum' AND purity IN ('950', '900', '850'))
  ),
  CONSTRAINT chk_exchange_rates_reasonable_rates CHECK (
    (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 50000) OR
    (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 2000) OR
    (metal_type = 'platinum' AND rate_per_gram BETWEEN 1000 AND 10000)
  )
);

-- 13. EXCHANGE TRANSACTIONS (Enhanced)
CREATE TABLE exchange_transactions (
  id VARCHAR(36) PRIMARY KEY,
  transaction_number VARCHAR(100) UNIQUE NOT NULL,
  customer_id VARCHAR(36),
  transaction_date DATE NOT NULL,
  transaction_time TIME DEFAULT CURRENT_TIME,
  total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  payment_method ENUM('cash', 'bank_transfer', 'adjustment', 'account_credit') DEFAULT 'cash',
  notes TEXT,
  status ENUM('pending', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
  approval_required BOOLEAN DEFAULT FALSE,
  approved_by VARCHAR(36),
  approved_at TIMESTAMP NULL,
  purchase_bill_generated BOOLEAN DEFAULT FALSE,
  purchase_bill_id VARCHAR(36),
  purchase_bill_number VARCHAR(100),
  purchase_bill_date DATE,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_transaction_number (transaction_number),
  INDEX idx_customer (customer_id),
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_status (status),
  INDEX idx_created_by (created_by),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,

  -- Validation constraints
  CONSTRAINT chk_exchange_transactions_positive_amount CHECK (total_amount >= 0),
  CONSTRAINT chk_exchange_transactions_valid_date CHECK (
    transaction_date >= '2020-01-01' AND
    transaction_date <= CURDATE() + INTERVAL 1 DAY
  )
);

-- 14. EXCHANGE ITEMS (Enhanced)
CREATE TABLE exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  transaction_id VARCHAR(36) NOT NULL,
  item_description VARCHAR(255) NOT NULL,
  metal_type ENUM('gold', 'silver', 'platinum') NOT NULL,
  purity VARCHAR(20) NOT NULL,
  gross_weight DECIMAL(10,3) NOT NULL,
  stone_weight DECIMAL(10,3) DEFAULT 0.000,
  net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
  rate_per_gram DECIMAL(12,2) NOT NULL,
  amount DECIMAL(15,2) GENERATED ALWAYS AS (net_weight * rate_per_gram) STORED,
  item_condition ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good',
  hallmark_available BOOLEAN DEFAULT FALSE,
  certificate_number VARCHAR(100),
  photos JSON,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_transaction (transaction_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity),
  INDEX idx_item_condition (item_condition),
  FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,

  -- Validation constraints
  CONSTRAINT chk_exchange_items_positive_weights CHECK (
    gross_weight >= 0 AND stone_weight >= 0 AND gross_weight >= stone_weight
  ),
  CONSTRAINT chk_exchange_items_positive_rate CHECK (rate_per_gram > 0),
  CONSTRAINT chk_exchange_items_valid_purity CHECK (
    (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
    (metal_type = 'silver' AND purity IN ('999', '925', '900', '800')) OR
    (metal_type = 'platinum' AND purity IN ('950', '900', '850'))
  ),
  CONSTRAINT chk_exchange_items_reasonable_rates CHECK (
    (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 50000) OR
    (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 2000) OR
    (metal_type = 'platinum' AND rate_per_gram BETWEEN 1000 AND 10000)
  )
);

-- 15. EXCHANGE PURCHASE BILLS (Enhanced)
CREATE TABLE exchange_purchase_bills (
  id VARCHAR(36) PRIMARY KEY,
  bill_number VARCHAR(100) UNIQUE NOT NULL,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  customer_id VARCHAR(36),
  bill_date DATE NOT NULL,
  bill_time TIME DEFAULT CURRENT_TIME,
  subtotal DECIMAL(15,2) NOT NULL,
  discount_percentage DECIMAL(5,2) DEFAULT 0.00,
  discount_amount DECIMAL(15,2) DEFAULT 0.00,
  taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,
  cgst_rate DECIMAL(5,2) DEFAULT 1.50,
  sgst_rate DECIMAL(5,2) DEFAULT 1.50,
  igst_rate DECIMAL(5,2) DEFAULT 0.00,
  cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
  sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
  igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
  total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount) STORED,
  round_off DECIMAL(5,2) DEFAULT 0.00,
  total_with_tax DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + round_off) STORED,
  payment_method ENUM('cash', 'bank_transfer', 'cheque', 'account_credit') DEFAULT 'cash',
  payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'paid',
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_bill_number (bill_number),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_customer (customer_id),
  INDEX idx_bill_date (bill_date),
  INDEX idx_payment_status (payment_status),
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE RESTRICT,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,

  -- Validation constraints
  CONSTRAINT chk_purchase_bills_positive_amounts CHECK (
    subtotal >= 0 AND discount_amount >= 0 AND round_off BETWEEN -5.00 AND 5.00
  ),
  CONSTRAINT chk_purchase_bills_valid_date CHECK (
    bill_date >= '2020-01-01' AND
    bill_date <= CURDATE() + INTERVAL 1 DAY
  )
);

-- 16. SALES EXCHANGE ITEMS (Enhanced)
CREATE TABLE sales_exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  exchange_item_id VARCHAR(36) NOT NULL,
  deduction_amount DECIMAL(15,2) NOT NULL,
  applied_rate DECIMAL(12,2) NOT NULL,
  exchange_percentage DECIMAL(5,2) DEFAULT 100.00,
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_sale (sale_id),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_exchange_item (exchange_item_id),
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE RESTRICT,
  FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) ON DELETE RESTRICT,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,

  -- Validation constraints
  CONSTRAINT chk_sales_exchange_positive_deduction CHECK (deduction_amount >= 0),
  CONSTRAINT chk_sales_exchange_positive_rate CHECK (applied_rate > 0),
  CONSTRAINT chk_sales_exchange_valid_percentage CHECK (exchange_percentage BETWEEN 0 AND 100)
);

-- ============================================================================
-- BUSINESS WORKFLOW TABLES
-- ============================================================================

-- 17. SCHEMES (Enhanced)
CREATE TABLE schemes (
  id VARCHAR(36) PRIMARY KEY,
  scheme_number VARCHAR(100) UNIQUE NOT NULL,
  scheme_name VARCHAR(255) NOT NULL,
  customer_id VARCHAR(36),
  scheme_type ENUM('monthly', 'quarterly', 'yearly', 'flexible') DEFAULT 'monthly',
  total_amount DECIMAL(15,2) NOT NULL,
  installment_amount DECIMAL(15,2) NOT NULL,
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  outstanding_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
  duration_months INT NOT NULL,
  start_date DATE NOT NULL,
  maturity_date DATE GENERATED ALWAYS AS (start_date + INTERVAL duration_months MONTH) STORED,
  interest_rate DECIMAL(5,2) DEFAULT 0.00,
  bonus_percentage DECIMAL(5,2) DEFAULT 0.00,
  status ENUM('active', 'completed', 'cancelled', 'defaulted', 'matured') DEFAULT 'active',
  auto_debit BOOLEAN DEFAULT FALSE,
  reminder_enabled BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_scheme_number (scheme_number),
  INDEX idx_customer (customer_id),
  INDEX idx_scheme_type (scheme_type),
  INDEX idx_status (status),
  INDEX idx_maturity_date (maturity_date),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 18. SCHEME PAYMENTS
CREATE TABLE scheme_payments (
  id VARCHAR(36) PRIMARY KEY,
  scheme_id VARCHAR(36) NOT NULL,
  payment_number INT NOT NULL,
  payment_date DATE NOT NULL,
  due_date DATE NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque') DEFAULT 'cash',
  transaction_reference VARCHAR(100),
  status ENUM('pending', 'paid', 'overdue', 'partial') DEFAULT 'pending',
  late_fee DECIMAL(10,2) DEFAULT 0.00,
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_scheme_payment (scheme_id, payment_number),
  INDEX idx_scheme (scheme_id),
  INDEX idx_payment_date (payment_date),
  INDEX idx_due_date (due_date),
  INDEX idx_status (status),
  FOREIGN KEY (scheme_id) REFERENCES schemes(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 19. REPAIRS (Enhanced)
CREATE TABLE repairs (
  id VARCHAR(36) PRIMARY KEY,
  repair_number VARCHAR(100) UNIQUE NOT NULL,
  customer_id VARCHAR(36),
  item_name VARCHAR(255) NOT NULL,
  item_description TEXT,
  repair_type ENUM('repair', 'resize', 'polish', 'stone_setting', 'chain_repair', 'custom_making') DEFAULT 'repair',
  received_date DATE NOT NULL,
  promised_date DATE,
  completed_date DATE,
  delivered_date DATE,
  estimated_cost DECIMAL(15,2) DEFAULT 0.00,
  actual_cost DECIMAL(15,2) DEFAULT 0.00,
  advance_paid DECIMAL(15,2) DEFAULT 0.00,
  balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (actual_cost - advance_paid) STORED,
  status ENUM('received', 'in_progress', 'completed', 'ready_for_delivery', 'delivered', 'cancelled') DEFAULT 'received',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  special_instructions TEXT,
  before_photos JSON,
  after_photos JSON,
  artisan_id VARCHAR(36),
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_repair_number (repair_number),
  INDEX idx_customer (customer_id),
  INDEX idx_repair_type (repair_type),
  INDEX idx_status (status),
  INDEX idx_promised_date (promised_date),
  INDEX idx_priority (priority),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (artisan_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ============================================================================
-- FINANCIAL & ACCOUNTING TABLES
-- ============================================================================

-- 20. PAYMENTS
CREATE TABLE payments (
  id VARCHAR(36) PRIMARY KEY,
  payment_number VARCHAR(100) UNIQUE NOT NULL,
  payment_date DATE NOT NULL,
  payment_type ENUM('sale_payment', 'purchase_payment', 'scheme_payment', 'advance', 'refund', 'other') NOT NULL,
  reference_type ENUM('sale', 'purchase', 'scheme', 'repair', 'exchange', 'other') NOT NULL,
  reference_id VARCHAR(36),
  customer_id VARCHAR(36),
  supplier_id VARCHAR(36),
  amount DECIMAL(15,2) NOT NULL,
  payment_method ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'dd') NOT NULL,
  transaction_reference VARCHAR(100),
  bank_name VARCHAR(255),
  cheque_number VARCHAR(50),
  cheque_date DATE,
  status ENUM('pending', 'cleared', 'bounced', 'cancelled') DEFAULT 'cleared',
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_payment_number (payment_number),
  INDEX idx_payment_date (payment_date),
  INDEX idx_payment_type (payment_type),
  INDEX idx_reference (reference_type, reference_id),
  INDEX idx_customer (customer_id),
  INDEX idx_supplier (supplier_id),
  INDEX idx_status (status),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 21. BILL SEQUENCES (Enhanced)
CREATE TABLE bill_sequences (
  id VARCHAR(36) PRIMARY KEY,
  sequence_type VARCHAR(100) NOT NULL,
  prefix VARCHAR(20) NOT NULL,
  current_number INT NOT NULL DEFAULT 0,
  financial_year VARCHAR(20) NOT NULL,
  format_pattern VARCHAR(100) DEFAULT '{prefix}/{year}/{number:04d}',
  reset_annually BOOLEAN DEFAULT TRUE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY unique_sequence (sequence_type, financial_year),
  INDEX idx_sequence_type (sequence_type),
  INDEX idx_financial_year (financial_year),
  INDEX idx_is_active (is_active),

  CONSTRAINT chk_bill_sequences_positive_number CHECK (current_number >= 0)
);

-- 22. AUDIT TRAIL (Enhanced)
CREATE TABLE audit_trail (
  id VARCHAR(36) PRIMARY KEY,
  table_name VARCHAR(100) NOT NULL,
  record_id VARCHAR(36) NOT NULL,
  action_type ENUM('create', 'update', 'delete', 'view', 'export', 'print') NOT NULL,
  old_values JSON,
  new_values JSON,
  changed_fields JSON,
  user_id VARCHAR(36),
  ip_address VARCHAR(45),
  user_agent TEXT,
  session_id VARCHAR(255),
  performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_table_record (table_name, record_id),
  INDEX idx_action_type (action_type),
  INDEX idx_user (user_id),
  INDEX idx_performed_at (performed_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 23. EXCHANGE AUDIT TRAIL (Enhanced)
CREATE TABLE exchange_audit_trail (
  id VARCHAR(36) PRIMARY KEY,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  action_type ENUM('created', 'updated', 'approved', 'billed', 'used_in_sale', 'cancelled', 'refunded') NOT NULL,
  action_description TEXT NOT NULL,
  old_values JSON,
  new_values JSON,
  related_bill_id VARCHAR(36),
  related_sale_id VARCHAR(36),
  performed_by VARCHAR(36),
  performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_action_type (action_type),
  INDEX idx_performed_at (performed_at),
  INDEX idx_performed_by (performed_by),
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (related_bill_id) REFERENCES exchange_purchase_bills(id) ON DELETE SET NULL,
  FOREIGN KEY (related_sale_id) REFERENCES sales(id) ON DELETE SET NULL,
  FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ============================================================================
-- REPORTING & ANALYTICS TABLES
-- ============================================================================

-- 24. REPORTS
CREATE TABLE reports (
  id VARCHAR(36) PRIMARY KEY,
  report_name VARCHAR(255) NOT NULL,
  report_type ENUM('sales', 'purchase', 'inventory', 'customer', 'financial', 'exchange', 'custom') NOT NULL,
  description TEXT,
  sql_query TEXT,
  parameters JSON,
  is_system_report BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_report_type (report_type),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 25. REPORT EXECUTIONS
CREATE TABLE report_executions (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  executed_by VARCHAR(36),
  execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  parameters_used JSON,
  execution_duration_ms INT,
  status ENUM('success', 'failed', 'timeout') DEFAULT 'success',
  error_message TEXT,
  result_count INT DEFAULT 0,

  INDEX idx_report (report_id),
  INDEX idx_executed_by (executed_by),
  INDEX idx_execution_time (execution_time),
  FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
  FOREIGN KEY (executed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ============================================================================
-- SYSTEM CONFIGURATION TABLES
-- ============================================================================

-- 26. SYSTEM_SETTINGS
CREATE TABLE system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json', 'date') DEFAULT 'string',
  description TEXT,
  is_system BOOLEAN DEFAULT FALSE,
  is_encrypted BOOLEAN DEFAULT FALSE,
  updated_by VARCHAR(36),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_setting_key (setting_key),
  INDEX idx_is_system (is_system),
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 27. NOTIFICATIONS
CREATE TABLE notifications (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  notification_type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
  category ENUM('system', 'sale', 'purchase', 'inventory', 'scheme', 'repair', 'exchange') DEFAULT 'system',
  is_read BOOLEAN DEFAULT FALSE,
  action_url VARCHAR(500),
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_user (user_id),
  INDEX idx_is_read (is_read),
  INDEX idx_notification_type (notification_type),
  INDEX idx_category (category),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 28. USER_SESSIONS
CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_user (user_id),
  INDEX idx_session_token (session_token),
  INDEX idx_is_active (is_active),
  INDEX idx_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ============================================================================
-- VIEWS FOR REPORTING AND ANALYTICS
-- ============================================================================

-- Sales Summary View
CREATE VIEW v_sales_summary AS
SELECT
  DATE(s.invoice_date) as sale_date,
  COUNT(s.id) as total_sales,
  SUM(s.subtotal) as total_subtotal,
  SUM(s.discount_amount) as total_discount,
  SUM(s.total_tax) as total_tax,
  SUM(s.grand_total) as total_grand_total,
  SUM(s.exchange_deduction) as total_exchange_deduction,
  SUM(s.final_amount) as total_final_amount,
  AVG(s.final_amount) as average_sale_amount
FROM sales s
WHERE s.status IN ('confirmed', 'delivered')
GROUP BY DATE(s.invoice_date);

-- Inventory Summary View
CREATE VIEW v_inventory_summary AS
SELECT
  i.metal_type,
  i.purity,
  COUNT(i.id) as total_items,
  SUM(CASE WHEN i.status = 'active' THEN 1 ELSE 0 END) as active_items,
  SUM(CASE WHEN i.status = 'sold' THEN 1 ELSE 0 END) as sold_items,
  SUM(i.gross_weight) as total_gross_weight,
  SUM(i.net_weight) as total_net_weight,
  SUM(i.total_cost) as total_cost_value,
  SUM(i.selling_price) as total_selling_value
FROM inventory i
GROUP BY i.metal_type, i.purity;

-- Exchange Summary View
CREATE VIEW v_exchange_summary AS
SELECT
  DATE(et.transaction_date) as exchange_date,
  COUNT(et.id) as total_transactions,
  SUM(et.total_amount) as total_amount,
  COUNT(CASE WHEN et.status = 'completed' THEN 1 END) as completed_transactions,
  COUNT(CASE WHEN et.status = 'pending' THEN 1 END) as pending_transactions,
  SUM(CASE WHEN et.purchase_bill_generated THEN et.total_amount ELSE 0 END) as billed_amount,
  SUM(CASE WHEN sei.id IS NOT NULL THEN sei.deduction_amount ELSE 0 END) as used_in_sales
FROM exchange_transactions et
LEFT JOIN sales_exchange_items sei ON et.id = sei.exchange_transaction_id
GROUP BY DATE(et.transaction_date);

-- Customer Analysis View
CREATE VIEW v_customer_analysis AS
SELECT
  c.id,
  c.first_name,
  c.last_name,
  c.phone,
  c.total_purchases,
  COUNT(s.id) as total_sales,
  SUM(s.final_amount) as total_sale_amount,
  COUNT(et.id) as total_exchanges,
  SUM(et.total_amount) as total_exchange_amount,
  COUNT(sch.id) as total_schemes,
  SUM(sch.paid_amount) as total_scheme_payments,
  c.last_visit,
  DATEDIFF(CURDATE(), c.last_visit) as days_since_last_visit
FROM customers c
LEFT JOIN sales s ON c.id = s.customer_id AND s.status IN ('confirmed', 'delivered')
LEFT JOIN exchange_transactions et ON c.id = et.customer_id AND et.status = 'completed'
LEFT JOIN schemes sch ON c.id = sch.customer_id
GROUP BY c.id;

-- ============================================================================
-- STORED PROCEDURES FOR BUSINESS LOGIC
-- ============================================================================

DELIMITER //

-- Procedure to update exchange transaction total
CREATE PROCEDURE sp_update_exchange_transaction_total(IN transaction_id VARCHAR(36))
BEGIN
  DECLARE total DECIMAL(15,2) DEFAULT 0.00;

  SELECT SUM(amount) INTO total
  FROM exchange_items
  WHERE transaction_id = transaction_id;

  UPDATE exchange_transactions
  SET total_amount = COALESCE(total, 0.00),
      updated_at = CURRENT_TIMESTAMP
  WHERE id = transaction_id;
END //

-- Procedure to update sale total
CREATE PROCEDURE sp_update_sale_total(IN sale_id VARCHAR(36))
BEGIN
  DECLARE subtotal DECIMAL(15,2) DEFAULT 0.00;

  SELECT SUM(final_amount) INTO subtotal
  FROM sale_items
  WHERE sale_id = sale_id;

  UPDATE sales
  SET subtotal = COALESCE(subtotal, 0.00),
      updated_at = CURRENT_TIMESTAMP
  WHERE id = sale_id;
END //

-- Procedure to generate next bill number
CREATE PROCEDURE sp_get_next_bill_number(
  IN sequence_type VARCHAR(100),
  IN financial_year VARCHAR(20),
  OUT next_number VARCHAR(100)
)
BEGIN
  DECLARE current_num INT DEFAULT 0;
  DECLARE prefix VARCHAR(20);
  DECLARE format_pattern VARCHAR(100);

  -- Get current number and increment
  SELECT current_number + 1, prefix, format_pattern
  INTO current_num, prefix, format_pattern
  FROM bill_sequences
  WHERE sequence_type = sequence_type AND financial_year = financial_year;

  -- Update the sequence
  UPDATE bill_sequences
  SET current_number = current_num,
      updated_at = CURRENT_TIMESTAMP
  WHERE sequence_type = sequence_type AND financial_year = financial_year;

  -- Generate the bill number
  SET next_number = CONCAT(prefix, '/', financial_year, '/', LPAD(current_num, 4, '0'));
END //

DELIMITER ;

-- ============================================================================
-- TRIGGERS FOR BUSINESS LOGIC
-- ============================================================================

DELIMITER //

-- Trigger to update exchange transaction total when items change
CREATE TRIGGER tr_exchange_items_after_insert
AFTER INSERT ON exchange_items
FOR EACH ROW
BEGIN
  CALL sp_update_exchange_transaction_total(NEW.transaction_id);
END //

CREATE TRIGGER tr_exchange_items_after_update
AFTER UPDATE ON exchange_items
FOR EACH ROW
BEGIN
  CALL sp_update_exchange_transaction_total(NEW.transaction_id);
END //

CREATE TRIGGER tr_exchange_items_after_delete
AFTER DELETE ON exchange_items
FOR EACH ROW
BEGIN
  CALL sp_update_exchange_transaction_total(OLD.transaction_id);
END //

-- Trigger to update sale total when items change
CREATE TRIGGER tr_sale_items_after_insert
AFTER INSERT ON sale_items
FOR EACH ROW
BEGIN
  CALL sp_update_sale_total(NEW.sale_id);
END //

CREATE TRIGGER tr_sale_items_after_update
AFTER UPDATE ON sale_items
FOR EACH ROW
BEGIN
  CALL sp_update_sale_total(NEW.sale_id);
END //

CREATE TRIGGER tr_sale_items_after_delete
AFTER DELETE ON sale_items
FOR EACH ROW
BEGIN
  CALL sp_update_sale_total(OLD.sale_id);
END //

-- Trigger to update inventory status when sold
CREATE TRIGGER tr_sale_items_update_inventory
AFTER INSERT ON sale_items
FOR EACH ROW
BEGIN
  UPDATE inventory
  SET status = 'sold',
      stock_quantity = stock_quantity - 1,
      updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.inventory_id AND stock_quantity > 0;
END //

DELIMITER ;

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert default business settings
INSERT INTO business_settings (
  business_name, business_type, currency, timezone, financial_year_start,
  cgst_rate, sgst_rate, igst_rate
) VALUES (
  'JJ Jewellers', 'retail', 'INR', 'Asia/Kolkata', 'april',
  1.50, 1.50, 3.00
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_system) VALUES
('app_version', '2.0.0', 'string', 'Application version', TRUE),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode flag', TRUE),
('backup_enabled', 'true', 'boolean', 'Automatic backup enabled', FALSE),
('low_stock_threshold', '5', 'number', 'Low stock alert threshold', FALSE),
('auto_update_rates', 'true', 'boolean', 'Auto update metal rates', FALSE),
('invoice_template', 'standard', 'string', 'Default invoice template', FALSE),
('print_logo', 'true', 'boolean', 'Print logo on invoices', FALSE),
('email_notifications', 'true', 'boolean', 'Email notifications enabled', FALSE),
('sms_notifications', 'false', 'boolean', 'SMS notifications enabled', FALSE);

-- Insert default categories
INSERT INTO categories (id, name, category_code, hsn_code, making_charge_type, making_charge_value) VALUES
(UUID(), 'Rings', 'RING', '71131900', 'percentage', 15.00),
(UUID(), 'Necklaces', 'NECK', '71131100', 'percentage', 18.00),
(UUID(), 'Earrings', 'EARR', '71131200', 'percentage', 12.00),
(UUID(), 'Bangles', 'BANG', '71131300', 'percentage', 10.00),
(UUID(), 'Chains', 'CHAI', '71131400', 'percentage', 8.00),
(UUID(), 'Pendants', 'PEND', '71131500', 'percentage', 15.00),
(UUID(), 'Bracelets', 'BRAC', '71131600', 'percentage', 12.00);

-- Insert current financial year bill sequences
INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) VALUES
(UUID(), 'sales_invoice', 'INV', 0, '2024-25'),
(UUID(), 'purchase_bill', 'PUR', 0, '2024-25'),
(UUID(), 'exchange_purchase', 'EPB', 0, '2024-25'),
(UUID(), 'scheme_receipt', 'SCH', 0, '2024-25'),
(UUID(), 'repair_receipt', 'REP', 0, '2024-25');

-- Insert default metal rates
INSERT INTO metal_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active, source, created_by) VALUES
(UUID(), 'gold', '24K', 7200.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'gold', '22K', 6600.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'gold', '18K', 5400.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'gold', '14K', 4200.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'silver', '999', 90.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'silver', '925', 83.00, CURDATE(), TRUE, 'manual', NULL),
(UUID(), 'silver', '900', 81.00, CURDATE(), TRUE, 'manual', NULL);

-- Insert exchange rates (same as metal rates for consistency)
INSERT INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active, source) VALUES
(UUID(), 'gold', '24K', 7200.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'gold', '22K', 6600.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'gold', '18K', 5400.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'gold', '14K', 4200.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'silver', '999', 90.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'silver', '925', 83.00, CURDATE(), TRUE, 'manual'),
(UUID(), 'silver', '900', 81.00, CURDATE(), TRUE, 'manual');

-- ============================================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Additional performance indexes
CREATE INDEX idx_sales_date_status ON sales(invoice_date, status);
CREATE INDEX idx_sales_customer_date ON sales(customer_id, invoice_date);
CREATE INDEX idx_inventory_metal_status ON inventory(metal_type, status);
CREATE INDEX idx_exchange_date_status ON exchange_transactions(transaction_date, status);
CREATE INDEX idx_payments_date_type ON payments(payment_date, payment_type);
CREATE INDEX idx_audit_trail_date ON audit_trail(performed_at);

-- ============================================================================
-- SCHEMA VALIDATION AND CONSTRAINTS
-- ============================================================================

-- Add foreign key constraint for exchange transactions purchase bill reference
ALTER TABLE exchange_transactions
ADD CONSTRAINT fk_exchange_purchase_bill
FOREIGN KEY (purchase_bill_id) REFERENCES exchange_purchase_bills(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Schema creation completed
SELECT 'Professional Jewelry Management System Schema Created Successfully' as status;
