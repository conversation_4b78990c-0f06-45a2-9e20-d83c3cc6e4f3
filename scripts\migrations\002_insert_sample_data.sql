-- Sample Data for Exchange System Testing
-- Version: 1.0.0
-- Description: Inserts sample customers, exchange transactions, and related data for testing

-- Insert sample customers if they don't exist
INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) VALUES
('cust_001', '<PERSON><PERSON>', '9876543210', 'raj<PERSON>.<EMAIL>', '123 MG Road, Mumbai, Maharashtra 400001', 150000.00, '2024-01-31', NOW(), NOW()),
('cust_002', '<PERSON><PERSON>', '9876543211', '<EMAIL>', '456 Brigade Road, Bangalore, Karnataka 560001', 85000.00, '2024-01-30', NOW(), NOW()),
('cust_003', '<PERSON><PERSON>', '9876543212', '<EMAIL>', '789 CG Road, Ahmedabad, Gujarat 380001', 220000.00, '2024-01-29', NOW(), NOW()),
('cust_004', '<PERSON><PERSON>', '9876543213', '<EMAIL>', '321 Jubilee Hills, Hyderabad, Telangana 500001', 95000.00, '2024-01-28', NOW(), NOW()),
('cust_005', 'Vikram Singh', '9876543214', '<EMAIL>', '654 Connaught Place, New Delhi, Delhi 110001', 175000.00, '2024-01-27', NOW(), NOW());

-- Insert sample exchange transactions
INSERT INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, purchase_bill_generated, created_at, updated_at) VALUES
('exg_001', 'EXG-20240131-001', 'cust_001', '2024-01-31', 33000.00, 'cash', 'Old gold bar exchange', 'completed', TRUE, NOW(), NOW()),
('exg_002', 'EXG-20240130-001', 'cust_002', '2024-01-30', 15600.00, 'cash', 'Silver bangles exchange', 'completed', TRUE, NOW(), NOW()),
('exg_003', 'EXG-20240129-001', 'cust_003', '2024-01-29', 45000.00, 'cash', 'Gold chain exchange', 'completed', FALSE, NOW(), NOW()),
('exg_004', 'EXG-20240128-001', 'cust_004', '2024-01-28', 28500.00, 'cash', 'Mixed gold jewelry', 'completed', TRUE, NOW(), NOW()),
('exg_005', 'EXG-20240127-001', 'cust_005', '2024-01-27', 52000.00, 'cash', 'Gold necklace set', 'pending', FALSE, NOW(), NOW());

-- Insert sample exchange items
INSERT INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) VALUES
-- Items for transaction exg_001
('item_001', 'exg_001', 'GOLD OLD BAR', 'gold', '22K', 10.000, 2.500, 7.500, 4400.00, 33000.00, 'good', 'Clean gold bar', NOW(), NOW()),

-- Items for transaction exg_002
('item_002', 'exg_002', 'OLD SILVER BANGLES', 'silver', '925', 220.000, 20.000, 200.000, 78.00, 15600.00, 'good', 'Set of 4 bangles', NOW(), NOW()),

-- Items for transaction exg_003
('item_003', 'exg_003', 'GOLD CHAIN OLD', 'gold', '18K', 15.000, 3.000, 12.000, 5100.00, 61200.00, 'fair', 'Slightly worn chain', NOW(), NOW()),
('item_004', 'exg_003', 'GOLD PENDANT', 'gold', '22K', 5.000, 1.500, 3.500, 6200.00, 21700.00, 'good', 'Traditional pendant', NOW(), NOW()),

-- Items for transaction exg_004
('item_005', 'exg_004', 'GOLD EARRINGS PAIR', 'gold', '22K', 8.000, 3.000, 5.000, 6200.00, 31000.00, 'good', 'Matching pair', NOW(), NOW()),
('item_006', 'exg_004', 'GOLD RING', 'gold', '18K', 6.000, 2.000, 4.000, 5100.00, 20400.00, 'fair', 'Wedding ring', NOW(), NOW()),

-- Items for transaction exg_005
('item_007', 'exg_005', 'GOLD NECKLACE HEAVY', 'gold', '22K', 25.000, 5.000, 20.000, 6200.00, 124000.00, 'good', 'Heavy traditional necklace', NOW(), NOW());

-- Update transaction totals based on items
UPDATE exchange_transactions SET total_amount = (
    SELECT SUM(amount) FROM exchange_items WHERE transaction_id = exchange_transactions.id
) WHERE id IN ('exg_001', 'exg_002', 'exg_003', 'exg_004', 'exg_005');

-- Insert sample purchase bills for completed transactions
INSERT INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) VALUES
('bill_001', 'EPB/2024-25/0001', 'exg_001', 'cust_001', '2024-01-31', 33000.00, 495.00, 495.00, 33990.00, 'cash', 'paid', 'Purchase bill for gold bar exchange', NOW(), NOW()),
('bill_002', 'EPB/2024-25/0002', 'exg_002', 'cust_002', '2024-01-30', 15600.00, 234.00, 234.00, 16068.00, 'cash', 'paid', 'Purchase bill for silver bangles', NOW(), NOW()),
('bill_003', 'EPB/2024-25/0003', 'exg_004', 'cust_004', '2024-01-28', 51400.00, 771.00, 771.00, 52942.00, 'cash', 'paid', 'Purchase bill for mixed gold jewelry', NOW(), NOW());

-- Update exchange transactions with purchase bill references
UPDATE exchange_transactions SET 
    purchase_bill_id = 'bill_001',
    purchase_bill_number = 'EPB/2024-25/0001',
    purchase_bill_date = '2024-01-31'
WHERE id = 'exg_001';

UPDATE exchange_transactions SET 
    purchase_bill_id = 'bill_002',
    purchase_bill_number = 'EPB/2024-25/0002',
    purchase_bill_date = '2024-01-30'
WHERE id = 'exg_002';

UPDATE exchange_transactions SET 
    purchase_bill_id = 'bill_003',
    purchase_bill_number = 'EPB/2024-25/0003',
    purchase_bill_date = '2024-01-28'
WHERE id = 'exg_004';

-- Update bill sequence counter
UPDATE bill_sequences SET current_number = 3 
WHERE sequence_type = 'exchange_purchase' 
AND financial_year = CONCAT(YEAR(CURDATE()), '-', RIGHT(YEAR(CURDATE()) + 1, 2));

-- Insert sample audit trail entries
INSERT INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, related_bill_id, performed_at) VALUES
('audit_001', 'exg_001', 'created', 'Exchange transaction created for gold bar', '{"totalAmount": 33000, "items": 1}', NULL, '2024-01-31 10:30:00'),
('audit_002', 'exg_001', 'billed', 'Purchase bill EPB/2024-25/0001 generated', '{"billNumber": "EPB/2024-25/0001", "totalWithTax": 33990}', 'bill_001', '2024-01-31 11:00:00'),
('audit_003', 'exg_002', 'created', 'Exchange transaction created for silver bangles', '{"totalAmount": 15600, "items": 1}', NULL, '2024-01-30 14:15:00'),
('audit_004', 'exg_002', 'billed', 'Purchase bill EPB/2024-25/0002 generated', '{"billNumber": "EPB/2024-25/0002", "totalWithTax": 16068}', 'bill_002', '2024-01-30 14:45:00'),
('audit_005', 'exg_003', 'created', 'Exchange transaction created for gold chain and pendant', '{"totalAmount": 82900, "items": 2}', NULL, '2024-01-29 16:20:00'),
('audit_006', 'exg_004', 'created', 'Exchange transaction created for mixed gold jewelry', '{"totalAmount": 51400, "items": 2}', NULL, '2024-01-28 12:10:00'),
('audit_007', 'exg_004', 'billed', 'Purchase bill EPB/2024-25/0003 generated', '{"billNumber": "EPB/2024-25/0003", "totalWithTax": 52942}', 'bill_003', '2024-01-28 12:40:00'),
('audit_008', 'exg_005', 'created', 'Exchange transaction created for heavy gold necklace', '{"totalAmount": 124000, "items": 1}', NULL, '2024-01-27 09:45:00');

-- Insert sample sales data to demonstrate exchange integration
INSERT IGNORE INTO sales (id, customer_id, subtotal, cgst, sgst, total, status, date, created_at, updated_at) VALUES
('sale_001', 'cust_001', 120000.00, 3600.00, 3600.00, 127200.00, 'paid', '2024-02-01', NOW(), NOW()),
('sale_002', 'cust_002', 85000.00, 2550.00, 2550.00, 90100.00, 'paid', '2024-02-02', NOW(), NOW());

-- Insert sample sales exchange items to show exchange usage in sales
INSERT INTO sales_exchange_items (id, sale_id, exchange_transaction_id, exchange_item_id, deduction_amount, applied_rate, created_at, updated_at) VALUES
('sei_001', 'sale_001', 'exg_001', 'item_001', 25000.00, 4400.00, NOW(), NOW()),
('sei_002', 'sale_002', 'exg_002', 'item_002', 12000.00, 78.00, NOW(), NOW());

-- Add audit trail entries for sales usage
INSERT INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, related_sale_id, performed_at) VALUES
('audit_009', 'exg_001', 'used_in_sale', 'Exchange used in sale with deduction of ₹25,000', '{"saleId": "sale_001", "deductionAmount": 25000}', 'sale_001', '2024-02-01 15:30:00'),
('audit_010', 'exg_002', 'used_in_sale', 'Exchange used in sale with deduction of ₹12,000', '{"saleId": "sale_002", "deductionAmount": 12000}', 'sale_002', '2024-02-02 11:20:00');

-- Insert sample inventory items for testing sales integration
INSERT IGNORE INTO inventory (id, name, category, metal_type, gross_weight, stone_weight, net_weight, stone_amount, purity, making_charges, current_value, stock, stone_details, description, created_at, updated_at) VALUES
('inv_001', 'Gold Necklace Set Traditional', 'Necklace', 'gold', 45.000, 5.000, 40.000, 15000.00, '22K', 25000.00, 273000.00, 5, 'Ruby and emerald stones', 'Traditional heavy necklace set', NOW(), NOW()),
('inv_002', 'Gold Earrings Designer', 'Earrings', 'gold', 12.000, 2.000, 10.000, 8000.00, '22K', 8000.00, 78000.00, 8, 'Diamond and pearl', 'Designer earrings with stones', NOW(), NOW()),
('inv_003', 'Silver Bangles Set', 'Bangles', 'silver', 150.000, 0.000, 150.000, 0.00, '925', 5000.00, 16700.00, 12, 'No stones', 'Plain silver bangles set of 6', NOW(), NOW()),
('inv_004', 'Gold Ring Wedding', 'Ring', 'gold', 8.000, 1.000, 7.000, 5000.00, '18K', 3000.00, 43700.00, 15, 'Single diamond', 'Wedding ring with diamond', NOW(), NOW()),
('inv_005', 'Gold Chain Fancy', 'Chain', 'gold', 25.000, 0.000, 25.000, 0.00, '22K', 15000.00, 170000.00, 6, 'No stones', 'Fancy design gold chain', NOW(), NOW());

COMMIT;

-- Display sample data summary
SELECT 'Sample Data Insertion Completed!' as Status;
SELECT 
    'Customers' as DataType, 
    COUNT(*) as Count 
FROM customers 
WHERE id IN ('cust_001', 'cust_002', 'cust_003', 'cust_004', 'cust_005')
UNION ALL
SELECT 
    'Exchange Transactions' as DataType, 
    COUNT(*) as Count 
FROM exchange_transactions 
WHERE id LIKE 'exg_%'
UNION ALL
SELECT 
    'Exchange Items' as DataType, 
    COUNT(*) as Count 
FROM exchange_items 
WHERE id LIKE 'item_%'
UNION ALL
SELECT 
    'Purchase Bills' as DataType, 
    COUNT(*) as Count 
FROM exchange_purchase_bills 
WHERE id LIKE 'bill_%'
UNION ALL
SELECT 
    'Audit Trail Entries' as DataType, 
    COUNT(*) as Count 
FROM exchange_audit_trail 
WHERE id LIKE 'audit_%'
UNION ALL
SELECT 
    'Sales Exchange Items' as DataType, 
    COUNT(*) as Count 
FROM sales_exchange_items 
WHERE id LIKE 'sei_%';

-- Show exchange transaction summary
SELECT 
    et.transaction_number,
    et.transaction_date,
    c.name as customer_name,
    et.total_amount,
    et.status,
    CASE WHEN et.purchase_bill_generated THEN 'Yes' ELSE 'No' END as bill_generated,
    et.purchase_bill_number
FROM exchange_transactions et
LEFT JOIN customers c ON et.customer_id = c.id
WHERE et.id LIKE 'exg_%'
ORDER BY et.transaction_date DESC;
