#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedMissingTableData() {
  console.log('🌱 Seeding Data for Missing Tables - JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Get reference data
    const [users] = await connection.execute('SELECT id, username FROM users LIMIT 3')
    const [customers] = await connection.execute('SELECT id, customer_code, first_name, last_name, phone FROM customers LIMIT 5')
    const [suppliers] = await connection.execute('SELECT id, supplier_code, company_name FROM suppliers LIMIT 3')

    const userMap = new Map()
    const customerMap = new Map()
    const supplierMap = new Map()

    ;(users as any[]).forEach(user => userMap.set(user.username, user.id))
    ;(customers as any[]).forEach(customer => customerMap.set(customer.customer_code, customer))
    ;(suppliers as any[]).forEach(supplier => supplierMap.set(supplier.supplier_code, supplier))

    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`

    // Step 1: Seed Purchases
    console.log('📦 Step 1: Seeding purchases...')
    
    const purchases = [
      {
        id: randomUUID(),
        number: `PUR/${financialYear}/001`,
        supplier_id: supplierMap.get('SUP001')?.id,
        supplier_name: 'Chennai Gold Crafts Pvt Ltd',
        subtotal: 450000.00,
        discount_amount: 5000.00,
        shipping_charges: 2000.00,
        notes: 'Gold jewelry purchase for festival season'
      },
      {
        id: randomUUID(),
        number: `PUR/${financialYear}/002`,
        supplier_id: supplierMap.get('SUP002')?.id,
        supplier_name: 'Coimbatore Silver Works',
        subtotal: 125000.00,
        discount_amount: 2500.00,
        shipping_charges: 1000.00,
        notes: 'Silver jewelry and artifacts purchase'
      }
    ]

    for (const purchase of purchases) {
      await connection.execute(`
        INSERT INTO purchases (
          id, purchase_number, purchase_date, supplier_id, supplier_name,
          purchase_type, subtotal, discount_amount, shipping_charges,
          payment_status, status, notes, purchased_by, created_by, created_at, updated_at
        ) VALUES (?, ?, CURDATE(), ?, ?, 'inventory', ?, ?, ?, 'paid', 'received', ?, ?, ?, NOW(), NOW())
      `, [
        purchase.id, purchase.number, purchase.supplier_id, purchase.supplier_name,
        purchase.subtotal, purchase.discount_amount, purchase.shipping_charges,
        purchase.notes, userMap.get('admin'), userMap.get('admin')
      ])
    }
    console.log(`   ✅ Created ${purchases.length} purchases`)

    // Step 2: Seed Purchase Items
    console.log('\n📋 Step 2: Seeding purchase items...')
    
    const purchaseItems = [
      // Purchase 1 items
      { purchase_id: purchases[0].id, line: 1, description: 'Gold Necklace Set Heavy', metal: 'gold', purity: '22K', gross: 45.000, rate: 6590.00, making: 50000.00 },
      { purchase_id: purchases[0].id, line: 2, description: 'Gold Bangles Pair Traditional', metal: 'gold', purity: '22K', gross: 65.000, rate: 6590.00, making: 45000.00 },
      
      // Purchase 2 items
      { purchase_id: purchases[1].id, line: 1, description: 'Silver Necklace Contemporary', metal: 'silver', purity: '925', gross: 35.000, rate: 85.50, making: 8000.00 },
      { purchase_id: purchases[1].id, line: 2, description: 'Silver Bangles Set', metal: 'silver', purity: '925', gross: 120.000, rate: 85.50, making: 12000.00 }
    ]

    for (const item of purchaseItems) {
      await connection.execute(`
        INSERT INTO purchase_items (
          id, purchase_id, line_number, item_description, metal_type, purity,
          gross_weight, rate_per_gram, making_charges, quantity, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 'received', NOW(), NOW())
      `, [
        randomUUID(), item.purchase_id, item.line, item.description, item.metal, item.purity,
        item.gross, item.rate, item.making
      ])
    }
    console.log(`   ✅ Created ${purchaseItems.length} purchase items`)

    // Step 3: Seed Schemes
    console.log('\n💰 Step 3: Seeding schemes...')
    
    const schemes = [
      {
        id: randomUUID(),
        number: `SCH/${financialYear}/001`,
        name: 'Gold Savings Scheme - Premium',
        customer_id: customerMap.get('TRP000001')?.id,
        customer_name: 'Rajesh Murugan',
        customer_phone: '+91-9876543301',
        total_amount: 120000.00,
        installment_amount: 10000.00,
        total_installments: 12,
        paid_installments: 8,
        total_paid: 80000.00
      },
      {
        id: randomUUID(),
        number: `SCH/${financialYear}/002`,
        name: 'Silver Scheme - Standard',
        customer_id: customerMap.get('TRP000002')?.id,
        customer_name: 'Priya Selvam',
        customer_phone: '+91-9876543302',
        total_amount: 60000.00,
        installment_amount: 5000.00,
        total_installments: 12,
        paid_installments: 5,
        total_paid: 25000.00
      }
    ]

    for (const scheme of schemes) {
      await connection.execute(`
        INSERT INTO schemes (
          id, scheme_number, scheme_name, scheme_type, customer_id, customer_name, customer_phone,
          total_amount, installment_amount, total_installments, paid_installments, total_paid,
          start_date, maturity_date, next_due_date, status, bonus_percentage,
          created_by, managed_by, created_at, updated_at
        ) VALUES (?, ?, ?, 'gold_scheme', ?, ?, ?, ?, ?, ?, ?, ?, 
                  DATE_SUB(CURDATE(), INTERVAL 8 MONTH), DATE_ADD(CURDATE(), INTERVAL 4 MONTH), 
                  DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'active', 5.00, ?, ?, NOW(), NOW())
      `, [
        scheme.id, scheme.number, scheme.name, scheme.customer_id, scheme.customer_name, scheme.customer_phone,
        scheme.total_amount, scheme.installment_amount, scheme.total_installments, scheme.paid_installments, scheme.total_paid,
        userMap.get('admin'), userMap.get('admin')
      ])
    }
    console.log(`   ✅ Created ${schemes.length} schemes`)

    // Step 4: Seed Scheme Payments
    console.log('\n💳 Step 4: Seeding scheme payments...')
    
    let paymentCount = 0
    for (const scheme of schemes) {
      for (let i = 1; i <= scheme.paid_installments; i++) {
        await connection.execute(`
          INSERT INTO scheme_payments (
            id, scheme_id, payment_number, payment_date, payment_amount,
            payment_mode, status, received_by, created_at, updated_at
          ) VALUES (?, ?, ?, DATE_SUB(CURDATE(), INTERVAL ? MONTH), ?, 'cash', 'confirmed', ?, NOW(), NOW())
        `, [
          randomUUID(), scheme.id, i, (scheme.paid_installments - i + 1), scheme.installment_amount, userMap.get('admin')
        ])
        paymentCount++
      }
    }
    console.log(`   ✅ Created ${paymentCount} scheme payments`)

    // Step 5: Seed Repairs
    console.log('\n🔧 Step 5: Seeding repairs...')
    
    const repairs = [
      {
        id: randomUUID(),
        number: `REP/${financialYear}/001`,
        customer_id: customerMap.get('TRP000003')?.id,
        customer_name: 'Murugan Raman',
        customer_phone: '+91-9876543303',
        item_description: 'Gold chain with broken clasp - needs repair and polishing',
        repair_type: 'clasp_repair',
        estimated_cost: 2500.00,
        actual_cost: 2200.00,
        advance_amount: 1000.00,
        status: 'completed'
      },
      {
        id: randomUUID(),
        number: `REP/${financialYear}/002`,
        customer_id: customerMap.get('TRP000004')?.id,
        customer_name: 'Kavitha Devi',
        customer_phone: '+91-9876543304',
        item_description: 'Silver bangles - cleaning and polishing required',
        repair_type: 'cleaning',
        estimated_cost: 800.00,
        actual_cost: 750.00,
        advance_amount: 400.00,
        status: 'in_progress'
      }
    ]

    for (const repair of repairs) {
      await connection.execute(`
        INSERT INTO repairs (
          id, repair_number, customer_id, customer_name, customer_phone,
          item_description, item_type, metal_type, repair_type, repair_description,
          estimated_cost, actual_cost, advance_amount, received_date, promised_date,
          status, priority, payment_status, received_by, assigned_to, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'jewelry', 'gold', ?, ?, ?, ?, ?, 
                  DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 2 DAY),
                  ?, 'normal', 'advance_paid', ?, ?, NOW(), NOW())
      `, [
        repair.id, repair.number, repair.customer_id, repair.customer_name, repair.customer_phone,
        repair.item_description, repair.repair_type, repair.item_description,
        repair.estimated_cost, repair.actual_cost, repair.advance_amount,
        repair.status, userMap.get('admin'), userMap.get('admin')
      ])
    }
    console.log(`   ✅ Created ${repairs.length} repairs`)

    console.log('\n🎉 All Missing Table Data Seeded Successfully!')

    // Generate summary
    const [summary] = await connection.execute(`
      SELECT 
        (SELECT COUNT(*) FROM purchases) as total_purchases,
        (SELECT COUNT(*) FROM purchase_items) as total_purchase_items,
        (SELECT COUNT(*) FROM schemes) as total_schemes,
        (SELECT COUNT(*) FROM scheme_payments) as total_scheme_payments,
        (SELECT COUNT(*) FROM repairs) as total_repairs
    `)

    const stats = (summary as any[])[0]
    console.log('\n📊 Data Summary:')
    console.log(`   Purchases: ${stats.total_purchases}`)
    console.log(`   Purchase Items: ${stats.total_purchase_items}`)
    console.log(`   Schemes: ${stats.total_schemes}`)
    console.log(`   Scheme Payments: ${stats.total_scheme_payments}`)
    console.log(`   Repairs: ${stats.total_repairs}`)

  } catch (error) {
    console.error('\n❌ Missing table data seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the missing table data seeding
seedMissingTableData().catch(console.error)
