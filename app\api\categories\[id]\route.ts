import { NextRequest, NextResponse } from "next/server"
import { categoryService } from "@/lib/database/services"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const category = await categoryService.findById(params.id)

    if (!category) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    return NextResponse.json({
      category,
      success: true
    })
  } catch (error) {
    console.error("Error fetching category:", error)
    return NextResponse.json(
      { error: "Failed to fetch category", success: false },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { category: updateData } = await request.json()

    if (!updateData) {
      return NextResponse.json(
        { error: "Category data is required", success: false },
        { status: 400 }
      )
    }

    // Check if category exists
    const existingCategory = await categoryService.findById(params.id)
    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    // Check if new name conflicts with existing categories (excluding current one)
    if (updateData.name) {
      const categoriesWithSameName = await categoryService.findByName(updateData.name)
      const conflictingCategory = categoriesWithSameName.find(
        cat => cat.id !== params.id &&
        cat.name.toLowerCase() === updateData.name.toLowerCase()
      )

      if (conflictingCategory) {
        return NextResponse.json(
          { error: "Category name already exists", success: false },
          { status: 400 }
        )
      }
    }

    const updatedCategory = await categoryService.update(params.id, updateData)

    if (!updatedCategory) {
      return NextResponse.json(
        { error: "Failed to update category", success: false },
        { status: 500 }
      )
    }

    return NextResponse.json({
      category: updatedCategory,
      success: true
    })
  } catch (error) {
    console.error("Error updating category:", error)
    return NextResponse.json(
      { error: "Failed to update category", success: false },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if category exists
    const existingCategory = await categoryService.findById(params.id)
    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    // TODO: Check if category is being used by any inventory items
    // This would require a query to check inventory table
    // For now, we'll allow deletion

    const success = await categoryService.delete(params.id)

    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete category", success: false },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Category deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting category:", error)
    return NextResponse.json(
      { error: "Failed to delete category", success: false },
      { status: 500 }
    )
  }
}
