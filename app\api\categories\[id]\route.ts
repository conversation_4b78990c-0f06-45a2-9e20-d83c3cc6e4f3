import { NextRequest, NextResponse } from "next/server"
import { Category } from "@/lib/types"

// This would normally come from a database
// For now, we'll use the same mock data structure
const mockCategories: Category[] = [
  {
    id: "cat_1",
    name: "Rings",
    description: "Wedding rings, engagement rings, and fashion rings",
    isActive: true,
    sortOrder: 1,
    tags: ["wedding", "engagement", "fashion"],
    makingChargePercentage: 15,
    wastagePercentage: 2,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_2",
    name: "Necklaces",
    description: "Gold and silver necklaces, chains, and pendants",
    isActive: true,
    sortOrder: 2,
    tags: ["chains", "pendants", "traditional"],
    makingChargePercentage: 12,
    wastagePercentage: 1.5,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  // Add other categories as needed
]

let categories = [...mockCategories]

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const category = categories.find(cat => cat.id === params.id)

    if (!category) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    return NextResponse.json({ 
      category, 
      success: true 
    })
  } catch (error) {
    console.error("Error fetching category:", error)
    return NextResponse.json(
      { error: "Failed to fetch category", success: false },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { category: updateData } = await request.json()

    if (!updateData) {
      return NextResponse.json(
        { error: "Category data is required", success: false },
        { status: 400 }
      )
    }

    const categoryIndex = categories.findIndex(cat => cat.id === params.id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    // Check if new name conflicts with existing categories (excluding current one)
    if (updateData.name) {
      const existingCategory = categories.find(
        cat => cat.id !== params.id && 
        cat.name.toLowerCase() === updateData.name.toLowerCase()
      )

      if (existingCategory) {
        return NextResponse.json(
          { error: "Category name already exists", success: false },
          { status: 400 }
        )
      }
    }

    const updatedCategory: Category = {
      ...categories[categoryIndex],
      ...updateData,
      id: params.id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString(),
    }

    categories[categoryIndex] = updatedCategory

    return NextResponse.json({ 
      category: updatedCategory, 
      success: true 
    })
  } catch (error) {
    console.error("Error updating category:", error)
    return NextResponse.json(
      { error: "Failed to update category", success: false },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryIndex = categories.findIndex(cat => cat.id === params.id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: "Category not found", success: false },
        { status: 404 }
      )
    }

    // Check if category is being used by any inventory items
    // This would normally be a database query
    // For now, we'll just allow deletion
    
    categories.splice(categoryIndex, 1)

    return NextResponse.json({ 
      success: true,
      message: "Category deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting category:", error)
    return NextResponse.json(
      { error: "Failed to delete category", success: false },
      { status: 500 }
    )
  }
}
