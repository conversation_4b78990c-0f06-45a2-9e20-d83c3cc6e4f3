"use client"

import { useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { InvoiceTemplate } from "./invoice-template"
import { Sale, Customer, InventoryItem } from "@/lib/types"
import { useStore } from "@/lib/store"
import { Printer, Download, Eye, FileText } from "lucide-react"
// import { useReactToPrint } from "react-to-print"
// import html2canvas from "html2canvas"
// import jsPDF from "jspdf"

interface InvoiceActionsProps {
  sale: Sale
  customer: Customer
  items: Array<{
    item: InventoryItem
    quantity: number
    weight: number
    rate: number
    amount: number
  }>
}

export function InvoiceActions({ sale, customer, items }: InvoiceActionsProps) {
  const { settings } = useStore()
  const [selectedTemplate, setSelectedTemplate] = useState<"standard" | "detailed" | "minimal">("standard")
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const invoiceRef = useRef<HTMLDivElement>(null)

  const handlePrint = () => {
    if (!invoiceRef.current) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = invoiceRef.current.innerHTML
    const pageStyle = `
      <style>
        @page {
          size: ${selectedTemplate === "minimal" ? "80mm auto" : "A4"};
          margin: ${selectedTemplate === "minimal" ? "5mm" : "10mm"};
        }
        @media print {
          body { -webkit-print-color-adjust: exact; }
          .no-print { display: none !important; }
        }
        body { font-family: system-ui, -apple-system, sans-serif; }
        .bg-yellow-600 { color: #d97706 !important; }
        .bg-gray-50 { background-color: #f9fafb !important; }
        .border { border: 1px solid #e5e7eb !important; }
        .border-b { border-bottom: 1px solid #e5e7eb !important; }
        .border-t { border-top: 1px solid #e5e7eb !important; }
        .text-yellow-600 { color: #d97706 !important; }
        .text-gray-600 { color: #4b5563 !important; }
        .text-gray-500 { color: #6b7280 !important; }
        .font-bold { font-weight: bold !important; }
        .font-medium { font-weight: 500 !important; }
        .font-semibold { font-weight: 600 !important; }
        .text-xs { font-size: 0.75rem !important; }
        .text-sm { font-size: 0.875rem !important; }
        .text-lg { font-size: 1.125rem !important; }
        .text-xl { font-size: 1.25rem !important; }
        .text-2xl { font-size: 1.5rem !important; }
        .text-3xl { font-size: 1.875rem !important; }
        .p-2 { padding: 0.5rem !important; }
        .p-4 { padding: 1rem !important; }
        .p-6 { padding: 1.5rem !important; }
        .p-8 { padding: 2rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-4 { margin-bottom: 1rem !important; }
        .mb-6 { margin-bottom: 1.5rem !important; }
        .mb-8 { margin-bottom: 2rem !important; }
        .mt-2 { margin-top: 0.5rem !important; }
        .mt-4 { margin-top: 1rem !important; }
        .mt-8 { margin-top: 2rem !important; }
        .text-center { text-align: center !important; }
        .text-right { text-align: right !important; }
        .text-left { text-align: left !important; }
        .flex { display: flex !important; }
        .justify-between { justify-content: space-between !important; }
        .justify-end { justify-content: flex-end !important; }
        .items-center { align-items: center !important; }
        .items-start { align-items: flex-start !important; }
        .space-y-1 > * + * { margin-top: 0.25rem !important; }
        .space-y-2 > * + * { margin-top: 0.5rem !important; }
        .space-y-4 > * + * { margin-top: 1rem !important; }
        .space-y-6 > * + * { margin-top: 1.5rem !important; }
        .w-full { width: 100% !important; }
        .max-w-sm { max-width: 24rem !important; }
        .max-w-2xl { max-width: 42rem !important; }
        .max-w-4xl { max-width: 56rem !important; }
        .mx-auto { margin-left: auto !important; margin-right: auto !important; }
        .bg-white { background-color: white !important; }
        .rounded { border-radius: 0.25rem !important; }
        .truncate { overflow: hidden !important; text-overflow: ellipsis !important; white-space: nowrap !important; }
        table { border-collapse: collapse !important; width: 100% !important; }
        th, td { border: 1px solid #e5e7eb !important; padding: 0.5rem !important; }
        th { background-color: #f9fafb !important; }
        .grid { display: grid !important; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
        .gap-2 { gap: 0.5rem !important; }
        .gap-4 { gap: 1rem !important; }
        .gap-8 { gap: 2rem !important; }
      </style>
    `

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice-${sale.invoiceNumber}</title>
          ${pageStyle}
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `)

    printWindow.document.close()
    printWindow.focus()

    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 250)
  }

  const handleDownloadPDF = async () => {
    // For now, we'll use the print functionality
    // PDF generation will be implemented later with proper dependencies
    alert("PDF download functionality will be available soon. Please use Print for now.")
    handlePrint()
  }

  const handleEmailInvoice = async () => {
    // This would integrate with an email service
    alert("Email functionality would be implemented here with your email service provider")
  }

  const templateOptions = [
    { value: "standard", label: "Standard Invoice", description: "Professional A4 format" },
    { value: "detailed", label: "Detailed Invoice", description: "Comprehensive with all details" },
    { value: "minimal", label: "Thermal Receipt", description: "Compact 80mm thermal printer format" },
  ]

  return (
    <div className="flex gap-2">
      {/* Template Selection */}
      <div className="flex items-center gap-2">
        <Label htmlFor="template">Template:</Label>
        <Select value={selectedTemplate} onValueChange={(value: any) => setSelectedTemplate(value)}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {templateOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                <div>
                  <div className="font-medium">{option.label}</div>
                  <div className="text-xs text-muted-foreground">{option.description}</div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Preview Button */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Invoice Preview - {templateOptions.find(t => t.value === selectedTemplate)?.label}</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <InvoiceTemplate
              ref={invoiceRef}
              sale={sale}
              customer={customer}
              items={items}
              template={selectedTemplate}
            />
          </div>
          <div className="flex justify-end gap-2 mt-4 no-print">
            <Button onClick={handlePrint} variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button onClick={handleDownloadPDF} disabled={isGenerating}>
              <Download className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Download PDF"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Print Button */}
      <Button onClick={handlePrint} variant="outline" size="sm">
        <Printer className="h-4 w-4 mr-2" />
        Print
      </Button>

      {/* Download PDF Button */}
      <Button onClick={handleDownloadPDF} disabled={isGenerating} variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        {isGenerating ? "Generating..." : "PDF"}
      </Button>

      {/* Email Button */}
      <Button onClick={handleEmailInvoice} variant="outline" size="sm">
        <FileText className="h-4 w-4 mr-2" />
        Email
      </Button>

      {/* Hidden invoice for printing */}
      <div className="hidden">
        <InvoiceTemplate
          ref={invoiceRef}
          sale={sale}
          customer={customer}
          items={items}
          template={selectedTemplate}
        />
      </div>
    </div>
  )
}
