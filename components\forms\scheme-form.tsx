"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useStore } from "@/lib/store"
import type { Scheme } from "@/lib/types"

interface SchemeFormProps {
  scheme?: Scheme
  onSubmit: () => void
  onCancel: () => void
}

export function SchemeForm({ scheme, onSubmit, onCancel }: SchemeFormProps) {
  const { addScheme, updateScheme, customers } = useStore()
  const [formData, setFormData] = useState({
    name: "",
    customerId: "",
    totalAmount: "",
    paidAmount: "",
    monthlyAmount: "",
    duration: "",
    startDate: "",
    status: "active",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (scheme) {
      setFormData({
        name: scheme.name,
        customerId: scheme.customer.id,
        totalAmount: scheme.totalAmount.toString(),
        paidAmount: scheme.paidAmount.toString(),
        monthlyAmount: scheme.monthlyAmount.toString(),
        duration: scheme.duration.toString(),
        startDate: scheme.startDate,
        status: scheme.status,
      })
    } else {
      // Set default start date to today
      const today = new Date().toISOString().split("T")[0]
      setFormData((prev) => ({ ...prev, startDate: today }))
    }
  }, [scheme])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Scheme name is required"
    }

    if (!formData.customerId) {
      newErrors.customerId = "Customer is required"
    }

    if (!formData.totalAmount || Number.parseFloat(formData.totalAmount) <= 0) {
      newErrors.totalAmount = "Total amount must be greater than 0"
    }

    if (Number.parseFloat(formData.paidAmount) < 0) {
      newErrors.paidAmount = "Paid amount cannot be negative"
    }

    if (Number.parseFloat(formData.paidAmount) > Number.parseFloat(formData.totalAmount)) {
      newErrors.paidAmount = "Paid amount cannot exceed total amount"
    }

    if (!formData.monthlyAmount || Number.parseFloat(formData.monthlyAmount) <= 0) {
      newErrors.monthlyAmount = "Monthly amount must be greater than 0"
    }

    if (!formData.duration || Number.parseInt(formData.duration) <= 0) {
      newErrors.duration = "Duration must be greater than 0"
    }

    if (!formData.startDate) {
      newErrors.startDate = "Start date is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const customer = customers.find((c) => c.id === formData.customerId)
      if (!customer) {
        setErrors({ submit: "Selected customer not found" })
        return
      }

      const schemeData = {
        name: formData.name.trim(),
        customer,
        totalAmount: Number.parseFloat(formData.totalAmount),
        paidAmount: Number.parseFloat(formData.paidAmount) || 0,
        monthlyAmount: Number.parseFloat(formData.monthlyAmount),
        duration: Number.parseInt(formData.duration),
        startDate: formData.startDate,
        status: formData.status as "active" | "completed" | "cancelled",
      }

      if (scheme) {
        await updateScheme(scheme.id, schemeData)
      } else {
        await addScheme(schemeData)
      }

      // Reset form
      setFormData({
        name: "",
        customerId: "",
        totalAmount: "",
        paidAmount: "",
        monthlyAmount: "",
        duration: "",
        startDate: "",
        status: "active",
      })
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving scheme:", error)
      setErrors({ submit: "Failed to save scheme. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Scheme Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Gold Savings Scheme"
            required
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="customerId">Customer *</Label>
          <Select value={formData.customerId || undefined} onValueChange={(value) => handleInputChange("customerId", value)}>
            <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
              <SelectValue placeholder="Select customer" />
            </SelectTrigger>
            <SelectContent>
              {customers.filter(customer => customer.id && customer.id.trim() !== "").map((customer) => (
                <SelectItem key={customer.id} value={customer.id}>
                  {customer.name} - {customer.phone}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="totalAmount">Total Amount (₹) *</Label>
          <Input
            id="totalAmount"
            type="number"
            step="0.01"
            min="0"
            value={formData.totalAmount}
            onChange={(e) => handleInputChange("totalAmount", e.target.value)}
            placeholder="100000"
            required
            className={errors.totalAmount ? "border-red-500" : ""}
          />
          {errors.totalAmount && <p className="text-sm text-red-500">{errors.totalAmount}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="paidAmount">Paid Amount (₹)</Label>
          <Input
            id="paidAmount"
            type="number"
            step="0.01"
            min="0"
            value={formData.paidAmount}
            onChange={(e) => handleInputChange("paidAmount", e.target.value)}
            placeholder="25000"
            className={errors.paidAmount ? "border-red-500" : ""}
          />
          {errors.paidAmount && <p className="text-sm text-red-500">{errors.paidAmount}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="monthlyAmount">Monthly Amount (₹) *</Label>
          <Input
            id="monthlyAmount"
            type="number"
            step="0.01"
            min="0"
            value={formData.monthlyAmount}
            onChange={(e) => handleInputChange("monthlyAmount", e.target.value)}
            placeholder="5000"
            required
            className={errors.monthlyAmount ? "border-red-500" : ""}
          />
          {errors.monthlyAmount && <p className="text-sm text-red-500">{errors.monthlyAmount}</p>}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="duration">Duration (months) *</Label>
          <Input
            id="duration"
            type="number"
            min="1"
            value={formData.duration}
            onChange={(e) => handleInputChange("duration", e.target.value)}
            placeholder="12"
            required
            className={errors.duration ? "border-red-500" : ""}
          />
          {errors.duration && <p className="text-sm text-red-500">{errors.duration}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="startDate">Start Date *</Label>
          <Input
            id="startDate"
            type="date"
            value={formData.startDate}
            onChange={(e) => handleInputChange("startDate", e.target.value)}
            required
            className={errors.startDate ? "border-red-500" : ""}
          />
          {errors.startDate && <p className="text-sm text-red-500">{errors.startDate}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {formData.totalAmount && formData.monthlyAmount && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-700">
            <strong>Calculation:</strong> ₹{formData.monthlyAmount}/month × {formData.duration} months = ₹
            {(Number.parseFloat(formData.monthlyAmount) * Number.parseInt(formData.duration)).toLocaleString()}
          </p>
          {formData.paidAmount && (
            <p className="text-sm text-blue-700">
              <strong>Progress:</strong>{" "}
              {Math.round((Number.parseFloat(formData.paidAmount) / Number.parseFloat(formData.totalAmount)) * 100)}%
              completed
            </p>
          )}
        </div>
      )}

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : scheme ? "Update Scheme" : "Create Scheme"}
        </Button>
      </div>
    </form>
  )
}
