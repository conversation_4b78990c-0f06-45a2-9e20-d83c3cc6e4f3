#!/usr/bin/env node

/**
 * Database Setup Script for Exchange System
 * This script sets up the complete database schema and sample data
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Database configuration
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'jewellers_db',
  multipleStatements: true
};

async function runSQLFile(connection, filePath) {
  try {
    console.log(`📄 Running SQL file: ${filePath}`);
    const sql = await fs.readFile(filePath, 'utf8');

    // Split by semicolon and execute each statement
    const statements = sql.split(';').filter(stmt => {
      const trimmed = stmt.trim();
      return trimmed.length > 0 &&
             !trimmed.startsWith('--') &&
             !trimmed.startsWith('/*') &&
             !trimmed.toLowerCase().includes('commit');
    });

    for (const statement of statements) {
      const trimmed = statement.trim();
      if (trimmed && !trimmed.toLowerCase().startsWith('select ')) {
        try {
          await connection.execute(trimmed);
        } catch (error) {
          // Skip non-critical errors like duplicate entries
          if (!error.message.includes('Duplicate entry') &&
              !error.message.includes('already exists')) {
            console.warn(`⚠️  Warning in statement: ${error.message}`);
          }
        }
      }
    }

    console.log(`✅ Successfully executed: ${path.basename(filePath)}`);
  } catch (error) {
    console.error(`❌ Error executing ${filePath}:`, error.message);
    throw error;
  }
}

async function checkDatabaseExists(connection, dbName) {
  try {
    const [rows] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [dbName]
    );
    return rows.length > 0;
  } catch (error) {
    return false;
  }
}

async function createDatabase(connection, dbName) {
  try {
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
    console.log(`✅ Database '${dbName}' created/verified`);
  } catch (error) {
    console.error(`❌ Error creating database:`, error.message);
    throw error;
  }
}

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🚀 Starting Exchange System Database Setup...\n');
    
    // Connect to MySQL (without database first)
    console.log('📡 Connecting to MySQL server...');
    connection = await mysql.createConnection({
      host: DB_CONFIG.host,
      user: DB_CONFIG.user,
      password: DB_CONFIG.password,
      multipleStatements: true
    });
    console.log('✅ Connected to MySQL server\n');
    
    // Create database if it doesn't exist
    console.log(`🗄️  Setting up database: ${DB_CONFIG.database}`);
    await createDatabase(connection, DB_CONFIG.database);
    
    // Switch to the target database
    await connection.execute(`USE \`${DB_CONFIG.database}\``);
    console.log(`✅ Using database: ${DB_CONFIG.database}\n`);
    
    // Check if core tables exist
    console.log('🔍 Checking existing tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    const existingTables = tables.map(row => Object.values(row)[0]);
    console.log(`📊 Found ${existingTables.length} existing tables\n`);
    
    // Run migration scripts
    console.log('📋 Running migration scripts...\n');
    
    const migrationFiles = [
      path.join(__dirname, 'migrations', '001_create_exchange_tables.sql'),
      path.join(__dirname, 'migrations', '002_insert_sample_data.sql')
    ];
    
    for (const file of migrationFiles) {
      try {
        await fs.access(file);
        await runSQLFile(connection, file);
      } catch (error) {
        if (error.code === 'ENOENT') {
          console.log(`⚠️  Migration file not found: ${path.basename(file)}`);
        } else {
          throw error;
        }
      }
    }
    
    // Verify setup
    console.log('\n🔍 Verifying database setup...');
    
    // Check exchange tables
    const exchangeTables = [
      'exchange_rates',
      'exchange_transactions', 
      'exchange_items',
      'exchange_purchase_bills',
      'sales_exchange_items',
      'exchange_audit_trail',
      'bill_sequences'
    ];
    
    for (const table of exchangeTables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = rows[0].count;
        console.log(`✅ Table '${table}': ${count} records`);
      } catch (error) {
        console.log(`❌ Table '${table}': Not found or error`);
      }
    }
    
    // Show sample data summary
    console.log('\n📊 Sample Data Summary:');
    try {
      const [customers] = await connection.execute(
        "SELECT COUNT(*) as count FROM customers WHERE id LIKE 'cust_%'"
      );
      console.log(`👥 Sample Customers: ${customers[0].count}`);
      
      const [transactions] = await connection.execute(
        "SELECT COUNT(*) as count FROM exchange_transactions WHERE id LIKE 'exg_%'"
      );
      console.log(`🔄 Sample Exchange Transactions: ${transactions[0].count}`);
      
      const [bills] = await connection.execute(
        "SELECT COUNT(*) as count FROM exchange_purchase_bills WHERE id LIKE 'bill_%'"
      );
      console.log(`📄 Sample Purchase Bills: ${bills[0].count}`);
      
      const [rates] = await connection.execute(
        "SELECT COUNT(*) as count FROM exchange_rates WHERE is_active = TRUE"
      );
      console.log(`💰 Active Exchange Rates: ${rates[0].count}`);
      
    } catch (error) {
      console.log('⚠️  Could not retrieve sample data summary');
    }
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Start the application: npm run dev');
    console.log('2. Navigate to the Exchange tab');
    console.log('3. Try the Demo to see the complete workflow');
    console.log('4. Create test exchange transactions');
    console.log('5. Test purchase bill generation');
    console.log('6. Try sales with exchange integration\n');
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check MySQL server is running');
    console.error('2. Verify database credentials in .env file');
    console.error('3. Ensure user has CREATE/INSERT/UPDATE permissions');
    console.error('4. Check if database already exists with conflicting data\n');
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('📡 Database connection closed');
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Exchange System Database Setup

Usage: node setup-database.js [options]

Options:
  --help, -h     Show this help message
  --force        Force recreation of tables (drops existing data)
  --sample-only  Only insert sample data (skip table creation)

Environment Variables:
  DB_HOST        Database host (default: localhost)
  DB_USER        Database user (default: root)
  DB_PASSWORD    Database password (default: empty)
  DB_NAME        Database name (default: jewellers_db)

Examples:
  node setup-database.js
  DB_PASSWORD=mypass node setup-database.js
  node setup-database.js --force
`);
  process.exit(0);
}

// Run the setup
setupDatabase().catch(console.error);
