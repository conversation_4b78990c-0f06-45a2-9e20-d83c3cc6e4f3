// Data mapping service for Shree Jewellers system
// Maps database fields to component fields for backward compatibility

import { safeNumber, safeString, formatDate } from '@/lib/utils/safe-values'

/**
 * Maps database inventory item to component-compatible format
 */
export const mapInventoryItem = (dbItem: any): any => {
  if (!dbItem) return {}
  
  return {
    // Keep all original database fields
    ...dbItem,
    
    // Add computed fields
    net_weight: safeNumber(dbItem.gross_weight, 0) - safeNumber(dbItem.stone_weight, 0),
    
    // Legacy field mappings for backward compatibility
    stoneAmount: safeNumber(dbItem.stone_charges, 0),
    currentValue: safeNumber(dbItem.selling_price, 0),
    stock: safeNumber(dbItem.stock_quantity, 0),
    stoneDetails: safeString(dbItem.description, ''),
    hsnCode: safeString(dbItem.hsn_code, ''),
    metalType: safeString(dbItem.metal_type, ''),
    grossWeight: safeNumber(dbItem.gross_weight, 0),
    stoneWeight: safeNumber(dbItem.stone_weight, 0),
    netWeight: safeNumber(dbItem.gross_weight, 0) - safeNumber(dbItem.stone_weight, 0),
    makingCharges: safeNumber(dbItem.making_charges, 0),
    createdAt: formatDate(dbItem.created_at),
    updatedAt: formatDate(dbItem.updated_at),
  }
}

/**
 * Maps database customer to component-compatible format
 */
export const mapCustomer = (dbCustomer: any): any => {
  if (!dbCustomer) return {}
  
  const firstName = safeString(dbCustomer.first_name, '')
  const lastName = safeString(dbCustomer.last_name, '')
  const fullName = `${firstName} ${lastName}`.trim() || safeString(dbCustomer.name, 'Unknown Customer')
  
  return {
    // Keep all original database fields
    ...dbCustomer,
    
    // Add computed fields
    name: fullName,
    
    // Legacy field mappings for backward compatibility
    gstNumber: safeString(dbCustomer.gst_number, ''),
    totalPurchases: safeNumber(dbCustomer.total_purchases, 0),
    lastVisit: formatDate(dbCustomer.last_purchase_date, 'Never'),
    createdAt: formatDate(dbCustomer.created_at),
    updatedAt: formatDate(dbCustomer.updated_at),
  }
}

/**
 * Maps database sale to component-compatible format
 */
export const mapSale = (dbSale: any): any => {
  if (!dbSale) return {}
  
  return {
    // Keep all original database fields
    ...dbSale,
    
    // Add computed fields
    total: safeNumber(dbSale.final_amount || dbSale.grand_total || dbSale.total, 0),
    
    // Legacy field mappings for backward compatibility
    date: formatDate(dbSale.invoice_date || dbSale.created_at),
    createdAt: formatDate(dbSale.created_at),
    updatedAt: formatDate(dbSale.updated_at),
    exchangeDeduction: safeNumber(dbSale.exchange_deduction, 0),
    finalTotal: safeNumber(dbSale.final_amount, 0),
  }
}

/**
 * Maps database purchase to component-compatible format
 */
export const mapPurchase = (dbPurchase: any): any => {
  if (!dbPurchase) return {}
  
  return {
    // Keep all original database fields
    ...dbPurchase,
    
    // Add computed fields
    amount: safeNumber(dbPurchase.grand_total || dbPurchase.total_amount || dbPurchase.amount, 0),
    
    // Legacy field mappings for backward compatibility
    date: formatDate(dbPurchase.purchase_date || dbPurchase.created_at),
    supplier: safeString(dbPurchase.supplier_name, 'Unknown Supplier'),
    createdAt: formatDate(dbPurchase.created_at),
    updatedAt: formatDate(dbPurchase.updated_at),
  }
}

/**
 * Maps database scheme to component-compatible format
 */
export const mapScheme = (dbScheme: any): any => {
  if (!dbScheme) return {}
  
  return {
    // Keep all original database fields
    ...dbScheme,
    
    // Add computed fields
    totalAmount: safeNumber(dbScheme.total_amount, 0),
    paidAmount: safeNumber(dbScheme.total_paid, 0),
    balanceAmount: safeNumber(dbScheme.balance_amount, 0),
    
    // Legacy field mappings for backward compatibility
    customerName: safeString(dbScheme.customer_name, 'Unknown Customer'),
    schemeName: safeString(dbScheme.scheme_name, 'Unknown Scheme'),
    startDate: formatDate(dbScheme.start_date),
    maturityDate: formatDate(dbScheme.maturity_date),
    createdAt: formatDate(dbScheme.created_at),
    updatedAt: formatDate(dbScheme.updated_at),
  }
}

/**
 * Maps database repair to component-compatible format
 */
export const mapRepair = (dbRepair: any): any => {
  if (!dbRepair) return {}
  
  return {
    // Keep all original database fields
    ...dbRepair,
    
    // Add computed fields
    estimatedCost: safeNumber(dbRepair.estimated_cost, 0),
    actualCost: safeNumber(dbRepair.actual_cost, 0),
    advanceAmount: safeNumber(dbRepair.advance_amount, 0),
    balanceAmount: safeNumber(dbRepair.balance_amount, 0),
    
    // Legacy field mappings for backward compatibility
    customerName: safeString(dbRepair.customer_name, 'Unknown Customer'),
    itemDescription: safeString(dbRepair.item_description, 'No description'),
    repairType: safeString(dbRepair.repair_type, 'general'),
    receivedDate: formatDate(dbRepair.received_date),
    promisedDate: formatDate(dbRepair.promised_date),
    completedDate: formatDate(dbRepair.completed_date),
    createdAt: formatDate(dbRepair.created_at),
    updatedAt: formatDate(dbRepair.updated_at),
  }
}

/**
 * Maps database user to component-compatible format
 */
export const mapUser = (dbUser: any): any => {
  if (!dbUser) return {}
  
  const firstName = safeString(dbUser.first_name, '')
  const lastName = safeString(dbUser.last_name, '')
  const fullName = `${firstName} ${lastName}`.trim() || safeString(dbUser.name, 'Unknown User')
  
  return {
    // Keep all original database fields
    ...dbUser,
    
    // Add computed fields
    name: fullName,
    
    // Parse permissions if they're stored as JSON string
    permissions: dbUser.permissions ? 
      (typeof dbUser.permissions === 'string' ? 
        JSON.parse(dbUser.permissions) : dbUser.permissions) : null,
    
    // Legacy field mappings for backward compatibility
    createdAt: formatDate(dbUser.created_at),
    updatedAt: formatDate(dbUser.updated_at),
  }
}

/**
 * Maps an array of database records using the appropriate mapper
 */
export const mapArray = <T>(items: any[], mapper: (item: any) => T): T[] => {
  if (!Array.isArray(items)) return []
  return items.map(mapper)
}

/**
 * Generic mapper that handles common database to component field mappings
 */
export const mapGeneric = (dbItem: any): any => {
  if (!dbItem) return {}
  
  return {
    // Keep all original database fields
    ...dbItem,
    
    // Common legacy field mappings
    createdAt: formatDate(dbItem.created_at),
    updatedAt: formatDate(dbItem.updated_at),
  }
}

/**
 * Maps database response to component-compatible format based on type
 */
export const mapResponse = (data: any, type: string): any => {
  if (!data) return data
  
  if (Array.isArray(data)) {
    switch (type) {
      case 'inventory':
        return mapArray(data, mapInventoryItem)
      case 'customers':
        return mapArray(data, mapCustomer)
      case 'sales':
        return mapArray(data, mapSale)
      case 'purchases':
        return mapArray(data, mapPurchase)
      case 'schemes':
        return mapArray(data, mapScheme)
      case 'repairs':
        return mapArray(data, mapRepair)
      case 'users':
        return mapArray(data, mapUser)
      default:
        return mapArray(data, mapGeneric)
    }
  } else {
    switch (type) {
      case 'inventory':
        return mapInventoryItem(data)
      case 'customer':
        return mapCustomer(data)
      case 'sale':
        return mapSale(data)
      case 'purchase':
        return mapPurchase(data)
      case 'scheme':
        return mapScheme(data)
      case 'repair':
        return mapRepair(data)
      case 'user':
        return mapUser(data)
      default:
        return mapGeneric(data)
    }
  }
}
