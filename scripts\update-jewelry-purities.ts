/**
 * <PERSON>ript to update jewelry purities to only include retail-relevant ones
 * Updates existing inventory items and settings to use only 22K, 18K gold and 925 silver
 */

import mysql from 'mysql2/promise'

interface InventoryRow {
  id: string
  name: string
  category: string
  metal_type: string
  purity: string
  [key: string]: any
}

async function updateJewelryPurities() {
  let connection: mysql.Connection | undefined

  try {
    // Database connection configuration
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jjjewellers_db'
    }

    console.log('🔄 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Start transaction
    await connection.beginTransaction()

    try {
      // Update inventory items with non-standard purities
      console.log('🔄 Updating inventory item purities...')
      
      // Map old purities to new ones
      const purityMapping: Record<string, string> = {
        '24K': '22K',  // Convert 24K to 22K (most common retail gold)
        '14K': '18K',  // Convert 14K to 18K
        '10K': '18K',  // Convert 10K to 18K
        '999': '925',  // Convert 999 silver to 925 (sterling silver)
        '916': '22K',  // Convert 916 to 22K (same purity, different notation)
        '750': '18K',  // Convert 750 to 18K (same purity, different notation)
        '585': '18K',  // Convert 585 to 18K
        '375': '18K'   // Convert 375 to 18K
      }

      let updatedCount = 0
      for (const [oldPurity, newPurity] of Object.entries(purityMapping)) {
        const [result] = await connection.execute(
          'UPDATE inventory SET purity = ?, updated_at = ? WHERE purity = ?',
          [newPurity, new Date().toISOString(), oldPurity]
        )
        const affectedRows = (result as any).affectedRows
        if (affectedRows > 0) {
          console.log(`   Updated ${affectedRows} items from ${oldPurity} to ${newPurity}`)
          updatedCount += affectedRows
        }
      }

      // Update settings to use new metal rates structure
      console.log('🔄 Updating settings metal rates...')
      
      const newMetalRates = JSON.stringify({
        gold: { "22K": "642", "18K": "525" },
        silver: { "925": "78.5" }
      })

      await connection.execute(
        'UPDATE settings SET metal_rates = ?, updated_at = ? WHERE id = 1',
        [newMetalRates, new Date().toISOString()]
      )

      // Commit transaction
      await connection.commit()
      console.log('✅ Transaction committed successfully')

      // Verify the updates
      console.log('\n🔄 Verifying updates...')
      
      // Check inventory purities
      const [inventoryRows] = await connection.execute(
        'SELECT purity, COUNT(*) as count FROM inventory GROUP BY purity ORDER BY purity'
      )
      
      console.log('\n📊 Current inventory purities:')
      console.table(inventoryRows)

      // Check settings
      const [settingsRows] = await connection.execute('SELECT metal_rates FROM settings WHERE id = 1')
      const settings = settingsRows as any[]
      if (settings.length > 0) {
        console.log('\n⚙️  Updated metal rates:')
        console.log(JSON.stringify(JSON.parse(settings[0].metal_rates), null, 2))
      }

      console.log(`\n✅ Successfully updated ${updatedCount} inventory items!`)
      console.log('🎉 All items now use jewelry retail standard purities')

    } catch (error) {
      // Rollback on error
      await connection.rollback()
      throw error
    }

  } catch (error) {
    console.error('❌ Update failed:', (error as Error).message)
    console.error((error as Error).stack)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

// Check if this script is being run directly
if (require.main === module) {
  console.log('🚀 Starting Jewelry Purity Update')
  console.log('==================================')
  updateJewelryPurities()
    .then(() => {
      console.log('\n🎊 Update completed successfully!')
      console.log('All jewelry items now use retail standard purities!')
      console.log('• Gold: 22K and 18K only')
      console.log('• Silver: 925 (Sterling) only')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Update failed:', error)
      process.exit(1)
    })
}

export { updateJewelryPurities }
