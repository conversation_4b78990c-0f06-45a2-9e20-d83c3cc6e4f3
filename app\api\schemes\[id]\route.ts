import { NextRequest, NextResponse } from 'next/server'
import { schemeService } from '@/lib/database/services'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheme = await schemeService.findById(params.id)
    if (!scheme) {
      return NextResponse.json(
        { error: 'Scheme not found' },
        { status: 404 }
      )
    }
    return NextResponse.json({ scheme })
  } catch (error) {
    console.error('Error fetching scheme:', error)
    return NextResponse.json(
      { error: 'Failed to fetch scheme' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const schemeData = await request.json()
    const success = await schemeService.update(params.id, schemeData)
    if (!success) {
      return NextResponse.json(
        { error: 'Scheme not found' },
        { status: 404 }
      )
    }
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating scheme:', error)
    return NextResponse.json(
      { error: 'Failed to update scheme' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await schemeService.delete(params.id)
    if (!success) {
      return NextResponse.json(
        { error: 'Scheme not found' },
        { status: 404 }
      )
    }
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting scheme:', error)
    return NextResponse.json(
      { error: 'Failed to delete scheme' },
      { status: 500 }
    )
  }
}
