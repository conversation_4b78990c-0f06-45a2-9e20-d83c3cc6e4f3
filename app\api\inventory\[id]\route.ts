import { NextRequest, NextResponse } from 'next/server'
import { inventoryService } from '@/lib/database/services'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const updates = await request.json()
    const item = await inventoryService.update(params.id, updates)
    
    if (item) {
      return NextResponse.json({ item })
    } else {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error updating inventory item:', error)
    return NextResponse.json(
      { error: 'Failed to update inventory item' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await inventoryService.delete(params.id)
    
    if (success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error deleting inventory item:', error)
    return NextResponse.json(
      { error: 'Failed to delete inventory item' },
      { status: 500 }
    )
  }
}
