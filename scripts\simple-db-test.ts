#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function testSimpleConnection() {
  console.log('🔍 Testing simple MySQL connection...\n')
  
  // Show environment variables
  console.log('Environment variables:')
  console.log('DB_HOST:', process.env.DB_HOST)
  console.log('DB_USER:', process.env.DB_USER)
  console.log('DB_NAME:', process.env.DB_NAME)
  console.log('DB_PASSWORD:', process.env.DB_PASSWORD === '' ? '[EMPTY]' : process.env.DB_PASSWORD ? '[SET]' : '[NOT SET]')
  console.log()

  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  console.log('Connection config:')
  console.log('Host:', dbConfig.host)
  console.log('Port:', dbConfig.port)
  console.log('User:', dbConfig.user)
  console.log('Password:', dbConfig.password === '' ? '[EMPTY]' : '[SET]')
  console.log('Database:', dbConfig.database)
  console.log()

  try {
    console.log('1. Testing connection to MySQL server...')
    
    // First, try to connect without specifying a database
    const serverConnection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
    })
    
    console.log('✅ Connected to MySQL server successfully')
    
    // Check if database exists
    console.log('2. Checking if database exists...')
    const [databases] = await serverConnection.execute('SHOW DATABASES')
    const dbExists = (databases as any[]).some(db => db.Database === dbConfig.database)
    
    if (dbExists) {
      console.log(`✅ Database '${dbConfig.database}' exists`)
    } else {
      console.log(`⚠️  Database '${dbConfig.database}' does not exist`)
      console.log('3. Creating database...')
      await serverConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``)
      console.log(`✅ Database '${dbConfig.database}' created`)
    }
    
    await serverConnection.end()
    
    // Now try to connect to the specific database
    console.log('4. Testing connection to specific database...')
    const dbConnection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database successfully')
    
    // Test a simple query
    console.log('5. Testing simple query...')
    const [result] = await dbConnection.execute('SELECT 1 as test')
    console.log('✅ Query executed successfully:', result)
    
    await dbConnection.end()
    
    console.log('\n🎉 All database tests passed!')
    return true
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
    return false
  }
}

async function main() {
  const success = await testSimpleConnection()
  process.exit(success ? 0 : 1)
}

main()
