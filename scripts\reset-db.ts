#!/usr/bin/env tsx

import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') })

import { databaseSeeder } from '../lib/database/seed'

async function main() {
  try {
    console.log('Starting database reset...')
    await databaseSeeder.resetDatabase()
    console.log('Database reset completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Database reset failed:', error)
    process.exit(1)
  }
}

main()
