-- Enhanced Validation and Constraints Migration
-- Version: 1.1.0
-- Description: Adds validation constraints and enforces data integrity

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Add check constraints for negative value validation
-- Note: MySQL 8.0.16+ supports CHECK constraints

-- 1. Add constraints to exchange_rates table
ALTER TABLE exchange_rates 
ADD CONSTRAINT chk_exchange_rates_positive_rate 
CHECK (rate_per_gram > 0);

-- 2. Add constraints to exchange_transactions table
ALTER TABLE exchange_transactions 
ADD CONSTRAINT chk_exchange_transactions_positive_amount 
CHECK (total_amount >= 0);

-- 3. Add constraints to exchange_items table
ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_positive_weights 
CHECK (gross_weight >= 0 AND stone_weight >= 0 AND net_weight >= 0);

ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_positive_rate 
CHECK (rate_per_gram > 0);

ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_positive_amount 
CHECK (amount >= 0);

ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_weight_logic 
CHECK (net_weight <= gross_weight);

-- 4. Add constraints to exchange_purchase_bills table
ALTER TABLE exchange_purchase_bills 
ADD CONSTRAINT chk_purchase_bills_positive_amounts 
CHECK (total_amount >= 0 AND cgst_amount >= 0 AND sgst_amount >= 0 AND total_with_tax >= 0);

ALTER TABLE exchange_purchase_bills 
ADD CONSTRAINT chk_purchase_bills_tax_logic 
CHECK (total_with_tax >= total_amount);

-- 5. Add constraints to sales_exchange_items table
ALTER TABLE sales_exchange_items 
ADD CONSTRAINT chk_sales_exchange_positive_deduction 
CHECK (deduction_amount >= 0);

ALTER TABLE sales_exchange_items 
ADD CONSTRAINT chk_sales_exchange_positive_rate 
CHECK (applied_rate > 0);

-- 6. Add constraints to bill_sequences table
ALTER TABLE bill_sequences 
ADD CONSTRAINT chk_bill_sequences_positive_number 
CHECK (current_number >= 0);

-- 7. Enforce foreign key constraints more strictly
-- Drop existing foreign keys and recreate with proper CASCADE/RESTRICT options

-- Exchange transactions to customers
ALTER TABLE exchange_transactions 
DROP FOREIGN KEY IF EXISTS exchange_transactions_ibfk_1;

ALTER TABLE exchange_transactions 
ADD CONSTRAINT fk_exchange_transactions_customer 
FOREIGN KEY (customer_id) REFERENCES customers(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Exchange items to transactions
ALTER TABLE exchange_items 
DROP FOREIGN KEY IF EXISTS exchange_items_ibfk_1;

ALTER TABLE exchange_items 
ADD CONSTRAINT fk_exchange_items_transaction 
FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Purchase bills to transactions
ALTER TABLE exchange_purchase_bills 
DROP FOREIGN KEY IF EXISTS exchange_purchase_bills_ibfk_1;

ALTER TABLE exchange_purchase_bills 
ADD CONSTRAINT fk_purchase_bills_transaction 
FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Purchase bills to customers
ALTER TABLE exchange_purchase_bills 
DROP FOREIGN KEY IF EXISTS exchange_purchase_bills_ibfk_2;

ALTER TABLE exchange_purchase_bills 
ADD CONSTRAINT fk_purchase_bills_customer 
FOREIGN KEY (customer_id) REFERENCES customers(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Sales exchange items to sales
ALTER TABLE sales_exchange_items 
DROP FOREIGN KEY IF EXISTS sales_exchange_items_ibfk_1;

ALTER TABLE sales_exchange_items 
ADD CONSTRAINT fk_sales_exchange_sale 
FOREIGN KEY (sale_id) REFERENCES sales(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Sales exchange items to exchange transactions
ALTER TABLE sales_exchange_items 
DROP FOREIGN KEY IF EXISTS sales_exchange_items_ibfk_2;

ALTER TABLE sales_exchange_items 
ADD CONSTRAINT fk_sales_exchange_transaction 
FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Sales exchange items to exchange items
ALTER TABLE sales_exchange_items 
DROP FOREIGN KEY IF EXISTS sales_exchange_items_ibfk_3;

ALTER TABLE sales_exchange_items 
ADD CONSTRAINT fk_sales_exchange_item 
FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Audit trail to transactions
ALTER TABLE exchange_audit_trail 
DROP FOREIGN KEY IF EXISTS exchange_audit_trail_ibfk_1;

ALTER TABLE exchange_audit_trail 
ADD CONSTRAINT fk_audit_trail_transaction 
FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Audit trail to bills (optional)
ALTER TABLE exchange_audit_trail 
DROP FOREIGN KEY IF EXISTS exchange_audit_trail_ibfk_2;

ALTER TABLE exchange_audit_trail 
ADD CONSTRAINT fk_audit_trail_bill 
FOREIGN KEY (related_bill_id) REFERENCES exchange_purchase_bills(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Audit trail to sales (optional)
ALTER TABLE exchange_audit_trail 
DROP FOREIGN KEY IF EXISTS exchange_audit_trail_ibfk_3;

ALTER TABLE exchange_audit_trail 
ADD CONSTRAINT fk_audit_trail_sale 
FOREIGN KEY (related_sale_id) REFERENCES sales(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 8. Add additional business logic constraints

-- Ensure exchange rates have valid effective dates
ALTER TABLE exchange_rates 
ADD CONSTRAINT chk_exchange_rates_valid_date 
CHECK (effective_date <= CURDATE() + INTERVAL 1 YEAR);

-- Ensure transaction dates are reasonable
ALTER TABLE exchange_transactions 
ADD CONSTRAINT chk_exchange_transactions_valid_date 
CHECK (transaction_date >= '2020-01-01' AND transaction_date <= CURDATE() + INTERVAL 1 DAY);

-- Ensure bill dates are reasonable
ALTER TABLE exchange_purchase_bills 
ADD CONSTRAINT chk_purchase_bills_valid_date 
CHECK (bill_date >= '2020-01-01' AND bill_date <= CURDATE() + INTERVAL 1 DAY);

-- Ensure purity values are valid
ALTER TABLE exchange_rates 
ADD CONSTRAINT chk_exchange_rates_valid_purity 
CHECK (
  (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
  (metal_type = 'silver' AND purity IN ('999', '925', '900', '800'))
);

ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_valid_purity 
CHECK (
  (metal_type = 'gold' AND purity IN ('24K', '22K', '18K', '14K', '10K')) OR
  (metal_type = 'silver' AND purity IN ('999', '925', '900', '800'))
);

-- Ensure reasonable weight limits (in grams)
ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_reasonable_weights 
CHECK (
  gross_weight <= 10000 AND  -- Max 10kg
  stone_weight <= gross_weight AND
  net_weight <= gross_weight AND
  gross_weight >= 0.001  -- Min 1mg
);

-- Ensure reasonable rate limits (per gram in INR)
ALTER TABLE exchange_rates 
ADD CONSTRAINT chk_exchange_rates_reasonable_rates 
CHECK (
  (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 20000) OR
  (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 500)
);

ALTER TABLE exchange_items 
ADD CONSTRAINT chk_exchange_items_reasonable_rates 
CHECK (
  (metal_type = 'gold' AND rate_per_gram BETWEEN 1000 AND 20000) OR
  (metal_type = 'silver' AND rate_per_gram BETWEEN 10 AND 500)
);

-- 9. Create validation triggers for complex business rules

DELIMITER //

-- Trigger to validate exchange item amounts
CREATE TRIGGER trg_validate_exchange_item_amount 
BEFORE INSERT ON exchange_items
FOR EACH ROW
BEGIN
  DECLARE calculated_amount DECIMAL(12,2);
  SET calculated_amount = NEW.net_weight * NEW.rate_per_gram;
  
  -- Allow small rounding differences (up to 1 rupee)
  IF ABS(NEW.amount - calculated_amount) > 1.00 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Exchange item amount does not match calculated value (net_weight * rate_per_gram)';
  END IF;
END//

-- Trigger to validate exchange item amounts on update
CREATE TRIGGER trg_validate_exchange_item_amount_update 
BEFORE UPDATE ON exchange_items
FOR EACH ROW
BEGIN
  DECLARE calculated_amount DECIMAL(12,2);
  SET calculated_amount = NEW.net_weight * NEW.rate_per_gram;
  
  -- Allow small rounding differences (up to 1 rupee)
  IF ABS(NEW.amount - calculated_amount) > 1.00 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Exchange item amount does not match calculated value (net_weight * rate_per_gram)';
  END IF;
END//

-- Trigger to validate purchase bill tax calculations
CREATE TRIGGER trg_validate_purchase_bill_tax 
BEFORE INSERT ON exchange_purchase_bills
FOR EACH ROW
BEGIN
  DECLARE expected_total DECIMAL(12,2);
  SET expected_total = NEW.total_amount + NEW.cgst_amount + NEW.sgst_amount;
  
  -- Allow small rounding differences (up to 1 rupee)
  IF ABS(NEW.total_with_tax - expected_total) > 1.00 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Purchase bill total_with_tax does not match total_amount + cgst_amount + sgst_amount';
  END IF;
END//

-- Trigger to validate purchase bill tax calculations on update
CREATE TRIGGER trg_validate_purchase_bill_tax_update 
BEFORE UPDATE ON exchange_purchase_bills
FOR EACH ROW
BEGIN
  DECLARE expected_total DECIMAL(12,2);
  SET expected_total = NEW.total_amount + NEW.cgst_amount + NEW.sgst_amount;
  
  -- Allow small rounding differences (up to 1 rupee)
  IF ABS(NEW.total_with_tax - expected_total) > 1.00 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Purchase bill total_with_tax does not match total_amount + cgst_amount + sgst_amount';
  END IF;
END//

-- Trigger to prevent deletion of transactions with bills
CREATE TRIGGER trg_prevent_transaction_deletion 
BEFORE DELETE ON exchange_transactions
FOR EACH ROW
BEGIN
  DECLARE bill_count INT;
  SELECT COUNT(*) INTO bill_count 
  FROM exchange_purchase_bills 
  WHERE exchange_transaction_id = OLD.id;
  
  IF bill_count > 0 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Cannot delete exchange transaction that has associated purchase bills';
  END IF;
END//

-- Trigger to prevent deletion of customers with transactions
CREATE TRIGGER trg_prevent_customer_deletion 
BEFORE DELETE ON customers
FOR EACH ROW
BEGIN
  DECLARE transaction_count INT;
  SELECT COUNT(*) INTO transaction_count 
  FROM exchange_transactions 
  WHERE customer_id = OLD.id;
  
  IF transaction_count > 0 THEN
    SIGNAL SQLSTATE '45000' 
    SET MESSAGE_TEXT = 'Cannot delete customer that has associated exchange transactions';
  END IF;
END//

DELIMITER ;

-- 10. Create indexes for better performance with constraints
CREATE INDEX idx_exchange_rates_metal_purity_active ON exchange_rates(metal_type, purity, is_active);
CREATE INDEX idx_exchange_transactions_customer_date ON exchange_transactions(customer_id, transaction_date);
CREATE INDEX idx_exchange_transactions_status_date ON exchange_transactions(status, transaction_date);
CREATE INDEX idx_exchange_items_metal_purity ON exchange_items(metal_type, purity);
CREATE INDEX idx_purchase_bills_date_status ON exchange_purchase_bills(bill_date, payment_status);
CREATE INDEX idx_sales_exchange_transaction_date ON sales_exchange_items(exchange_transaction_id, created_at);

-- 11. Create a validation summary view
CREATE OR REPLACE VIEW v_exchange_validation_summary AS
SELECT 
  'Exchange Rates' as table_name,
  COUNT(*) as total_records,
  SUM(CASE WHEN rate_per_gram <= 0 THEN 1 ELSE 0 END) as invalid_rates,
  SUM(CASE WHEN effective_date > CURDATE() + INTERVAL 1 YEAR THEN 1 ELSE 0 END) as invalid_dates
FROM exchange_rates
UNION ALL
SELECT 
  'Exchange Transactions' as table_name,
  COUNT(*) as total_records,
  SUM(CASE WHEN total_amount < 0 THEN 1 ELSE 0 END) as invalid_amounts,
  SUM(CASE WHEN transaction_date < '2020-01-01' OR transaction_date > CURDATE() + INTERVAL 1 DAY THEN 1 ELSE 0 END) as invalid_dates
FROM exchange_transactions
UNION ALL
SELECT 
  'Exchange Items' as table_name,
  COUNT(*) as total_records,
  SUM(CASE WHEN gross_weight < 0 OR stone_weight < 0 OR net_weight < 0 OR rate_per_gram <= 0 OR amount < 0 THEN 1 ELSE 0 END) as invalid_values,
  SUM(CASE WHEN net_weight > gross_weight THEN 1 ELSE 0 END) as invalid_weight_logic
FROM exchange_items;

COMMIT;

-- Display success message
SELECT 'Enhanced Validation and Constraints Applied Successfully!' as Status;
SELECT 'Added:' as Info, 
       'Check constraints, Foreign key constraints, Business logic triggers, Performance indexes' as Details;
