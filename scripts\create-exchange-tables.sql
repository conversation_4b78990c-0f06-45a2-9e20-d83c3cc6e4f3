-- Old Gold/Silver Exchange System Database Schema

-- Exchange Rates Table - Stores current rates for different metals and purities
CREATE TABLE IF NOT EXISTS exchange_rates (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver') NOT NULL,
  purity VARCHAR(10) NOT NULL, -- e.g., '22K', '18K', '916', '999'
  rate_per_gram DECIMAL(10,2) NOT NULL,
  effective_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_metal_purity (metal_type, purity),
  INDEX idx_active_rates (is_active, effective_date)
);

-- Exchange Transactions Table - Main exchange transaction records
CREATE TABLE IF NOT EXISTS exchange_transactions (
  id VARCHAR(36) PRIMARY KEY,
  transaction_number VARCHAR(50) UNIQUE NOT NULL, -- e.g., EXG-********-001
  customer_id VARCHAR(36),
  transaction_date DATE NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
  payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
  notes TEXT,
  status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_customer (customer_id),
  INDEX idx_status (status)
);

-- Exchange Items Table - Individual items in each exchange transaction
CREATE TABLE IF NOT EXISTS exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  transaction_id VARCHAR(36) NOT NULL,
  item_description VARCHAR(255) NOT NULL, -- e.g., 'GOLD OLD BAR', 'OLD GOLD RING'
  metal_type ENUM('gold', 'silver') NOT NULL,
  purity VARCHAR(10) NOT NULL,
  gross_weight DECIMAL(8,3) NOT NULL, -- in grams
  stone_weight DECIMAL(8,3) DEFAULT 0, -- weight of stones/other materials
  net_weight DECIMAL(8,3) NOT NULL, -- gross_weight - stone_weight
  rate_per_gram DECIMAL(10,2) NOT NULL,
  amount DECIMAL(12,2) NOT NULL, -- net_weight * rate_per_gram
  item_condition ENUM('good', 'fair', 'poor') DEFAULT 'good',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  INDEX idx_transaction (transaction_id),
  INDEX idx_metal_type (metal_type),
  INDEX idx_purity (purity)
);

-- Exchange Rate History Table - Track rate changes over time
CREATE TABLE IF NOT EXISTS exchange_rate_history (
  id VARCHAR(36) PRIMARY KEY,
  metal_type ENUM('gold', 'silver') NOT NULL,
  purity VARCHAR(10) NOT NULL,
  old_rate DECIMAL(10,2),
  new_rate DECIMAL(10,2) NOT NULL,
  change_reason VARCHAR(255),
  effective_date DATE NOT NULL,
  changed_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_metal_purity_date (metal_type, purity, effective_date)
);

-- Insert default exchange rates for common purities
INSERT INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date) VALUES
(UUID(), 'gold', '24K', 6500.00, CURDATE()),
(UUID(), 'gold', '22K', 5950.00, CURDATE()),
(UUID(), 'gold', '18K', 4875.00, CURDATE()),
(UUID(), 'gold', '14K', 3800.00, CURDATE()),
(UUID(), 'gold', '916', 5950.00, CURDATE()),
(UUID(), 'gold', '750', 4875.00, CURDATE()),
(UUID(), 'silver', '999', 85.00, CURDATE()),
(UUID(), 'silver', '925', 78.00, CURDATE()),
(UUID(), 'silver', '900', 76.00, CURDATE())
ON DUPLICATE KEY UPDATE 
  rate_per_gram = VALUES(rate_per_gram),
  updated_at = CURRENT_TIMESTAMP;
