import { NextRequest, NextResponse } from 'next/server'
import { salesService } from '@/lib/database/services'

export async function GET() {
  try {
    const sales = await salesService.findAll()
    return NextResponse.json({ sales })
  } catch (error) {
    console.error('Error fetching sales:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sales' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const saleData = await request.json()
    const sale = await salesService.create(saleData)
    return NextResponse.json({ sale })
  } catch (error) {
    console.error('Error creating sale:', error)
    return NextResponse.json(
      { error: 'Failed to create sale' },
      { status: 500 }
    )
  }
}
