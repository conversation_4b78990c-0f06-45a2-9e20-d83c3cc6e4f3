import { BaseService } from '../base-service'
import { Category } from '../../types'

export class CategoryService extends BaseService<Category> {
  protected tableName = 'categories'

  async findByName(name: string): Promise<Category[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE name LIKE ? ORDER BY sort_order ASC, created_at DESC`
    const rows = await this.executeQuery(sql, [`%${name}%`])
    return rows.map(row => this.transformKeys(row) as Category)
  }

  async findActive(): Promise<Category[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE is_active = true ORDER BY sort_order ASC, created_at DESC`
    const rows = await this.executeQuery(sql)
    return rows.map(row => this.transformKeys(row) as Category)
  }

  async findByParent(parentId: string): Promise<Category[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE parent_id = ? ORDER BY sort_order ASC, created_at DESC`
    const rows = await this.executeQuery(sql, [parentId])
    return rows.map(row => this.transformKeys(row) as Category)
  }

  async updateSortOrder(id: string, sortOrder: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET sort_order = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [sortOrder, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  async toggleActive(id: string, isActive: boolean): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET is_active = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [isActive, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  // Override findAll to include proper ordering
  async findAll(conditions: Record<string, any> = {}): Promise<Category[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `SELECT * FROM ${this.tableName} ${whereClause} ORDER BY sort_order ASC, created_at DESC`
    
    const rows = await this.executeQuery(sql, params)
    return rows.map(row => this.transformKeys(row) as Category)
  }

  // Transform database keys to match TypeScript interface
  protected transformKeys(row: any): any {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      parentId: row.parent_id,
      isActive: Boolean(row.is_active),
      sortOrder: row.sort_order || 0,
      image: row.image,
      tags: row.tags ? (typeof row.tags === 'string' ? JSON.parse(row.tags) : row.tags) : [],
      makingChargePercentage: parseFloat(row.making_charge_percentage) || 0,
      wastagePercentage: parseFloat(row.wastage_percentage) || 0,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }

  // Override create to handle JSON fields and transformations
  async create(data: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> {
    const id = this.generateId()
    const now = new Date().toISOString()
    
    const dbData = {
      id,
      name: data.name,
      description: data.description || null,
      parent_id: data.parentId || null,
      is_active: data.isActive,
      sort_order: data.sortOrder || 0,
      image: data.image || null,
      tags: JSON.stringify(data.tags || []),
      making_charge_percentage: data.makingChargePercentage || 0,
      wastage_percentage: data.wastagePercentage || 0,
      created_at: now,
      updated_at: now
    }

    const columns = Object.keys(dbData).join(', ')
    const placeholders = Object.keys(dbData).map(() => '?').join(', ')
    const values = Object.values(dbData)

    const sql = `INSERT INTO ${this.tableName} (${columns}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)

    return this.findById(id) as Promise<Category>
  }

  // Override update to handle JSON fields and transformations
  async update(id: string, data: Partial<Category>): Promise<Category | null> {
    const updates: Record<string, any> = {}
    
    if (data.name !== undefined) updates.name = data.name
    if (data.description !== undefined) updates.description = data.description
    if (data.parentId !== undefined) updates.parent_id = data.parentId
    if (data.isActive !== undefined) updates.is_active = data.isActive
    if (data.sortOrder !== undefined) updates.sort_order = data.sortOrder
    if (data.image !== undefined) updates.image = data.image
    if (data.tags !== undefined) updates.tags = JSON.stringify(data.tags)
    if (data.makingChargePercentage !== undefined) updates.making_charge_percentage = data.makingChargePercentage
    if (data.wastagePercentage !== undefined) updates.wastage_percentage = data.wastagePercentage
    
    updates.updated_at = new Date().toISOString()

    if (Object.keys(updates).length === 1) { // Only updated_at
      return this.findById(id)
    }

    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = [...Object.values(updates), id]

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`
    const result = await this.executeUpdate(sql, values)

    if (result.affectedRows === 0) {
      return null
    }

    return this.findById(id)
  }
}
