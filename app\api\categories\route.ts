import { NextRequest, NextResponse } from "next/server"
import { Category } from "@/lib/types"
import { categoryService } from "@/lib/database/services"
export async function GET() {
  try {
    const categories = await categoryService.findAll()
    return NextResponse.json({
      categories,
      success: true
    })
  } catch (error) {
    console.error("Error fetching categories:", error)
    return NextResponse.json(
      { error: "Failed to fetch categories", success: false },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { category } = await request.json()

    if (!category || !category.name) {
      return NextResponse.json(
        { error: "Category name is required", success: false },
        { status: 400 }
      )
    }

    // Check if category name already exists
    const existingCategories = await categoryService.findByName(category.name)
    const exactMatch = existingCategories.find(
      cat => cat.name.toLowerCase() === category.name.toLowerCase()
    )

    if (exactMatch) {
      return NextResponse.json(
        { error: "Category name already exists", success: false },
        { status: 400 }
      )
    }

    const newCategory = await categoryService.create({
      name: category.name,
      description: category.description || "",
      parentId: category.parentId || undefined,
      isActive: category.isActive ?? true,
      sortOrder: category.sortOrder || 0,
      image: category.image || "",
      tags: category.tags || [],
      makingChargePercentage: category.makingChargePercentage || 0,
      wastagePercentage: category.wastagePercentage || 0,
    })

    return NextResponse.json({
      category: newCategory,
      success: true
    })
  } catch (error) {
    console.error("Error creating category:", error)
    return NextResponse.json(
      { error: "Failed to create category", success: false },
      { status: 500 }
    )
  }
}
