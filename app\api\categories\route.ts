import { NextRequest, NextResponse } from "next/server"
import { Category } from "@/lib/types"

// Mock data for categories
const mockCategories: Category[] = [
  {
    id: "cat_1",
    name: "Rings",
    description: "Wedding rings, engagement rings, and fashion rings",
    isActive: true,
    sortOrder: 1,
    tags: ["wedding", "engagement", "fashion"],
    makingChargePercentage: 15,
    wastagePercentage: 2,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_2",
    name: "Necklaces",
    description: "Gold and silver necklaces, chains, and pendants",
    isActive: true,
    sortOrder: 2,
    tags: ["chains", "pendants", "traditional"],
    makingChargePercentage: 12,
    wastagePercentage: 1.5,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_3",
    name: "Earrings",
    description: "Studs, hoops, and traditional earrings",
    isActive: true,
    sortOrder: 3,
    tags: ["studs", "hoops", "traditional", "modern"],
    makingChargePercentage: 18,
    wastagePercentage: 2.5,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_4",
    name: "Bracelets",
    description: "Gold and silver bracelets and bangles",
    isActive: true,
    sortOrder: 4,
    tags: ["bangles", "charm", "tennis"],
    makingChargePercentage: 14,
    wastagePercentage: 2,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_5",
    name: "Pendants",
    description: "Religious and fashion pendants",
    isActive: true,
    sortOrder: 5,
    tags: ["religious", "fashion", "custom"],
    makingChargePercentage: 16,
    wastagePercentage: 1.8,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_6",
    name: "Chains",
    description: "Gold and silver chains of various designs",
    isActive: true,
    sortOrder: 6,
    tags: ["rope", "box", "figaro", "curb"],
    makingChargePercentage: 10,
    wastagePercentage: 1,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_7",
    name: "Anklets",
    description: "Traditional and modern anklets",
    isActive: true,
    sortOrder: 7,
    tags: ["traditional", "modern", "charm"],
    makingChargePercentage: 13,
    wastagePercentage: 1.5,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "cat_8",
    name: "Sets",
    description: "Complete jewelry sets including necklace, earrings, and more",
    isActive: true,
    sortOrder: 8,
    tags: ["bridal", "party", "traditional", "complete"],
    makingChargePercentage: 20,
    wastagePercentage: 3,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
]

let categories = [...mockCategories]

export async function GET() {
  try {
    return NextResponse.json({ 
      categories: categories.sort((a, b) => a.sortOrder - b.sortOrder),
      success: true 
    })
  } catch (error) {
    console.error("Error fetching categories:", error)
    return NextResponse.json(
      { error: "Failed to fetch categories", success: false },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { category } = await request.json()

    if (!category || !category.name) {
      return NextResponse.json(
        { error: "Category name is required", success: false },
        { status: 400 }
      )
    }

    // Check if category name already exists
    const existingCategory = categories.find(
      cat => cat.name.toLowerCase() === category.name.toLowerCase()
    )

    if (existingCategory) {
      return NextResponse.json(
        { error: "Category name already exists", success: false },
        { status: 400 }
      )
    }

    const newCategory: Category = {
      id: `cat_${Date.now()}`,
      name: category.name,
      description: category.description || "",
      parentId: category.parentId || undefined,
      isActive: category.isActive ?? true,
      sortOrder: category.sortOrder || categories.length + 1,
      image: category.image || "",
      tags: category.tags || [],
      makingChargePercentage: category.makingChargePercentage || 0,
      wastagePercentage: category.wastagePercentage || 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    categories.push(newCategory)

    return NextResponse.json({ 
      category: newCategory, 
      success: true 
    })
  } catch (error) {
    console.error("Error creating category:", error)
    return NextResponse.json(
      { error: "Failed to create category", success: false },
      { status: 500 }
    )
  }
}
