#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function createMissingTables() {
  console.log('🏗️  Creating Missing Tables for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')

    // 1. Purchases Table
    console.log('   Creating purchases table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS purchases (
        id VARCHAR(36) PRIMARY KEY,
        purchase_number VARCHAR(100) UNIQUE NOT NULL,
        purchase_date DATE NOT NULL,
        purchase_time TIME DEFAULT '00:00:00',
        
        -- Supplier Information
        supplier_id VARCHAR(36),
        supplier_name VARCHAR(255),
        supplier_contact VARCHAR(20),
        supplier_address TEXT,
        
        -- Purchase Type and Classification
        purchase_type ENUM('inventory', 'exchange', 'raw_material', 'tools', 'other') DEFAULT 'inventory',
        purchase_category ENUM('gold', 'silver', 'platinum', 'diamonds', 'stones', 'tools', 'other') DEFAULT 'gold',
        payment_mode ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'dd', 'credit') DEFAULT 'cash',
        
        -- Financial Calculations
        subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
        
        -- Discount Information
        discount_type ENUM('percentage', 'fixed', 'trade', 'bulk') DEFAULT 'percentage',
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        discount_amount DECIMAL(15,2) DEFAULT 0.00,
        
        -- Taxable Amount Calculation
        taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,
        
        -- Tax Information
        cgst_rate DECIMAL(5,2) DEFAULT 1.50,
        sgst_rate DECIMAL(5,2) DEFAULT 1.50,
        igst_rate DECIMAL(5,2) DEFAULT 0.00,
        cess_rate DECIMAL(5,2) DEFAULT 0.00,
        cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
        sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
        igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
        cess_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cess_rate / 100) STORED,
        total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount + cess_amount) STORED,
        
        -- Additional Charges
        shipping_charges DECIMAL(10,2) DEFAULT 0.00,
        handling_charges DECIMAL(10,2) DEFAULT 0.00,
        insurance_charges DECIMAL(10,2) DEFAULT 0.00,
        other_charges DECIMAL(10,2) DEFAULT 0.00,
        total_additional_charges DECIMAL(15,2) GENERATED ALWAYS AS (shipping_charges + handling_charges + insurance_charges + other_charges) STORED,
        
        -- Round Off
        round_off DECIMAL(5,2) DEFAULT 0.00,
        
        -- Grand Total Calculation
        grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + total_additional_charges + round_off) STORED,
        
        -- Payment Information
        payment_status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'pending',
        paid_amount DECIMAL(15,2) DEFAULT 0.00,
        balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (grand_total - paid_amount) STORED,
        due_date DATE,
        
        -- Status Information
        status ENUM('draft', 'confirmed', 'received', 'partial_received', 'cancelled') DEFAULT 'confirmed',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- Staff Information
        purchased_by VARCHAR(36),
        approved_by VARCHAR(36),
        received_by VARCHAR(36),
        
        -- Additional Information
        notes TEXT,
        internal_notes TEXT,
        terms_conditions TEXT,
        
        -- Audit Information
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_purchase_number (purchase_number),
        INDEX idx_purchase_date (purchase_date),
        INDEX idx_supplier (supplier_id),
        INDEX idx_purchase_type (purchase_type),
        INDEX idx_payment_status (payment_status),
        INDEX idx_status (status),
        INDEX idx_grand_total (grand_total),
        INDEX idx_created_at (created_at)
      )
    `)

    // 2. Purchase Items Table
    console.log('   Creating purchase_items table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS purchase_items (
        id VARCHAR(36) PRIMARY KEY,
        purchase_id VARCHAR(36) NOT NULL,
        line_number INT NOT NULL,
        
        -- Item Information
        item_description VARCHAR(255) NOT NULL,
        item_type VARCHAR(100),
        category VARCHAR(100),
        
        -- Metal Information
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20),
        hallmark_number VARCHAR(100),
        
        -- Weight Information
        gross_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
        diamond_weight DECIMAL(8,3) DEFAULT 0.000,
        
        -- Pricing Information
        rate_per_gram DECIMAL(12,2) NOT NULL,
        amount DECIMAL(15,2) GENERATED ALWAYS AS (net_weight * rate_per_gram) STORED,
        
        -- Additional Charges
        making_charges DECIMAL(15,2) DEFAULT 0.00,
        stone_charges DECIMAL(15,2) DEFAULT 0.00,
        other_charges DECIMAL(15,2) DEFAULT 0.00,
        
        -- Total Amount Calculation
        total_amount DECIMAL(15,2) GENERATED ALWAYS AS (amount + making_charges + stone_charges + other_charges) STORED,
        
        -- Quantity Information
        quantity INT DEFAULT 1,
        unit_price DECIMAL(15,2) GENERATED ALWAYS AS (total_amount / quantity) STORED,
        
        -- Additional Information
        hsn_code VARCHAR(20),
        size VARCHAR(50),
        color VARCHAR(50),
        design_number VARCHAR(100),
        
        -- Status
        status ENUM('ordered', 'received', 'partial_received', 'cancelled') DEFAULT 'ordered',
        received_quantity INT DEFAULT 0,
        
        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_purchase_line (purchase_id, line_number),
        INDEX idx_purchase (purchase_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_total_amount (total_amount),
        INDEX idx_status (status)
      )
    `)

    // 3. Schemes Table
    console.log('   Creating schemes table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS schemes (
        id VARCHAR(36) PRIMARY KEY,
        scheme_number VARCHAR(100) UNIQUE NOT NULL,
        scheme_name VARCHAR(255) NOT NULL,
        scheme_type ENUM('gold_scheme', 'silver_scheme', 'diamond_scheme', 'custom_scheme') DEFAULT 'gold_scheme',
        
        -- Customer Information
        customer_id VARCHAR(36),
        customer_name VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(20),
        customer_address TEXT,
        
        -- Scheme Details
        total_amount DECIMAL(15,2) NOT NULL,
        installment_amount DECIMAL(15,2) NOT NULL,
        total_installments INT NOT NULL,
        paid_installments INT DEFAULT 0,
        pending_installments INT GENERATED ALWAYS AS (total_installments - paid_installments) STORED,
        
        -- Amount Calculations
        total_paid DECIMAL(15,2) DEFAULT 0.00,
        balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - total_paid) STORED,
        
        -- Dates
        start_date DATE NOT NULL,
        maturity_date DATE NOT NULL,
        last_payment_date DATE,
        next_due_date DATE,
        
        -- Status
        status ENUM('active', 'completed', 'cancelled', 'defaulted', 'on_hold') DEFAULT 'active',
        completion_percentage DECIMAL(5,2) GENERATED ALWAYS AS ((paid_installments / total_installments) * 100) STORED,
        
        -- Bonus and Benefits
        bonus_percentage DECIMAL(5,2) DEFAULT 0.00,
        bonus_amount DECIMAL(15,2) DEFAULT 0.00,
        
        -- Additional Information
        notes TEXT,
        terms_conditions TEXT,
        
        -- Staff Information
        created_by VARCHAR(36),
        managed_by VARCHAR(36),
        
        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_scheme_number (scheme_number),
        INDEX idx_customer (customer_id),
        INDEX idx_scheme_type (scheme_type),
        INDEX idx_status (status),
        INDEX idx_maturity_date (maturity_date),
        INDEX idx_next_due_date (next_due_date)
      )
    `)

    // 4. Scheme Payments Table
    console.log('   Creating scheme_payments table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS scheme_payments (
        id VARCHAR(36) PRIMARY KEY,
        scheme_id VARCHAR(36) NOT NULL,
        payment_number INT NOT NULL,
        
        -- Payment Details
        payment_date DATE NOT NULL,
        payment_amount DECIMAL(15,2) NOT NULL,
        payment_mode ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'dd') DEFAULT 'cash',
        payment_reference VARCHAR(100),
        
        -- Status
        status ENUM('pending', 'confirmed', 'cancelled', 'refunded') DEFAULT 'confirmed',
        
        -- Additional Information
        notes TEXT,
        
        -- Staff Information
        received_by VARCHAR(36),
        
        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_scheme_payment (scheme_id, payment_number),
        INDEX idx_scheme (scheme_id),
        INDEX idx_payment_date (payment_date),
        INDEX idx_status (status)
      )
    `)

    // 5. Repairs Table
    console.log('   Creating repairs table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS repairs (
        id VARCHAR(36) PRIMARY KEY,
        repair_number VARCHAR(100) UNIQUE NOT NULL,
        
        -- Customer Information
        customer_id VARCHAR(36),
        customer_name VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(20) NOT NULL,
        customer_address TEXT,
        
        -- Item Information
        item_description TEXT NOT NULL,
        item_type VARCHAR(100),
        metal_type ENUM('gold', 'silver', 'platinum', 'other') NOT NULL,
        purity VARCHAR(20),
        
        -- Repair Details
        repair_type ENUM('cleaning', 'polishing', 'stone_setting', 'resizing', 'chain_repair', 'clasp_repair', 'other') NOT NULL,
        repair_description TEXT,
        estimated_cost DECIMAL(10,2) DEFAULT 0.00,
        actual_cost DECIMAL(10,2) DEFAULT 0.00,
        
        -- Dates
        received_date DATE NOT NULL,
        promised_date DATE,
        completed_date DATE,
        delivered_date DATE,
        
        -- Status
        status ENUM('received', 'in_progress', 'completed', 'delivered', 'cancelled', 'on_hold') DEFAULT 'received',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- Payment
        payment_status ENUM('pending', 'advance_paid', 'paid', 'refunded') DEFAULT 'pending',
        advance_amount DECIMAL(10,2) DEFAULT 0.00,
        balance_amount DECIMAL(10,2) GENERATED ALWAYS AS (actual_cost - advance_amount) STORED,
        
        -- Additional Information
        notes TEXT,
        internal_notes TEXT,
        
        -- Staff Information
        received_by VARCHAR(36),
        assigned_to VARCHAR(36),
        completed_by VARCHAR(36),
        delivered_by VARCHAR(36),
        
        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_repair_number (repair_number),
        INDEX idx_customer (customer_id),
        INDEX idx_customer_phone (customer_phone),
        INDEX idx_repair_type (repair_type),
        INDEX idx_status (status),
        INDEX idx_promised_date (promised_date),
        INDEX idx_received_date (received_date)
      )
    `)

    console.log('   ✅ All missing tables created successfully')

    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    console.log('\n🎉 Missing Tables Created Successfully!')

    // Verify tables exist
    console.log('\n🔍 Verifying created tables...')
    const [tables] = await connection.execute(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name IN ('purchases', 'purchase_items', 'schemes', 'scheme_payments', 'repairs')
      ORDER BY table_name
    `, [dbConfig.database])

    console.log(`   ✅ Verified ${(tables as any[]).length} tables:`)
    ;(tables as any[]).forEach(table => {
      console.log(`      - ${table.table_name}`)
    })

  } catch (error) {
    console.error('\n❌ Missing table creation failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the missing table creation
createMissingTables().catch(console.error)
