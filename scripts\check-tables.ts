#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function checkTables() {
  console.log('🔍 Checking Database Tables and Structure...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Get all tables
    const [tables] = await connection.execute(`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = ? 
      ORDER BY table_name
    `, [dbConfig.database])

    console.log('📋 Available tables:')
    ;(tables as any[]).forEach(table => {
      console.log(`   - ${table.table_name}`)
    })

    // Check specific tables we need
    const tablesToCheck = ['customers', 'sales', 'inventory', 'sale_items']
    
    for (const tableName of tablesToCheck) {
      console.log(`\n🔍 Checking ${tableName} table structure:`)
      
      try {
        const [columns] = await connection.execute(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_schema = ? AND table_name = ?
          ORDER BY ordinal_position
        `, [dbConfig.database, tableName])

        if ((columns as any[]).length > 0) {
          console.log('   Columns:')
          ;(columns as any[]).forEach(col => {
            console.log(`     ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`)
          })
        } else {
          console.log('   ❌ Table does not exist')
        }
      } catch (error) {
        console.log(`   ❌ Error checking table: ${error}`)
      }
    }

    // Check exchange-related tables
    console.log('\n🔍 Exchange-related tables:')
    const exchangeTables = ['exchange_rates', 'exchange_transactions', 'exchange_items', 'exchange_purchase_bills', 'sales_exchange_items']
    
    for (const tableName of exchangeTables) {
      try {
        const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`)
        const recordCount = (count as any[])[0].count
        console.log(`   ${tableName}: ${recordCount} records`)
      } catch (error) {
        console.log(`   ${tableName}: ❌ Error - ${error}`)
      }
    }

  } catch (error) {
    console.error('\n❌ Database check failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the table check
checkTables().catch(console.error)
