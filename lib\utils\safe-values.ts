
// Utility functions for safe value handling
export const safeNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return defaultValue
  }
  return Number(value)
}

export const safeString = (value: any, defaultValue: string = ''): string => {
  if (value === null || value === undefined) {
    return defaultValue
  }
  return String(value)
}

export const formatCurrency = (value: any, defaultValue: number = 0): string => {
  const numValue = safeNumber(value, defaultValue)
  return `₹${numValue.toLocaleString()}`
}

export const formatWeight = (value: any, defaultValue: number = 0, unit: string = 'g'): string => {
  const numValue = safeNumber(value, defaultValue)
  return `${numValue.toFixed(3)}${unit}`
}

export const formatPercentage = (value: any, defaultValue: number = 0): string => {
  const numValue = safeNumber(value, defaultValue)
  return `${numValue.toFixed(2)}%`
}
