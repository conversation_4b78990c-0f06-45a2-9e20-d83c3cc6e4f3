"use client"

import { useDatabase } from '@/lib/hooks/use-database'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Database, Loader2, RefreshCw } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface DatabaseProviderProps {
  children: React.ReactNode
}

export function DatabaseProvider({ children }: DatabaseProviderProps) {
  const { isInitialized, isConnected, isLoading, error, retry } = useDatabase()

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Loader2 className="h-6 w-6 text-blue-600 animate-spin" />
            </div>
            <CardTitle>Initializing Database</CardTitle>
            <CardDescription>
              Please wait while we connect to the database and load your data...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  // Show error state
  if (error || !isConnected) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-lg">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Database Connection Error</CardTitle>
            <CardDescription>
              Unable to connect to the database. Please check your configuration.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error Details</AlertTitle>
              <AlertDescription>
                {error || 'Database connection failed'}
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <h4 className="font-medium">Troubleshooting Steps:</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>Ensure MySQL server is running</li>
                <li>Check database credentials in .env.local</li>
                <li>Verify database exists and user has permissions</li>
                <li>Run database initialization: <code className="bg-gray-100 px-1 rounded">pnpm run db:init</code></li>
              </ul>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={retry} className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Connection
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.open('/DATABASE_SETUP.md', '_blank')}
                className="flex-1"
              >
                <Database className="h-4 w-4 mr-2" />
                Setup Guide
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show success state - render children
  if (isInitialized && isConnected) {
    return <>{children}</>
  }

  // Fallback loading state
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
            <Database className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle>Loading Application</CardTitle>
          <CardDescription>
            Preparing your jewellery management system...
          </CardDescription>
        </CardHeader>
      </Card>
    </div>
  )
}
