import { BaseService } from '../base-service'
import { ExchangeTransaction, ExchangeItem, ExchangeRate, ExchangeRateHistory } from '../../types'
import { RowDataPacket } from 'mysql2'

export class ExchangeService extends BaseService<ExchangeTransaction> {
  protected tableName = 'exchange_transactions'

  // Generate transaction number
  private async generateTransactionNumber(): Promise<string> {
    const today = new Date()
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '')
    
    // Get count of transactions today
    const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE DATE(transaction_date) = CURDATE()`
    const result = await this.executeQuery<RowDataPacket[]>(sql)
    const count = result[0]?.count || 0
    
    return `EXG-${dateStr}-${String(count + 1).padStart(3, '0')}`
  }

  // Override create to handle transaction with items
  async create(data: Omit<ExchangeTransaction, 'id' | 'createdAt' | 'updatedAt' | 'transactionNumber'>): Promise<ExchangeTransaction> {
    const connection = await this.pool.getConnection()
    
    try {
      await connection.beginTransaction()
      
      const transactionNumber = await this.generateTransactionNumber()
      const transactionId = this.generateId()
      const now = new Date().toISOString()
      
      // Create main transaction
      const transactionData = {
        ...data,
        id: transactionId,
        transactionNumber,
        createdAt: now,
        updatedAt: now
      }
      
      const transformedData = this.transformKeysToSnake(transactionData)
      const keys = Object.keys(transformedData).filter(key => key !== 'items')
      const values = keys.map(key => transformedData[key])
      const placeholders = keys.map(() => '?').join(', ')
      
      const transactionSql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
      await connection.execute(transactionSql, values)
      
      // Create exchange items
      if (data.items && data.items.length > 0) {
        for (const item of data.items) {
          const itemData = {
            ...item,
            id: this.generateId(),
            transactionId,
            createdAt: now,
            updatedAt: now
          }
          
          const transformedItemData = this.transformKeysToSnake(itemData)
          const itemKeys = Object.keys(transformedItemData)
          const itemValues = Object.values(transformedItemData)
          const itemPlaceholders = itemKeys.map(() => '?').join(', ')
          
          const itemSql = `INSERT INTO exchange_items (${itemKeys.join(', ')}) VALUES (${itemPlaceholders})`
          await connection.execute(itemSql, itemValues)
        }
      }
      
      await connection.commit()
      
      // Return the created transaction with items
      return this.findById(transactionId) as Promise<ExchangeTransaction>
      
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Override findById to include items
  async findById(id: string): Promise<ExchangeTransaction | null> {
    const sql = `
      SELECT t.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
      FROM ${this.tableName} t
      LEFT JOIN customers c ON t.customer_id = c.id
      WHERE t.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    const transaction = this.transformKeys(rows[0]) as ExchangeTransaction
    
    // Get items for this transaction
    const itemsSql = `SELECT * FROM exchange_items WHERE transaction_id = ? ORDER BY created_at`
    const itemRows = await this.executeQuery<RowDataPacket[]>(itemsSql, [id])
    transaction.items = itemRows.map(row => this.transformKeys(row) as ExchangeItem)
    
    return transaction
  }

  // Override findAll to include items
  async findAll(conditions: Record<string, any> = {}): Promise<ExchangeTransaction[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `
      SELECT t.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
      FROM ${this.tableName} t
      LEFT JOIN customers c ON t.customer_id = c.id
      ${whereClause}
      ORDER BY t.transaction_date DESC, t.created_at DESC
    `
    
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    const transactions = rows.map(row => this.transformKeys(row) as ExchangeTransaction)
    
    // Get items for each transaction
    for (const transaction of transactions) {
      const itemsSql = `SELECT * FROM exchange_items WHERE transaction_id = ? ORDER BY created_at`
      const itemRows = await this.executeQuery<RowDataPacket[]>(itemsSql, [transaction.id])
      transaction.items = itemRows.map(row => this.transformKeys(row) as ExchangeItem)
    }
    
    return transactions
  }

  // Get transactions by date range
  async findByDateRange(startDate: string, endDate: string): Promise<ExchangeTransaction[]> {
    return this.findAll({
      transaction_date: {
        '>=': startDate,
        '<=': endDate
      }
    })
  }

  // Get transactions by customer
  async findByCustomer(customerId: string): Promise<ExchangeTransaction[]> {
    return this.findAll({ customer_id: customerId })
  }

  // Get exchange statistics
  async getExchangeStats(startDate?: string, endDate?: string): Promise<{
    totalTransactions: number
    totalAmount: number
    goldAmount: number
    silverAmount: number
    averageTransactionValue: number
  }> {
    let whereClause = 'WHERE status = "completed"'
    const params: any[] = []
    
    if (startDate && endDate) {
      whereClause += ' AND transaction_date BETWEEN ? AND ?'
      params.push(startDate, endDate)
    }
    
    const sql = `
      SELECT 
        COUNT(*) as total_transactions,
        SUM(total_amount) as total_amount,
        AVG(total_amount) as avg_transaction_value,
        SUM(CASE WHEN ei.metal_type = 'gold' THEN ei.amount ELSE 0 END) as gold_amount,
        SUM(CASE WHEN ei.metal_type = 'silver' THEN ei.amount ELSE 0 END) as silver_amount
      FROM ${this.tableName} et
      LEFT JOIN exchange_items ei ON et.id = ei.transaction_id
      ${whereClause}
    `
    
    const result = await this.executeQuery<RowDataPacket[]>(sql, params)
    const stats = result[0]
    
    return {
      totalTransactions: stats?.total_transactions || 0,
      totalAmount: stats?.total_amount || 0,
      goldAmount: stats?.gold_amount || 0,
      silverAmount: stats?.silver_amount || 0,
      averageTransactionValue: stats?.avg_transaction_value || 0
    }
  }

  // Update transaction status
  async updateStatus(id: string, status: 'pending' | 'completed' | 'cancelled'): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET status = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [status, new Date().toISOString(), id])
    return result.affectedRows > 0
  }
}
