import { getPool } from '../config'
import { MetalRates } from '../../types'

export interface Settings {
  id?: number
  businessName: string
  address: string
  phone: string
  email: string
  gstNumber: string
  metalRates: MetalRates
  autoUpdateRates: boolean
  cgstRate: string
  sgstRate: string
  lowStockAlert: boolean
  lowStockThreshold: string
  schemeReminders: boolean
  repairReminders: boolean
  currency: string
  dateFormat: string
  backupFrequency: string
  invoiceTemplate: string
  printLogo: boolean
  printTerms: boolean
  createdAt?: string
  updatedAt?: string
}

export class SettingsService {
  private pool = getPool()
  private tableName = 'settings'

  private transformKeys(obj: any): Settings {
    if (!obj) return obj
    
    return {
      id: obj.id,
      businessName: obj.business_name,
      address: obj.address,
      phone: obj.phone,
      email: obj.email,
      gstNumber: obj.gst_number,
      metalRates: typeof obj.metal_rates === 'string' ? JSON.parse(obj.metal_rates) : obj.metal_rates,
      autoUpdateRates: obj.auto_update_rates,
      cgstRate: obj.cgst_rate?.toString(),
      sgstRate: obj.sgst_rate?.toString(),
      lowStockAlert: obj.low_stock_alert,
      lowStockThreshold: obj.low_stock_threshold?.toString(),
      schemeReminders: obj.scheme_reminders,
      repairReminders: obj.repair_reminders,
      currency: obj.currency,
      dateFormat: obj.date_format,
      backupFrequency: obj.backup_frequency,
      invoiceTemplate: obj.invoice_template,
      printLogo: obj.print_logo,
      printTerms: obj.print_terms,
      createdAt: obj.created_at,
      updatedAt: obj.updated_at
    }
  }

  private transformKeysToSnake(obj: Partial<Settings>): any {
    const transformed: any = {}
    
    if (obj.businessName !== undefined) transformed.business_name = obj.businessName
    if (obj.address !== undefined) transformed.address = obj.address
    if (obj.phone !== undefined) transformed.phone = obj.phone
    if (obj.email !== undefined) transformed.email = obj.email
    if (obj.gstNumber !== undefined) transformed.gst_number = obj.gstNumber
    if (obj.metalRates !== undefined) transformed.metal_rates = JSON.stringify(obj.metalRates)
    if (obj.autoUpdateRates !== undefined) transformed.auto_update_rates = obj.autoUpdateRates
    if (obj.cgstRate !== undefined) transformed.cgst_rate = parseFloat(obj.cgstRate)
    if (obj.sgstRate !== undefined) transformed.sgst_rate = parseFloat(obj.sgstRate)
    if (obj.lowStockAlert !== undefined) transformed.low_stock_alert = obj.lowStockAlert
    if (obj.lowStockThreshold !== undefined) transformed.low_stock_threshold = parseInt(obj.lowStockThreshold)
    if (obj.schemeReminders !== undefined) transformed.scheme_reminders = obj.schemeReminders
    if (obj.repairReminders !== undefined) transformed.repair_reminders = obj.repairReminders
    if (obj.currency !== undefined) transformed.currency = obj.currency
    if (obj.dateFormat !== undefined) transformed.date_format = obj.dateFormat
    if (obj.backupFrequency !== undefined) transformed.backup_frequency = obj.backupFrequency
    if (obj.invoiceTemplate !== undefined) transformed.invoice_template = obj.invoiceTemplate
    if (obj.printLogo !== undefined) transformed.print_logo = obj.printLogo
    if (obj.printTerms !== undefined) transformed.print_terms = obj.printTerms
    
    return transformed
  }

  async getSettings(): Promise<Settings> {
    const sql = `SELECT * FROM ${this.tableName} ORDER BY id DESC LIMIT 1`
    const [rows] = await this.pool.execute(sql)
    const results = rows as any[]
    
    if (results.length === 0) {
      // Return default settings if none exist
      return this.getDefaultSettings()
    }
    
    return this.transformKeys(results[0])
  }

  async updateSettings(settings: Partial<Settings>): Promise<Settings> {
    const current = await this.getSettings()
    
    if (current.id) {
      // Update existing settings
      const transformedData = this.transformKeysToSnake(settings)
      transformedData.updated_at = new Date().toISOString()
      
      const keys = Object.keys(transformedData)
      const values = Object.values(transformedData)
      const setClause = keys.map(key => `${key} = ?`).join(', ')
      
      const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`
      await this.pool.execute(sql, [...values, current.id])
    } else {
      // Create new settings record
      const fullSettings = { ...this.getDefaultSettings(), ...settings }
      const transformedData = this.transformKeysToSnake(fullSettings)
      transformedData.created_at = new Date().toISOString()
      transformedData.updated_at = new Date().toISOString()
      
      const keys = Object.keys(transformedData)
      const values = Object.values(transformedData)
      const placeholders = keys.map(() => '?').join(', ')
      
      const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
      await this.pool.execute(sql, values)
    }
    
    return this.getSettings()
  }

  async resetToDefaults(): Promise<Settings> {
    // Delete all existing settings
    await this.pool.execute(`DELETE FROM ${this.tableName}`)
    
    // Create new default settings
    return this.updateSettings(this.getDefaultSettings())
  }

  private getDefaultSettings(): Settings {
    return {
      businessName: "Shree Jewellers",
      address: "123 Main Street, Mumbai",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      gstNumber: "27ABCDE1234F1Z5",
      metalRates: {
        gold: {
          "22K": "642",
          "18K": "525",
        },
        silver: {
          "925": "78.5",
        },
      },
      autoUpdateRates: true,
      cgstRate: "1.5",
      sgstRate: "1.5",
      lowStockAlert: true,
      lowStockThreshold: "5",
      schemeReminders: true,
      repairReminders: true,
      currency: "INR",
      dateFormat: "DD/MM/YYYY",
      backupFrequency: "daily",
      invoiceTemplate: "standard",
      printLogo: true,
      printTerms: true,
    }
  }

  async getMetalRate(metalType: string, purity: string): Promise<number> {
    const settings = await this.getSettings()
    
    if (metalType === "gold" && settings.metalRates.gold[purity as keyof typeof settings.metalRates.gold]) {
      return parseFloat(settings.metalRates.gold[purity as keyof typeof settings.metalRates.gold])
    }

    if (metalType === "silver" && settings.metalRates.silver[purity as keyof typeof settings.metalRates.silver]) {
      return parseFloat(settings.metalRates.silver[purity as keyof typeof settings.metalRates.silver])
    }

    // Add platinum rates if needed
    if (metalType === "platinum") {
      return 3000 // Default platinum rate
    }

    return 0
  }

  async updateMetalRates(metalRates: MetalRates): Promise<Settings> {
    return this.updateSettings({ metalRates })
  }
}
