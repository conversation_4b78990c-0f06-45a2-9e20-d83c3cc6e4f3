/**
 * Jewelry Item ID Generator
 * Generates business-appropriate item codes with HSN codes for jewelry items
 */

// HSN codes for different jewelry categories
export const HSN_CODES = {
  'Rings': '7113',
  'Necklaces': '7113', 
  'Earrings': '7113',
  'Bracelets': '7113',
  'Pendants': '7113',
  'Chains': '7113',
  'Anklets': '7113',
  'Sets': '7113',
  'Bangles': '7113',
  'Nose Pins': '7113',
  'Toe Rings': '7113',
  'Armlets': '7113',
  'Waist Chains': '7113',
  'Hair Ornaments': '7113',
  'Brooches': '7113',
  'Cufflinks': '7113',
  'Tie Pins': '7113',
  'Watches': '9101',
  'Watch Straps': '9113',
  'Precious Stones': '7103',
  'Semi Precious Stones': '7103',
  'Pearls': '7101',
  'Silver Items': '7114',
  'Gold Items': '7113',
  'Platinum Items': '7113',
  'Diamond Items': '7113'
} as const

// Metal type codes
export const METAL_CODES = {
  'gold': 'G',
  'silver': 'S', 
  'platinum': 'P',
  'copper': 'C',
  'brass': 'B',
  'steel': 'ST',
  'titanium': 'T',
  'palladium': 'PD'
} as const

// Category codes for shorter IDs
export const CATEGORY_CODES = {
  'Rings': 'RG',
  'Necklaces': 'NK',
  'Earrings': 'ER', 
  'Bracelets': 'BR',
  'Pendants': 'PD',
  'Chains': 'CH',
  'Anklets': 'AN',
  'Sets': 'ST',
  'Bangles': 'BG',
  'Nose Pins': 'NP',
  'Toe Rings': 'TR',
  'Armlets': 'AR',
  'Waist Chains': 'WC',
  'Hair Ornaments': 'HO',
  'Brooches': 'BC',
  'Cufflinks': 'CF',
  'Tie Pins': 'TP',
  'Watches': 'WT',
  'Watch Straps': 'WS',
  'Precious Stones': 'PS',
  'Semi Precious Stones': 'SP',
  'Pearls': 'PR',
  'Silver Items': 'SI',
  'Gold Items': 'GI',
  'Platinum Items': 'PI',
  'Diamond Items': 'DI'
} as const

// Purity codes
export const PURITY_CODES = {
  '22K': '22',
  '18K': '18',
  '925': '925', // Sterling silver
  '916': '916', // 22K gold (alternative notation)
  '750': '750', // 18K gold (alternative notation)
} as const

/**
 * Generate a jewelry item ID
 * Format: [CATEGORY][METAL][PURITY]-[YEAR][MONTH][SEQUENCE]
 * Example: RGGD22-202412001 (Ring, Gold, 22K, December 2024, sequence 001)
 */
export function generateJewelryItemId(
  category: string,
  metalType: string,
  purity: string,
  sequence?: number
): string {
  // Get category code
  const categoryCode = CATEGORY_CODES[category as keyof typeof CATEGORY_CODES] || 'GN' // GN = General

  // Get metal code
  const metalCode = METAL_CODES[metalType.toLowerCase() as keyof typeof METAL_CODES] || 'M' // M = Mixed/Other

  // Get purity code (simplified)
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)

  // Get current date
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')

  // Generate sequence number (if not provided, use timestamp-based)
  const sequenceNum = sequence || Math.floor(Math.random() * 999) + 1
  const sequenceStr = String(sequenceNum).padStart(3, '0')

  return `${categoryCode}${metalCode}${purityCode}-${year}${month}${sequenceStr}`
}

/**
 * Generate HSN code for a jewelry item
 */
export function getHSNCode(category: string, metalType?: string): string {
  // Check specific category first
  if (HSN_CODES[category as keyof typeof HSN_CODES]) {
    return HSN_CODES[category as keyof typeof HSN_CODES]
  }

  // Check by metal type
  if (metalType) {
    const metalLower = metalType.toLowerCase()
    if (metalLower.includes('silver')) return '7114'
    if (metalLower.includes('gold')) return '7113'
    if (metalLower.includes('platinum')) return '7113'
  }

  // Default to general jewelry HSN code
  return '7113'
}

/**
 * Parse a jewelry item ID to extract components
 */
export function parseJewelryItemId(itemId: string): {
  category: string
  metal: string
  purity: string
  year: string
  month: string
  sequence: string
  isValid: boolean
} {
  // Check if it matches our format: [CATEGORY][METAL][PURITY]-[YEAR][MONTH][SEQUENCE]
  const match = itemId.match(/^([A-Z]{2})([A-Z]{1,2})([0-9]{2,3})-([0-9]{4})([0-9]{2})([0-9]{3})$/)
  
  if (!match) {
    return {
      category: '',
      metal: '',
      purity: '',
      year: '',
      month: '',
      sequence: '',
      isValid: false
    }
  }

  const [, categoryCode, metalCode, purityCode, year, month, sequence] = match

  // Reverse lookup category
  const category = Object.keys(CATEGORY_CODES).find(
    key => CATEGORY_CODES[key as keyof typeof CATEGORY_CODES] === categoryCode
  ) || 'Unknown'

  // Reverse lookup metal
  const metal = Object.keys(METAL_CODES).find(
    key => METAL_CODES[key as keyof typeof METAL_CODES] === metalCode
  ) || 'Unknown'

  return {
    category,
    metal,
    purity: purityCode,
    year,
    month,
    sequence,
    isValid: true
  }
}

/**
 * Validate if an item ID follows jewelry business format
 */
export function isValidJewelryItemId(itemId: string): boolean {
  return parseJewelryItemId(itemId).isValid
}

/**
 * Generate a display-friendly item code
 * Format: [HSN]-[CATEGORY][METAL][PURITY]-[SEQUENCE]
 * Example: 7113-RGG22-001
 */
export function generateDisplayItemCode(
  category: string,
  metalType: string,
  purity: string,
  sequence?: number
): string {
  const hsn = getHSNCode(category, metalType)
  const categoryCode = CATEGORY_CODES[category as keyof typeof CATEGORY_CODES] || 'GN'
  const metalCode = METAL_CODES[metalType.toLowerCase() as keyof typeof METAL_CODES] || 'M'
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  const sequenceNum = sequence || Math.floor(Math.random() * 999) + 1
  const sequenceStr = String(sequenceNum).padStart(3, '0')

  return `${hsn}-${categoryCode}${metalCode}${purityCode}-${sequenceStr}`
}

/**
 * Generate a barcode-compatible item ID
 * Format: [HSN][CATEGORY_NUM][METAL_NUM][PURITY][SEQUENCE]
 * Example: 711301221001 (HSN:7113, Category:01, Metal:2, Purity:22, Sequence:1001)
 */
export function generateBarcodeItemId(
  category: string,
  metalType: string,
  purity: string,
  sequence?: number
): string {
  const hsn = getHSNCode(category, metalType)
  
  // Category number (01-99)
  const categoryKeys = Object.keys(CATEGORY_CODES)
  const categoryIndex = categoryKeys.indexOf(category) + 1
  const categoryNum = String(categoryIndex).padStart(2, '0')
  
  // Metal number (1-9)
  const metalKeys = Object.keys(METAL_CODES)
  const metalIndex = metalKeys.indexOf(metalType.toLowerCase()) + 1
  const metalNum = String(metalIndex).slice(-1)
  
  // Purity (2-3 digits)
  const purityCode = PURITY_CODES[purity as keyof typeof PURITY_CODES] || purity.replace(/[^0-9]/g, '').slice(0, 3)
  
  // Sequence (4 digits)
  const sequenceNum = sequence || Math.floor(Math.random() * 9999) + 1
  const sequenceStr = String(sequenceNum).padStart(4, '0')

  return `${hsn}${categoryNum}${metalNum}${purityCode}${sequenceStr}`
}
