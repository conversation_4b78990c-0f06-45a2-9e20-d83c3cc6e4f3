#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'

async function updateDemoUsers() {
  console.log('👥 Updating Demo Users for Shree Jewellers...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Update business name to match UI
    console.log('🏢 Step 1: Updating business name to Shree Jewellers...')
    
    await connection.execute(`
      UPDATE business_settings 
      SET business_name = 'Shree Jewellers',
          trade_name = 'Shree Jewellers',
          updated_at = NOW()
      WHERE id IS NOT NULL
    `)
    console.log('   ✅ Business name updated to Shree Jewellers')

    // Step 2: Update demo user accounts to match the UI
    console.log('\n👤 Step 2: Updating demo user accounts...')
    
    const saltRounds = 12
    const demoUsers = [
      {
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
        role: 'super_admin',
        password: 'admin123'
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        first_name: 'Store',
        last_name: 'Manager',
        role: 'manager',
        password: 'admin123'
      },
      {
        username: 'staff',
        email: '<EMAIL>',
        first_name: 'Sales',
        last_name: 'Staff',
        role: 'sales_staff',
        password: 'admin123'
      }
    ]

    let updatedCount = 0
    for (const user of demoUsers) {
      try {
        // Generate bcrypt hash
        const hashedPassword = await bcrypt.hash(user.password, saltRounds)
        
        // Check if user exists
        const [existingUser] = await connection.execute(`
          SELECT id FROM users WHERE username = ?
        `, [user.username])

        if ((existingUser as any[]).length > 0) {
          // Update existing user
          await connection.execute(`
            UPDATE users 
            SET email = ?, first_name = ?, last_name = ?, role = ?, password_hash = ?, updated_at = NOW()
            WHERE username = ?
          `, [user.email, user.first_name, user.last_name, user.role, hashedPassword, user.username])
          
          console.log(`   ✅ Updated ${user.username} (${user.email})`)
        } else {
          // Create new user if doesn't exist
          await connection.execute(`
            INSERT INTO users (
              id, username, email, first_name, last_name, role, password_hash,
              is_active, max_discount_percentage, max_transaction_amount,
              created_at, updated_at
            ) VALUES (UUID(), ?, ?, ?, ?, ?, ?, TRUE, 
                     CASE 
                       WHEN ? = 'super_admin' THEN 50.00
                       WHEN ? = 'manager' THEN 30.00
                       ELSE 10.00
                     END,
                     CASE 
                       WHEN ? = 'super_admin' THEN 10000000.00
                       WHEN ? = 'manager' THEN 5000000.00
                       ELSE 1000000.00
                     END,
                     NOW(), NOW())
          `, [
            user.username, user.email, user.first_name, user.last_name, user.role, hashedPassword,
            user.role, user.role, user.role, user.role
          ])
          
          console.log(`   ✅ Created ${user.username} (${user.email})`)
        }
        updatedCount++
      } catch (error) {
        console.log(`   ❌ Failed to update ${user.username}: ${error}`)
      }
    }

    console.log(`\n   📊 Updated/Created ${updatedCount}/${demoUsers.length} demo users`)

    // Step 3: Update user permissions for demo accounts
    console.log('\n🔐 Step 3: Updating demo user permissions...')
    
    const rolePermissions = {
      super_admin: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'customers', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'sales', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'purchases', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'schemes', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'repairs', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'categories', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'exchange', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'reports', actions: ['read', 'export'] },
        { module: 'settings', actions: ['read', 'update'] },
        { module: 'users', actions: ['create', 'read', 'update', 'delete'] }
      ],
      manager: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'customers', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'sales', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'purchases', actions: ['create', 'read', 'update', 'export'] },
        { module: 'schemes', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'repairs', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'categories', actions: ['read', 'update'] },
        { module: 'exchange', actions: ['create', 'read', 'update', 'export'] },
        { module: 'reports', actions: ['read', 'export'] },
        { module: 'settings', actions: ['read'] }
      ],
      sales_staff: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['read'] },
        { module: 'customers', actions: ['create', 'read', 'update'] },
        { module: 'sales', actions: ['create', 'read', 'update'] },
        { module: 'schemes', actions: ['create', 'read', 'update'] },
        { module: 'repairs', actions: ['create', 'read', 'update'] },
        { module: 'exchange', actions: ['create', 'read'] }
      ]
    }

    for (const user of demoUsers) {
      const permissions = rolePermissions[user.role as keyof typeof rolePermissions]
      if (permissions) {
        const permissionsJson = JSON.stringify(permissions)
        
        await connection.execute(`
          UPDATE users 
          SET permissions = ?, updated_at = NOW() 
          WHERE username = ?
        `, [permissionsJson, user.username])
        
        console.log(`   ✅ Updated permissions for ${user.username} (${permissions.length} modules)`)
      }
    }

    // Step 4: Verify demo accounts
    console.log('\n✅ Step 4: Verifying demo accounts...')
    
    const [verifyUsers] = await connection.execute(`
      SELECT username, email, first_name, last_name, role, is_active,
             CASE WHEN password_hash LIKE '$2%' THEN 'bcrypt' ELSE 'invalid' END as hash_type
      FROM users 
      WHERE username IN ('admin', 'manager', 'staff')
      ORDER BY 
        CASE role 
          WHEN 'super_admin' THEN 1 
          WHEN 'manager' THEN 2 
          WHEN 'sales_staff' THEN 3 
          ELSE 4 
        END
    `)

    console.log('   Demo Account Verification:')
    console.log('   ' + '='.repeat(80))
    console.log('   Username | Email                        | Name           | Role        | Status')
    console.log('   ' + '-'.repeat(80))
    
    ;(verifyUsers as any[]).forEach(user => {
      const status = user.is_active ? '✅ Active' : '❌ Inactive'
      const username = user.username.padEnd(8)
      const email = user.email.padEnd(28)
      const name = `${user.first_name} ${user.last_name}`.padEnd(14)
      const role = user.role.padEnd(11)
      
      console.log(`   ${username} | ${email} | ${name} | ${role} | ${status}`)
    })
    console.log('   ' + '='.repeat(80))

    // Step 5: Test authentication for demo accounts
    console.log('\n🧪 Step 5: Testing demo account authentication...')
    
    for (const user of demoUsers) {
      try {
        const [userRecord] = await connection.execute(`
          SELECT username, password_hash FROM users WHERE username = ?
        `, [user.username])

        if ((userRecord as any[]).length > 0) {
          const record = (userRecord as any[])[0]
          const isValid = await bcrypt.compare(user.password, record.password_hash)
          console.log(`   ${isValid ? '✅' : '❌'} ${user.username}: Password ${isValid ? 'Valid' : 'Invalid'}`)
        } else {
          console.log(`   ❌ ${user.username}: User not found`)
        }
      } catch (error) {
        console.log(`   ❌ ${user.username}: Authentication test failed`)
      }
    }

    console.log('\n🎉 Demo Users Updated Successfully!')

    console.log('\n📋 UPDATED DEMO ACCOUNTS:')
    console.log('=' .repeat(60))
    console.log('🏢 Business: Shree Jewellers')
    console.log('🔐 All accounts use password: admin123')
    console.log('=' .repeat(60))
    console.log('👑 Admin:    <EMAIL>')
    console.log('👔 Manager:  <EMAIL>') 
    console.log('👤 Staff:    <EMAIL>')
    console.log('=' .repeat(60))
    console.log('✅ All accounts have secure bcrypt password hashes')
    console.log('✅ All accounts have role-based permissions')
    console.log('✅ All accounts are active and ready for use')

  } catch (error) {
    console.error('\n❌ Demo user update failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the demo user update
updateDemoUsers().catch(console.error)
