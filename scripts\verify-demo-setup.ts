#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'

async function verifyDemoSetup() {
  console.log('🔍 Verifying Demo Setup for Shree Jewellers...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Verify business name
    console.log('🏢 Step 1: Verifying business configuration...')
    
    const [businessInfo] = await connection.execute(`
      SELECT business_name, city, state, phone, email, gst_number
      FROM business_settings LIMIT 1
    `)
    
    if ((businessInfo as any[]).length > 0) {
      const business = (businessInfo as any[])[0]
      console.log(`   ✅ Business Name: ${business.business_name}`)
      console.log(`   ✅ Location: ${business.city}, ${business.state}`)
      console.log(`   ✅ Contact: ${business.phone} | ${business.email}`)
      console.log(`   ✅ GST: ${business.gst_number}`)
    }

    // Step 2: Verify demo accounts match UI
    console.log('\n👥 Step 2: Verifying demo accounts match UI...')
    
    const expectedAccounts = [
      { username: 'admin', email: '<EMAIL>', role: 'super_admin' },
      { username: 'manager', email: '<EMAIL>', role: 'manager' },
      { username: 'staff', email: '<EMAIL>', role: 'sales_staff' }
    ]

    console.log('   Expected vs Actual Demo Accounts:')
    console.log('   ' + '='.repeat(80))
    
    for (const expected of expectedAccounts) {
      const [userRecord] = await connection.execute(`
        SELECT username, email, first_name, last_name, role, is_active, password_hash
        FROM users WHERE username = ?
      `, [expected.username])

      if ((userRecord as any[]).length > 0) {
        const user = (userRecord as any[])[0]
        const emailMatch = user.email === expected.email ? '✅' : '❌'
        const roleMatch = user.role === expected.role ? '✅' : '❌'
        const activeStatus = user.is_active ? '✅' : '❌'
        const hashValid = user.password_hash?.startsWith('$2') ? '✅' : '❌'
        
        console.log(`   ${expected.username.toUpperCase()}:`)
        console.log(`      Email: ${emailMatch} ${user.email} (expected: ${expected.email})`)
        console.log(`      Role:  ${roleMatch} ${user.role} (expected: ${expected.role})`)
        console.log(`      Active: ${activeStatus} ${user.is_active}`)
        console.log(`      Hash: ${hashValid} bcrypt format`)
        
        // Test password
        try {
          const passwordValid = await bcrypt.compare('admin123', user.password_hash)
          console.log(`      Password: ${passwordValid ? '✅' : '❌'} admin123`)
        } catch (error) {
          console.log(`      Password: ❌ Test failed`)
        }
        console.log('')
      } else {
        console.log(`   ${expected.username.toUpperCase()}: ❌ NOT FOUND`)
      }
    }

    // Step 3: Test authentication simulation
    console.log('\n🔐 Step 3: Simulating login authentication...')
    
    const testAuthentication = async (email: string, password: string) => {
      try {
        const [userRows] = await connection.execute(`
          SELECT id, username, email, first_name, last_name, role, password_hash, is_active, permissions
          FROM users 
          WHERE email = ? AND is_active = TRUE
        `, [email])

        if ((userRows as any[]).length === 0) {
          return { success: false, message: 'User not found or inactive' }
        }

        const user = (userRows as any[])[0]
        if (!user.password_hash) {
          return { success: false, message: 'No password hash found' }
        }

        const isValidPassword = await bcrypt.compare(password, user.password_hash)
        if (!isValidPassword) {
          return { success: false, message: 'Invalid password' }
        }

        // Parse permissions
        let permissions = null
        if (user.permissions) {
          try {
            permissions = typeof user.permissions === 'string' ? 
              JSON.parse(user.permissions) : user.permissions
          } catch (error) {
            permissions = null
          }
        }

        return { 
          success: true, 
          message: 'Authentication successful',
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            name: `${user.first_name} ${user.last_name}`,
            role: user.role,
            permissions: permissions
          }
        }
      } catch (error) {
        return { success: false, message: `Authentication error: ${error}` }
      }
    }

    // Test each demo account
    const testAccounts = [
      { email: '<EMAIL>', password: 'admin123', role: 'Admin' },
      { email: '<EMAIL>', password: 'admin123', role: 'Manager' },
      { email: '<EMAIL>', password: 'admin123', role: 'Staff' }
    ]

    console.log('   Authentication Test Results:')
    console.log('   ' + '='.repeat(70))
    
    for (const testAccount of testAccounts) {
      const result = await testAuthentication(testAccount.email, testAccount.password)
      const status = result.success ? '✅ SUCCESS' : '❌ FAILED'
      console.log(`   ${testAccount.role}: ${status}`)
      console.log(`      Email: ${testAccount.email}`)
      console.log(`      Result: ${result.message}`)
      
      if (result.success && result.user) {
        console.log(`      User: ${result.user.name} (${result.user.role})`)
        console.log(`      Permissions: ${result.user.permissions ? result.user.permissions.length + ' modules' : 'None'}`)
      }
      console.log('')
    }

    // Step 4: Generate final demo status report
    console.log('\n📊 Step 4: Final demo status report...')
    
    const [systemStats] = await connection.execute(`
      SELECT 
        (SELECT business_name FROM business_settings LIMIT 1) as business_name,
        (SELECT COUNT(*) FROM users WHERE username IN ('admin', 'manager', 'staff')) as demo_users,
        (SELECT COUNT(*) FROM users WHERE username IN ('admin', 'manager', 'staff') AND is_active = TRUE) as active_demo_users,
        (SELECT COUNT(*) FROM users WHERE username IN ('admin', 'manager', 'staff') AND password_hash LIKE '$2%') as secure_demo_users,
        (SELECT COUNT(*) FROM inventory WHERE status = 'active') as active_inventory,
        (SELECT COUNT(*) FROM customers) as total_customers,
        (SELECT COUNT(*) FROM sales) as total_sales
    `)

    const stats = (systemStats as any[])[0]
    
    console.log('   🎯 DEMO SYSTEM STATUS REPORT:')
    console.log('   ' + '='.repeat(70))
    console.log(`   🏢 Business: ${stats.business_name}`)
    console.log(`   👥 Demo Users: ${stats.active_demo_users}/${stats.demo_users} active`)
    console.log(`   🔐 Secure Hashes: ${stats.secure_demo_users}/${stats.demo_users} users`)
    console.log(`   📦 Active Inventory: ${stats.active_inventory} items`)
    console.log(`   🛍️  Customers: ${stats.total_customers} profiles`)
    console.log(`   💰 Sales: ${stats.total_sales} transactions`)
    console.log('   ' + '='.repeat(70))

    const allSystemsGo = stats.demo_users === 3 && 
                        stats.active_demo_users === 3 && 
                        stats.secure_demo_users === 3 &&
                        stats.business_name === 'Shree Jewellers'

    if (allSystemsGo) {
      console.log('   🟢 STATUS: ALL DEMO SYSTEMS OPERATIONAL')
      console.log('   ✅ Ready for demo and testing!')
    } else {
      console.log('   🟡 STATUS: SOME ISSUES DETECTED')
      console.log('   ⚠️  Please review the above results')
    }

    console.log('\n🎉 Demo Setup Verification Completed!')

    console.log('\n📋 FINAL DEMO CREDENTIALS:')
    console.log('=' .repeat(60))
    console.log('🏢 Shree Jewellers - Jewelry Management System')
    console.log('=' .repeat(60))
    console.log('👑 Admin:    <EMAIL>    | admin123')
    console.log('👔 Manager:  <EMAIL>  | admin123')
    console.log('👤 Staff:    <EMAIL>    | admin123')
    console.log('=' .repeat(60))
    console.log('✅ All accounts ready for demo use')
    console.log('✅ Matches the login screen exactly')
    console.log('✅ Secure bcrypt password hashing')
    console.log('✅ Role-based permission system')

  } catch (error) {
    console.error('\n❌ Demo setup verification failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the demo setup verification
verifyDemoSetup().catch(console.error)
