#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { readFile } from 'fs/promises'
import { randomUUID } from 'crypto'

async function implementCompleteProfessional() {
  console.log('🚀 Implementing Complete Professional Jewelry Management System...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Drop existing database and recreate
    console.log('🗑️  Step 1: Clean database recreation...')
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    await connection.execute(`DROP DATABASE IF EXISTS ${dbConfig.database}`)
    console.log('   ✅ Dropped existing database')

    await connection.execute(`CREATE DATABASE ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
    console.log('   ✅ Created fresh database with proper charset')

    // Reconnect to the new database
    await connection.end()
    connection = await mysql.createConnection({
      ...dbConfig,
      database: dbConfig.database
    })

    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    // Step 2: Implement complete professional schema
    console.log('\n🏗️  Step 2: Implementing complete professional schema...')
    
    try {
      const schemaPath = resolve(process.cwd(), 'lib/database/complete-professional-schema.sql')
      const schemaSQL = await readFile(schemaPath, 'utf8')
      
      // Split SQL into individual statements, handling multi-line statements properly
      const statements = schemaSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => 
          stmt.length > 0 && 
          !stmt.startsWith('--') && 
          !stmt.toLowerCase().includes('create database') &&
          !stmt.toLowerCase().includes('use jewellers_db')
        )

      let successCount = 0
      let skipCount = 0
      let errorCount = 0

      for (const statement of statements) {
        try {
          if (statement.toLowerCase().includes('create table')) {
            await connection.execute(statement)
            const tableMatch = statement.match(/create table (?:if not exists )?(\w+)/i)
            if (tableMatch) {
              console.log(`   ✅ Created table: ${tableMatch[1]}`)
              successCount++
            }
          } else if (statement.toLowerCase().includes('create index') || 
                     statement.toLowerCase().includes('alter table') ||
                     statement.toLowerCase().includes('insert into')) {
            await connection.execute(statement)
            successCount++
          } else if (statement.trim().length > 10) {
            await connection.execute(statement)
            successCount++
          } else {
            skipCount++
          }
          
        } catch (error) {
          errorCount++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          
          if (errorMessage.includes('already exists') || 
              errorMessage.includes('Duplicate')) {
            console.log(`   ⚠️  Skipped (already exists): ${statement.substring(0, 50)}...`)
            skipCount++
          } else {
            console.log(`   ❌ Error: ${errorMessage}`)
            console.log(`      Statement: ${statement.substring(0, 100)}...`)
          }
        }
      }

      console.log(`\n📊 Schema Implementation Summary:`)
      console.log(`   ✅ Successful operations: ${successCount}`)
      console.log(`   ⚠️  Skipped operations: ${skipCount}`)
      console.log(`   ❌ Failed operations: ${errorCount}`)

    } catch (error) {
      console.error('   ❌ Error reading schema file:', error)
      throw error
    }

    // Step 3: Seed comprehensive sample data
    console.log('\n🌱 Step 3: Seeding comprehensive sample data...')
    
    // Business Settings
    console.log('   Setting up business configuration...')
    await connection.execute(`
      INSERT INTO business_settings (
        business_name, business_type, address_line1, address_line2, city, state, 
        postal_code, country, phone, email, website, gst_number, pan_number,
        bank_name, bank_account_number, bank_ifsc, currency, timezone,
        financial_year_start, cgst_rate, sgst_rate, igst_rate,
        default_making_charge_percentage, default_wastage_percentage, default_margin_percentage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'JJ Jewellers Premium Collection', 'retail', '123 Gold Street', 'Jewelry Market Complex',
      'Mumbai', 'Maharashtra', '400001', 'India', '+91-22-********', 
      '<EMAIL>', 'www.jjjewellers.com', '27**********1Z5',
      '**********', 'HDFC Bank', '**************', 'HDFC0001234',
      'INR', 'Asia/Kolkata', 'april', 1.50, 1.50, 3.00, 15.00, 8.00, 25.00
    ])

    // System Settings
    const systemSettings = [
      ['general', 'app_version', '3.0.0', 'string', 'Application version', true],
      ['general', 'maintenance_mode', 'false', 'boolean', 'Maintenance mode flag', true],
      ['general', 'backup_enabled', 'true', 'boolean', 'Automatic backup enabled', false],
      ['general', 'low_stock_threshold', '5', 'number', 'Low stock alert threshold', false],
      ['general', 'auto_update_rates', 'true', 'boolean', 'Auto update metal rates', false],
      ['general', 'invoice_template', 'premium', 'string', 'Default invoice template', false],
      ['general', 'print_logo', 'true', 'boolean', 'Print logo on invoices', false],
      ['notification', 'email_notifications', 'true', 'boolean', 'Email notifications enabled', false],
      ['notification', 'sms_notifications', 'true', 'boolean', 'SMS notifications enabled', false],
      ['general', 'loyalty_program', 'true', 'boolean', 'Customer loyalty program enabled', false],
      ['general', 'exchange_discount', '2.0', 'number', 'Exchange rate discount percentage', false],
      ['security', 'max_login_attempts', '5', 'number', 'Maximum login attempts before lockout', true],
      ['security', 'session_timeout', '3600', 'number', 'Session timeout in seconds', true],
      ['reporting', 'auto_backup_time', '02:00', 'time', 'Daily backup time', false]
    ]

    for (const [category, key, value, type, description, isSystem] of systemSettings) {
      await connection.execute(`
        INSERT INTO system_settings (setting_category, setting_key, setting_value, setting_type, description, is_system) 
        VALUES (?, ?, ?, ?, ?, ?)
      `, [category, key, value, type, description, isSystem])
    }

    // Users
    console.log('   Creating professional user accounts...')
    const users = [
      {
        id: randomUUID(),
        employee_code: 'EMP001',
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'System',
        last_name: 'Administrator',
        phone: '+91-**********',
        role: 'super_admin',
        department: 'management',
        designation: 'System Administrator',
        max_discount_percentage: 50.00,
        max_transaction_amount: ********.00
      },
      {
        id: randomUUID(),
        employee_code: 'EMP002',
        username: 'manager',
        email: '<EMAIL>',
        first_name: 'Store',
        last_name: 'Manager',
        phone: '+91-**********',
        role: 'manager',
        department: 'management',
        designation: 'Store Manager',
        max_discount_percentage: 25.00,
        max_transaction_amount: 5000000.00
      },
      {
        id: randomUUID(),
        employee_code: 'EMP003',
        username: 'sales1',
        email: '<EMAIL>',
        first_name: 'Rajesh',
        last_name: 'Kumar',
        phone: '+91-9999999997',
        role: 'sales_staff',
        department: 'sales',
        designation: 'Senior Sales Executive',
        max_discount_percentage: 10.00,
        max_transaction_amount: 1000000.00
      },
      {
        id: randomUUID(),
        employee_code: 'EMP004',
        username: 'sales2',
        email: '<EMAIL>',
        first_name: 'Priya',
        last_name: 'Sharma',
        phone: '+91-**********',
        role: 'sales_staff',
        department: 'sales',
        designation: 'Sales Executive',
        max_discount_percentage: 5.00,
        max_transaction_amount: 500000.00
      },
      {
        id: randomUUID(),
        employee_code: 'EMP005',
        username: 'accountant',
        email: '<EMAIL>',
        first_name: 'Suresh',
        last_name: 'Agarwal',
        phone: '+91-**********',
        role: 'accountant',
        department: 'accounts',
        designation: 'Chief Accountant',
        max_discount_percentage: 0.00,
        max_transaction_amount: ********.00
      }
    ]

    for (const user of users) {
      await connection.execute(`
        INSERT INTO users (
          id, employee_code, username, email, password_hash, first_name, last_name, phone, 
          role, department, designation, max_discount_percentage, max_transaction_amount,
          is_active, joining_date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, CURDATE(), NOW(), NOW())
      `, [
        user.id, user.employee_code, user.username, user.email, '$2b$10$defaulthash',
        user.first_name, user.last_name, user.phone, user.role, user.department,
        user.designation, user.max_discount_percentage, user.max_transaction_amount
      ])
    }

    // Categories
    console.log('   Creating comprehensive product categories...')
    const categories = [
      { name: 'Rings', code: 'RING', hsn: '********', charge_type: 'percentage', charge_value: 15.00, wastage: 8.00 },
      { name: 'Necklaces', code: 'NECK', hsn: '71131100', charge_type: 'percentage', charge_value: 18.00, wastage: 10.00 },
      { name: 'Earrings', code: 'EARR', hsn: '71131200', charge_type: 'percentage', charge_value: 12.00, wastage: 6.00 },
      { name: 'Bangles', code: 'BANG', hsn: '71131300', charge_type: 'percentage', charge_value: 10.00, wastage: 5.00 },
      { name: 'Chains', code: 'CHAI', hsn: '71131400', charge_type: 'percentage', charge_value: 8.00, wastage: 4.00 },
      { name: 'Pendants', code: 'PEND', hsn: '71131500', charge_type: 'percentage', charge_value: 15.00, wastage: 8.00 },
      { name: 'Bracelets', code: 'BRAC', hsn: '71131600', charge_type: 'percentage', charge_value: 12.00, wastage: 6.00 },
      { name: 'Anklets', code: 'ANKL', hsn: '71131700', charge_type: 'percentage', charge_value: 10.00, wastage: 5.00 },
      { name: 'Nose Pins', code: 'NOSE', hsn: '71131800', charge_type: 'percentage', charge_value: 20.00, wastage: 12.00 },
      { name: 'Coins & Bars', code: 'COIN', hsn: '71131000', charge_type: 'fixed', charge_value: 500.00, wastage: 0.00 },
      { name: 'Temple Jewelry', code: 'TEMP', hsn: '********', charge_type: 'percentage', charge_value: 25.00, wastage: 15.00 },
      { name: 'Antique Jewelry', code: 'ANTQ', hsn: '********', charge_type: 'percentage', charge_value: 30.00, wastage: 20.00 }
    ]

    for (const category of categories) {
      await connection.execute(`
        INSERT INTO categories (
          id, category_code, name, hsn_code, making_charge_type, making_charge_value,
          wastage_percentage, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
      `, [
        randomUUID(), category.code, category.name, category.hsn,
        category.charge_type, category.charge_value, category.wastage
      ])
    }

    // Metal Rates
    console.log('   Setting up comprehensive metal rates...')
    const metalRates = [
      { metal: 'gold', purity: '24K', rate: 7200.00, opening: 7180.00, closing: 7220.00, high: 7250.00, low: 7150.00 },
      { metal: 'gold', purity: '22K', rate: 6600.00, opening: 6580.00, closing: 6620.00, high: 6650.00, low: 6550.00 },
      { metal: 'gold', purity: '18K', rate: 5400.00, opening: 5380.00, closing: 5420.00, high: 5450.00, low: 5350.00 },
      { metal: 'gold', purity: '14K', rate: 4200.00, opening: 4180.00, closing: 4220.00, high: 4250.00, low: 4150.00 },
      { metal: 'gold', purity: '10K', rate: 3000.00, opening: 2980.00, closing: 3020.00, high: 3050.00, low: 2950.00 },
      { metal: 'silver', purity: '999', rate: 90.00, opening: 89.50, closing: 90.50, high: 91.00, low: 89.00 },
      { metal: 'silver', purity: '925', rate: 83.00, opening: 82.50, closing: 83.50, high: 84.00, low: 82.00 },
      { metal: 'silver', purity: '900', rate: 81.00, opening: 80.50, closing: 81.50, high: 82.00, low: 80.00 },
      { metal: 'platinum', purity: '950', rate: 3200.00, opening: 3180.00, closing: 3220.00, high: 3250.00, low: 3150.00 },
      { metal: 'platinum', purity: '900', rate: 3000.00, opening: 2980.00, closing: 3020.00, high: 3050.00, low: 2950.00 }
    ]

    for (const rate of metalRates) {
      const rateId = randomUUID()
      
      await connection.execute(`
        INSERT INTO metal_rates (
          id, metal_type, purity, rate_per_gram, opening_rate, closing_rate, high_rate, low_rate,
          effective_date, is_active, source, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), TRUE, 'manual', NOW(), NOW())
      `, [
        rateId, rate.metal, rate.purity, rate.rate, rate.opening, rate.closing, rate.high, rate.low
      ])
      
      // Create corresponding exchange rates with 2% discount
      await connection.execute(`
        INSERT INTO exchange_rates (
          id, metal_type, purity, rate_per_gram, discount_percentage, effective_date, 
          is_active, source, base_metal_rate_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 2.00, CURDATE(), TRUE, 'derived', ?, NOW(), NOW())
      `, [randomUUID(), rate.metal, rate.purity, rate.rate, rateId])
    }

    console.log('\n🎉 Complete Professional System Implementation Successful!')

  } catch (error) {
    console.error('\n❌ Complete professional implementation failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the complete professional implementation
implementCompleteProfessional().catch(console.error)
