#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedTiruppurInventory() {
  console.log('📦 Seeding Comprehensive Inventory for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Get category IDs for reference
    const [categories] = await connection.execute('SELECT id, category_code FROM categories')
    const categoryMap = new Map()
    ;(categories as any[]).forEach(cat => {
      categoryMap.set(cat.category_code, cat.id)
    })

    console.log('💎 Creating comprehensive jewelry inventory...')
    
    // Comprehensive jewelry inventory with realistic items
    const inventoryItems = [
      // Gold Rings
      {
        item_code: 'GR001',
        name: 'Gold Wedding Ring Classic',
        description: 'Traditional 22K gold wedding ring with elegant finish',
        category_id: categoryMap.get('GRING'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 8.500,
        stone_weight: 0.000,
        wastage_percentage: 10.00,
        purchase_rate: 6740.00,
        making_charges: 12000.00,
        selling_price: 85000.00,
        mrp: 90000.00,
        size: '18',
        gender: 'unisex',
        occasion: 'wedding',
        design_number: 'WR-001',
        location: 'Showcase-A1',
        hsn_code: '71131900',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      {
        item_code: 'GR002',
        name: 'Gold Diamond Ring Designer',
        description: '18K gold ring with 0.25ct diamond solitaire',
        category_id: categoryMap.get('GRING'),
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 6.200,
        stone_weight: 0.500,
        diamond_weight: 0.250,
        diamond_pieces: 1,
        wastage_percentage: 8.00,
        purchase_rate: 5510.00,
        making_charges: 15000.00,
        stone_charges: 35000.00,
        selling_price: 125000.00,
        mrp: 135000.00,
        size: '16',
        gender: 'female',
        occasion: 'engagement',
        design_number: 'DR-002',
        location: 'Showcase-A2',
        hsn_code: '71131900',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      // Gold Necklaces
      {
        item_code: 'GN001',
        name: 'Gold Chain Necklace Heavy',
        description: '22K gold chain necklace with traditional design',
        category_id: categoryMap.get('GNECK'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 45.000,
        stone_weight: 0.000,
        wastage_percentage: 12.00,
        purchase_rate: 6740.00,
        making_charges: 60000.00,
        selling_price: 420000.00,
        mrp: 450000.00,
        size: '20 inches',
        gender: 'female',
        occasion: 'traditional',
        design_number: 'CN-001',
        location: 'Showcase-B1',
        hsn_code: '71131100',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      {
        item_code: 'GN002',
        name: 'Gold Necklace Set Temple Design',
        description: '22K gold temple jewelry necklace set with matching earrings',
        category_id: categoryMap.get('TEMP'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 85.000,
        stone_weight: 15.000,
        colored_stone_pieces: 25,
        wastage_percentage: 15.00,
        purchase_rate: 6740.00,
        making_charges: 150000.00,
        stone_charges: 45000.00,
        selling_price: 750000.00,
        mrp: 800000.00,
        size: 'Medium',
        gender: 'female',
        occasion: 'festival',
        design_number: 'TN-002',
        location: 'Showcase-C1',
        hsn_code: '71131900',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      // Gold Earrings
      {
        item_code: 'GE001',
        name: 'Gold Earrings Stud Simple',
        description: '22K gold stud earrings with floral design',
        category_id: categoryMap.get('GEARR'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 4.500,
        stone_weight: 0.000,
        wastage_percentage: 8.00,
        purchase_rate: 6740.00,
        making_charges: 8000.00,
        selling_price: 42000.00,
        mrp: 45000.00,
        size: 'Small',
        gender: 'female',
        occasion: 'daily wear',
        design_number: 'SE-001',
        location: 'Showcase-D1',
        hsn_code: '71131200',
        requires_hallmarking: false,
        is_hallmarked: false
      },
      {
        item_code: 'GE002',
        name: 'Gold Earrings Drop Designer',
        description: '18K gold drop earrings with ruby stones',
        category_id: categoryMap.get('GEARR'),
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 8.200,
        stone_weight: 2.000,
        colored_stone_pieces: 6,
        wastage_percentage: 10.00,
        purchase_rate: 5510.00,
        making_charges: 12000.00,
        stone_charges: 18000.00,
        selling_price: 85000.00,
        mrp: 92000.00,
        size: 'Medium',
        gender: 'female',
        occasion: 'party',
        design_number: 'DE-002',
        location: 'Showcase-D2',
        hsn_code: '71131200',
        requires_hallmarking: false,
        is_hallmarked: false
      },
      // Gold Bangles
      {
        item_code: 'GB001',
        name: 'Gold Bangles Pair Traditional',
        description: '22K gold bangles pair with traditional Tamil design',
        category_id: categoryMap.get('GBANG'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 65.000,
        stone_weight: 0.000,
        wastage_percentage: 6.00,
        purchase_rate: 6740.00,
        making_charges: 55000.00,
        selling_price: 520000.00,
        mrp: 550000.00,
        size: '2.6',
        gender: 'female',
        occasion: 'traditional',
        design_number: 'TB-001',
        location: 'Showcase-E1',
        hsn_code: '71131300',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      // Silver Items
      {
        item_code: 'SN001',
        name: 'Silver Necklace Contemporary',
        description: '925 silver contemporary design necklace',
        category_id: categoryMap.get('SNECK'),
        metal_type: 'silver',
        purity: '925',
        gross_weight: 35.000,
        stone_weight: 5.000,
        colored_stone_pieces: 12,
        wastage_percentage: 10.00,
        purchase_rate: 85.50,
        making_charges: 5000.00,
        stone_charges: 8000.00,
        selling_price: 18000.00,
        mrp: 20000.00,
        size: '18 inches',
        gender: 'female',
        occasion: 'casual',
        design_number: 'SN-001',
        location: 'Showcase-F1',
        hsn_code: '71131100',
        requires_hallmarking: false,
        is_hallmarked: false
      },
      {
        item_code: 'SB001',
        name: 'Silver Bangles Set Heavy',
        description: '925 silver bangles set of 4 with oxidized finish',
        category_id: categoryMap.get('SBANG'),
        metal_type: 'silver',
        purity: '925',
        gross_weight: 120.000,
        stone_weight: 0.000,
        wastage_percentage: 5.00,
        purchase_rate: 85.50,
        making_charges: 8000.00,
        selling_price: 19000.00,
        mrp: 22000.00,
        size: '2.4',
        gender: 'female',
        occasion: 'traditional',
        design_number: 'SB-001',
        location: 'Showcase-F2',
        hsn_code: '71131300',
        requires_hallmarking: false,
        is_hallmarked: false
      },
      // Gold Chains
      {
        item_code: 'GC001',
        name: 'Gold Chain Mens Heavy',
        description: '22K gold chain for men with rope design',
        category_id: categoryMap.get('GCHAI'),
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 28.000,
        stone_weight: 0.000,
        wastage_percentage: 4.00,
        purchase_rate: 6740.00,
        making_charges: 15000.00,
        selling_price: 210000.00,
        mrp: 225000.00,
        size: '22 inches',
        gender: 'male',
        occasion: 'daily wear',
        design_number: 'MC-001',
        location: 'Showcase-G1',
        hsn_code: '71131400',
        requires_hallmarking: true,
        is_hallmarked: true
      },
      // Gold Coins
      {
        item_code: 'GCOIN001',
        name: 'Gold Coin Lakshmi 10g',
        description: '24K gold coin with Goddess Lakshmi design - 10 grams',
        category_id: categoryMap.get('GCOIN'),
        metal_type: 'gold',
        purity: '24K',
        gross_weight: 10.000,
        stone_weight: 0.000,
        wastage_percentage: 0.00,
        purchase_rate: 7350.00,
        making_charges: 300.00,
        selling_price: 73800.00,
        mrp: 75000.00,
        size: '10g',
        gender: 'unisex',
        occasion: 'investment',
        design_number: 'LC-10G',
        location: 'Safe-Vault',
        hsn_code: '71131000',
        requires_hallmarking: true,
        is_hallmarked: true
      }
    ]

    // Insert inventory items
    for (const item of inventoryItems) {
      await connection.execute(`
        INSERT INTO inventory (
          id, item_code, name, description, category_id, metal_type, purity,
          gross_weight, stone_weight, diamond_weight, diamond_pieces, colored_stone_pieces,
          wastage_percentage, purchase_rate, making_charges, stone_charges, other_charges,
          selling_price, mrp, size, gender, occasion, design_number, location,
          hsn_code, requires_hallmarking, is_hallmarked, status, condition_status,
          stock_quantity, min_stock_level, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', 'new', 1, 1, NOW(), NOW())
      `, [
        randomUUID(), item.item_code, item.name, item.description, item.category_id,
        item.metal_type, item.purity, item.gross_weight, item.stone_weight,
        item.diamond_weight || 0.000, item.diamond_pieces || 0, item.colored_stone_pieces || 0,
        item.wastage_percentage, item.purchase_rate, item.making_charges,
        item.stone_charges || 0.00, item.other_charges || 0.00, item.selling_price, item.mrp,
        item.size, item.gender, item.occasion, item.design_number, item.location,
        item.hsn_code, item.requires_hallmarking, item.is_hallmarked
      ])
    }

    console.log(`   ✅ Created ${inventoryItems.length} comprehensive jewelry inventory items`)

    console.log('\n🎉 Comprehensive Inventory Seeded Successfully for JJ Jewellers Tiruppur!')

  } catch (error) {
    console.error('\n❌ Tiruppur inventory seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the Tiruppur inventory seeding
seedTiruppurInventory().catch(console.error)
