"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useStore } from "@/lib/store"
import type { Purchase } from "@/lib/types"

interface PurchaseFormProps {
  purchase?: Purchase
  onSubmit: () => void
  onCancel: () => void
}

export function PurchaseForm({ purchase, onSubmit, onCancel }: PurchaseFormProps) {
  const { addPurchase, updatePurchase } = useStore()
  const [formData, setFormData] = useState({
    supplier: "",
    items: "",
    amount: "",
    status: "pending",
    date: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (purchase) {
      setFormData({
        supplier: purchase.supplier,
        items: purchase.items,
        amount: purchase.amount.toString(),
        status: purchase.status,
        date: purchase.date,
      })
    } else {
      // Set default date to today
      const today = new Date().toISOString().split("T")[0]
      setFormData((prev) => ({ ...prev, date: today }))
    }
  }, [purchase])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.supplier.trim()) {
      newErrors.supplier = "Supplier name is required"
    }

    if (!formData.items.trim()) {
      newErrors.items = "Items description is required"
    }

    if (!formData.amount || Number.parseFloat(formData.amount) <= 0) {
      newErrors.amount = "Amount must be greater than 0"
    }

    if (!formData.date) {
      newErrors.date = "Purchase date is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const purchaseData = {
        supplier: formData.supplier.trim(),
        items: formData.items.trim(),
        amount: Number.parseFloat(formData.amount),
        status: formData.status as "pending" | "received" | "cancelled",
        date: formData.date,
      }

      if (purchase) {
        await updatePurchase(purchase.id, purchaseData)
      } else {
        await addPurchase(purchaseData)
      }

      // Reset form
      setFormData({
        supplier: "",
        items: "",
        amount: "",
        status: "pending",
        date: "",
      })
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving purchase:", error)
      setErrors({ submit: "Failed to save purchase. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="supplier">Supplier Name *</Label>
          <Input
            id="supplier"
            value={formData.supplier}
            onChange={(e) => handleInputChange("supplier", e.target.value)}
            placeholder="ABC Gold Suppliers"
            required
            className={errors.supplier ? "border-red-500" : ""}
          />
          {errors.supplier && <p className="text-sm text-red-500">{errors.supplier}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="date">Purchase Date *</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange("date", e.target.value)}
            required
            className={errors.date ? "border-red-500" : ""}
          />
          {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="items">Items Description *</Label>
        <Textarea
          id="items"
          value={formData.items}
          onChange={(e) => handleInputChange("items", e.target.value)}
          placeholder="Gold bars 22K - 100g, Silver coins 925 - 50 pieces"
          required
          className={errors.items ? "border-red-500" : ""}
          rows={3}
        />
        {errors.items && <p className="text-sm text-red-500">{errors.items}</p>}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount (₹) *</Label>
          <Input
            id="amount"
            type="number"
            step="0.01"
            min="0"
            value={formData.amount}
            onChange={(e) => handleInputChange("amount", e.target.value)}
            placeholder="150000"
            required
            className={errors.amount ? "border-red-500" : ""}
          />
          {errors.amount && <p className="text-sm text-red-500">{errors.amount}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="received">Received</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : purchase ? "Update Purchase" : "Add Purchase"}
        </Button>
      </div>
    </form>
  )
}
