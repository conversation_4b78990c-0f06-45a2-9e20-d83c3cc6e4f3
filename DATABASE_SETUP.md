# Database Setup Guide

This guide will help you set up the MySQL database for the Jewellers Software application.

## Prerequisites

1. **MySQL Server**: Install MySQL 8.0 or higher
2. **Node.js**: Version 18 or higher
3. **pnpm**: Package manager (or npm/yarn)

## Database Configuration

1. **Create a MySQL database**:
   ```sql
   CREATE DATABASE jewellers_db;
   CREATE USER 'jeweller_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON jewellers_db.* TO 'jeweller_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Configure environment variables**:
   Copy `.env.example` to `.env.local` and update the database credentials:
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local`:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=jeweller_user
   DB_PASSWORD=your_password
   DB_NAME=jewellers_db
   DATABASE_URL="mysql://jeweller_user:your_password@localhost:3306/jewellers_db"
   ```

## Installation and Setup

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Initialize the database**:
   ```bash
   pnpm run db:init
   ```
   
   This command will:
   - Create all necessary tables
   - Seed initial data (users, sample customers, inventory items)
   - Set up default settings

3. **Start the application**:
   ```bash
   pnpm run dev
   ```

## Database Scripts

- `pnpm run db:init` - Initialize database with schema and seed data
- `pnpm run db:reset` - Reset database (drops all tables and recreates them)
- `pnpm run db:seed` - Same as db:init (for consistency)

## Default Users

After initialization, you can log in with these default accounts:

### Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Permissions**: Full access to all modules

### Manager User
- **Email**: <EMAIL>
- **Password**: manager123
- **Permissions**: Most modules except user management

### Staff User
- **Email**: <EMAIL>
- **Password**: staff123
- **Permissions**: Limited access (inventory read, customer management, sales)

## Database Schema

The application uses the following main tables:

- `users` - User accounts and permissions
- `customers` - Customer information
- `inventory` - Jewelry items and stock
- `sales` - Sales transactions
- `sale_items` - Individual items in sales
- `schemes` - Customer schemes/plans
- `repairs` - Repair orders
- `purchases` - Purchase orders
- `settings` - Application settings
- `migrations` - Database migration tracking

## Troubleshooting

### Connection Issues

1. **Check MySQL service**:
   ```bash
   # On Ubuntu/Debian
   sudo systemctl status mysql
   
   # On macOS with Homebrew
   brew services list | grep mysql
   
   # On Windows
   net start mysql
   ```

2. **Verify database credentials**:
   ```bash
   mysql -u jeweller_user -p jewellers_db
   ```

3. **Check firewall settings**: Ensure MySQL port (3306) is accessible

### Permission Issues

If you get permission errors, ensure the database user has proper privileges:
```sql
GRANT ALL PRIVILEGES ON jewellers_db.* TO 'jeweller_user'@'localhost';
FLUSH PRIVILEGES;
```

### Migration Issues

If database initialization fails:
1. Check the error logs in the console
2. Verify database connection settings
3. Try resetting the database: `pnpm run db:reset`

## Production Deployment

For production deployment:

1. Use a dedicated MySQL server
2. Update connection settings in production environment
3. Use strong passwords and secure connection strings
4. Consider using connection pooling for better performance
5. Set up regular database backups

## Backup and Restore

### Manual Backup
```bash
mysqldump -u jeweller_user -p jewellers_db > backup.sql
```

### Restore from Backup
```bash
mysql -u jeweller_user -p jewellers_db < backup.sql
```

The application also includes built-in backup functionality in the settings panel.
