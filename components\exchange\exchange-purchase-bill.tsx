"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Download, Printer, FileText, X } from "lucide-react"
import { ExchangePurchaseBill, ExchangeTransaction } from "@/lib/types"
import { formatCurrency, formatDate } from "@/lib/utils"

interface ExchangePurchaseBillProps {
  exchangeTransaction: ExchangeTransaction
  onClose: () => void
  onBillGenerated: (bill: ExchangePurchaseBill) => void
}

export function ExchangePurchaseBill({ exchangeTransaction, onClose, onBillGenerated }: ExchangePurchaseBillProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedBill, setGeneratedBill] = useState<ExchangePurchaseBill | null>(null)

  // Mock settings - replace with actual settings from context/store
  const settings = {
    businessName: "S<PERSON>ee <PERSON>lers",
    address: "123 Main Street, Mumbai, Maharashtra 400001",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    gstNumber: "27ABCDE1234F1Z5",
    cgstRate: 1.5,
    sgstRate: 1.5
  }

  const handleGenerateBill = async () => {
    setIsGenerating(true)
    try {
      // Mock API call - replace with actual implementation
      const cgstAmount = (exchangeTransaction.totalAmount * settings.cgstRate) / 100
      const sgstAmount = (exchangeTransaction.totalAmount * settings.sgstRate) / 100
      const totalWithTax = exchangeTransaction.totalAmount + cgstAmount + sgstAmount

      const mockBill: ExchangePurchaseBill = {
        id: `bill_${Date.now()}`,
        billNumber: `EPB/2024-25/${String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0')}`,
        exchangeTransactionId: exchangeTransaction.id,
        exchangeTransaction,
        customerId: exchangeTransaction.customerId,
        customer: exchangeTransaction.customer,
        billDate: new Date().toISOString().split('T')[0],
        totalAmount: exchangeTransaction.totalAmount,
        cgstAmount,
        sgstAmount,
        totalWithTax,
        paymentMethod: exchangeTransaction.paymentMethod,
        paymentStatus: 'pending',
        notes: `Purchase bill for exchange transaction ${exchangeTransaction.transactionNumber}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      setGeneratedBill(mockBill)
      onBillGenerated(mockBill)
    } catch (error) {
      console.error('Error generating bill:', error)
      alert('Failed to generate bill')
    } finally {
      setIsGenerating(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const handleDownloadPDF = () => {
    // Mock PDF generation - replace with actual implementation
    alert('PDF download functionality will be implemented with actual backend')
  }

  const bill = generatedBill

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[95vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Exchange Purchase Bill
            </CardTitle>
            <CardDescription>
              {bill ? `Bill ${bill.billNumber}` : 'Generate purchase bill for exchange transaction'}
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {!bill ? (
            // Bill Generation Preview
            <div className="space-y-6">
              <div className="text-center p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Generate Purchase Bill</h3>
                <p className="text-muted-foreground mb-4">
                  Create a purchase bill for exchange transaction {exchangeTransaction.transactionNumber}
                </p>
                <Button onClick={handleGenerateBill} disabled={isGenerating}>
                  {isGenerating ? 'Generating...' : 'Generate Bill'}
                </Button>
              </div>

              {/* Transaction Preview */}
              <Card>
                <CardHeader>
                  <CardTitle>Transaction Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium">Transaction Number:</p>
                      <p>{exchangeTransaction.transactionNumber}</p>
                    </div>
                    <div>
                      <p className="font-medium">Date:</p>
                      <p>{formatDate(exchangeTransaction.transactionDate)}</p>
                    </div>
                    <div>
                      <p className="font-medium">Customer:</p>
                      <p>{exchangeTransaction.customer?.name || 'Walk-in Customer'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Total Amount:</p>
                      <p className="font-semibold">{formatCurrency(exchangeTransaction.totalAmount)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            // Generated Bill Display
            <div className="space-y-6" id="bill-content">
              {/* Bill Header */}
              <div className="text-center border-b pb-6">
                <h1 className="text-2xl font-bold">{settings.businessName}</h1>
                <p className="text-sm text-muted-foreground">{settings.address}</p>
                <p className="text-sm text-muted-foreground">
                  Phone: {settings.phone} | Email: {settings.email}
                </p>
                <p className="text-sm text-muted-foreground">GST No: {settings.gstNumber}</p>
                <div className="mt-4">
                  <Badge variant="outline" className="text-lg px-4 py-2">
                    PURCHASE BILL
                  </Badge>
                </div>
              </div>

              {/* Bill Details */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Bill Details</h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Bill No:</span> {bill.billNumber}</p>
                    <p><span className="font-medium">Date:</span> {formatDate(bill.billDate)}</p>
                    <p><span className="font-medium">Exchange Ref:</span> {exchangeTransaction.transactionNumber}</p>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Customer Details</h3>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Name:</span> {bill.customer?.name || 'Walk-in Customer'}</p>
                    {bill.customer?.phone && (
                      <p><span className="font-medium">Phone:</span> {bill.customer.phone}</p>
                    )}
                    {bill.customer?.address && (
                      <p><span className="font-medium">Address:</span> {bill.customer.address}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Items Table */}
              <div>
                <h3 className="font-semibold mb-4">Exchange Items</h3>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-muted">
                      <tr className="text-left">
                        <th className="p-3 text-sm font-medium">Description</th>
                        <th className="p-3 text-sm font-medium">Metal/Purity</th>
                        <th className="p-3 text-sm font-medium">Gross Wt (g)</th>
                        <th className="p-3 text-sm font-medium">Net Wt (g)</th>
                        <th className="p-3 text-sm font-medium">Rate/g</th>
                        <th className="p-3 text-sm font-medium text-right">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {exchangeTransaction.items.map((item, index) => (
                        <tr key={index} className="border-t">
                          <td className="p-3 text-sm">{item.itemDescription}</td>
                          <td className="p-3 text-sm">{item.metalType} {item.purity}</td>
                          <td className="p-3 text-sm">{item.grossWeight.toFixed(3)}</td>
                          <td className="p-3 text-sm">{item.netWeight.toFixed(3)}</td>
                          <td className="p-3 text-sm">₹{item.ratePerGram.toFixed(2)}</td>
                          <td className="p-3 text-sm text-right">{formatCurrency(item.amount)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-80 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span>{formatCurrency(bill.totalAmount)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>CGST ({settings.cgstRate}%):</span>
                    <span>{formatCurrency(bill.cgstAmount)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>SGST ({settings.sgstRate}%):</span>
                    <span>{formatCurrency(bill.sgstAmount)}</span>
                  </div>
                  <div className="border-t my-2"></div>
                  <div className="flex justify-between font-semibold">
                    <span>Total:</span>
                    <span>{formatCurrency(bill.totalWithTax)}</span>
                  </div>
                </div>
              </div>

              {/* Payment Details */}
              <div className="border-t pt-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><span className="font-medium">Payment Method:</span> {bill.paymentMethod}</p>
                    <p><span className="font-medium">Payment Status:</span> 
                      <Badge variant={bill.paymentStatus === 'paid' ? 'default' : 'secondary'} className="ml-2">
                        {bill.paymentStatus}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">
                      This is a computer generated bill and does not require signature.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4 border-t print:hidden">
                <Button variant="outline" onClick={handlePrint}>
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
                <Button variant="outline" onClick={handleDownloadPDF}>
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                <Button onClick={onClose}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
