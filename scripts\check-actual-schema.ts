#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function checkActualSchema() {
  console.log('🔍 Checking Actual Database Schema...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Check all table structures
    const tables = ['inventory', 'customers', 'sales', 'purchases', 'schemes', 'repairs']
    
    for (const tableName of tables) {
      console.log(`📋 ${tableName.toUpperCase()} table structure:`)
      
      try {
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
          ORDER BY ORDINAL_POSITION
        `, [dbConfig.database, tableName])

        ;(columns as any[]).forEach(col => {
          const nullable = col.IS_NULLABLE === 'YES' ? '(nullable)' : '(required)'
          const defaultVal = col.COLUMN_DEFAULT ? ` default: ${col.COLUMN_DEFAULT}` : ''
          console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}${defaultVal}`)
        })
        
        // Show sample data
        const [sampleData] = await connection.execute(`SELECT * FROM ${tableName} LIMIT 1`)
        if ((sampleData as any[]).length > 0) {
          console.log('   Sample data fields:')
          Object.keys((sampleData as any[])[0]).forEach(key => {
            const value = (sampleData as any[])[0][key]
            const type = typeof value
            const isNull = value === null ? ' (NULL)' : ''
            console.log(`      ${key}: ${type}${isNull}`)
          })
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking ${tableName}: ${error}`)
      }
      
      console.log('')
    }

    console.log('🎉 Schema Check Completed!')

  } catch (error) {
    console.error('\n❌ Schema check failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the schema check
checkActualSchema().catch(console.error)
