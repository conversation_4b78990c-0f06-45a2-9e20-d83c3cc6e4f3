"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  Play, 
  ArrowRight, 
  Scale, 
  FileText, 
  ShoppingCart, 
  CheckCircle,
  Star
} from "lucide-react"
import { formatCurrency } from "@/lib/utils"

export function ExchangeDemo() {
  const [currentStep, setCurrentStep] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)

  const demoSteps = [
    {
      title: "Customer Brings Old Gold",
      description: "Customer brings old gold jewelry for exchange",
      icon: Scale,
      details: {
        customer: "<PERSON><PERSON>",
        items: [
          { description: "Old Gold Chain", weight: "15.5g", purity: "22K", condition: "Good" },
          { description: "Gold Ring", weight: "8.2g", purity: "18K", condition: "Fair" }
        ]
      }
    },
    {
      title: "Exchange Transaction Created",
      description: "Staff creates exchange transaction with item details",
      icon: FileText,
      details: {
        transactionNumber: "EXG-20240131-001",
        totalWeight: "23.7g",
        totalValue: "₹85,000",
        status: "Completed"
      }
    },
    {
      title: "Purchase Bill Generated",
      description: "System generates purchase bill for compliance",
      icon: FileText,
      details: {
        billNumber: "EPB/2024-25/0001",
        subtotal: "₹85,000",
        cgst: "₹1,275",
        sgst: "₹1,275",
        total: "₹87,550"
      }
    },
    {
      title: "Customer Makes New Purchase",
      description: "Customer selects new jewelry items",
      icon: ShoppingCart,
      details: {
        items: [
          { name: "Gold Necklace Set", amount: "₹1,20,000" },
          { name: "Gold Earrings", amount: "₹35,000" }
        ],
        subtotal: "₹1,55,000",
        tax: "₹4,650",
        total: "₹1,59,650"
      }
    },
    {
      title: "Exchange Applied to Sale",
      description: "Old gold value deducted from new purchase",
      icon: CheckCircle,
      details: {
        saleTotal: "₹1,59,650",
        exchangeDeduction: "₹85,000",
        finalAmount: "₹74,650",
        savings: "₹85,000"
      }
    }
  ]

  const playDemo = () => {
    setIsPlaying(true)
    setCurrentStep(0)
    
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= demoSteps.length - 1) {
          clearInterval(interval)
          setIsPlaying(false)
          return prev
        }
        return prev + 1
      })
    }, 2000)
  }

  const resetDemo = () => {
    setCurrentStep(0)
    setIsPlaying(false)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2">
          <Star className="h-6 w-6 text-yellow-500" />
          <h1 className="text-3xl font-bold">Exchange System Demo</h1>
          <Star className="h-6 w-6 text-yellow-500" />
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          See how the complete exchange workflow integrates with billing to provide 
          seamless old gold/silver exchange with transparent invoicing.
        </p>
        <div className="flex justify-center gap-2">
          <Button onClick={playDemo} disabled={isPlaying}>
            <Play className="h-4 w-4 mr-2" />
            {isPlaying ? 'Playing Demo...' : 'Play Demo'}
          </Button>
          <Button variant="outline" onClick={resetDemo}>
            Reset
          </Button>
        </div>
      </div>

      {/* Demo Flow */}
      <div className="max-w-4xl mx-auto">
        <div className="relative">
          {/* Progress Line */}
          <div className="absolute top-8 left-8 right-8 h-0.5 bg-muted">
            <div 
              className="h-full bg-primary transition-all duration-500"
              style={{ width: `${(currentStep / (demoSteps.length - 1)) * 100}%` }}
            />
          </div>

          {/* Steps */}
          <div className="relative grid grid-cols-1 md:grid-cols-5 gap-4">
            {demoSteps.map((step, index) => {
              const Icon = step.icon
              const isActive = index <= currentStep
              const isCurrent = index === currentStep

              return (
                <div key={index} className="relative">
                  {/* Step Circle */}
                  <div className={`
                    w-16 h-16 rounded-full border-4 flex items-center justify-center mx-auto mb-4 transition-all duration-500
                    ${isActive 
                      ? 'bg-primary border-primary text-primary-foreground' 
                      : 'bg-background border-muted text-muted-foreground'
                    }
                    ${isCurrent ? 'ring-4 ring-primary/20 scale-110' : ''}
                  `}>
                    <Icon className="h-6 w-6" />
                  </div>

                  {/* Step Card */}
                  <Card className={`
                    transition-all duration-500 transform
                    ${isCurrent ? 'ring-2 ring-primary scale-105' : ''}
                    ${isActive ? 'opacity-100' : 'opacity-60'}
                  `}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-center">{step.title}</CardTitle>
                      <CardDescription className="text-xs text-center">
                        {step.description}
                      </CardDescription>
                    </CardHeader>
                    
                    {isCurrent && (
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-xs">
                          {index === 0 && (
                            <div>
                              <p className="font-medium">Customer: {step.details.customer}</p>
                              {step.details.items.map((item: any, i: number) => (
                                <div key={i} className="bg-muted/50 p-2 rounded mt-1">
                                  <p className="font-medium">{item.description}</p>
                                  <p className="text-muted-foreground">
                                    {item.weight} • {item.purity} • {item.condition}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                          
                          {index === 1 && (
                            <div className="space-y-1">
                              <p><span className="font-medium">Transaction:</span> {step.details.transactionNumber}</p>
                              <p><span className="font-medium">Total Weight:</span> {step.details.totalWeight}</p>
                              <p><span className="font-medium">Value:</span> {step.details.totalValue}</p>
                              <Badge variant="default" className="text-xs">
                                {step.details.status}
                              </Badge>
                            </div>
                          )}
                          
                          {index === 2 && (
                            <div className="space-y-1">
                              <p><span className="font-medium">Bill:</span> {step.details.billNumber}</p>
                              <p><span className="font-medium">Subtotal:</span> {step.details.subtotal}</p>
                              <p><span className="font-medium">CGST:</span> {step.details.cgst}</p>
                              <p><span className="font-medium">SGST:</span> {step.details.sgst}</p>
                              <p className="font-medium border-t pt-1">
                                <span>Total:</span> {step.details.total}
                              </p>
                            </div>
                          )}
                          
                          {index === 3 && (
                            <div>
                              {step.details.items.map((item: any, i: number) => (
                                <div key={i} className="bg-muted/50 p-2 rounded mb-1">
                                  <div className="flex justify-between">
                                    <span className="font-medium">{item.name}</span>
                                    <span>{item.amount}</span>
                                  </div>
                                </div>
                              ))}
                              <div className="border-t pt-2 mt-2 space-y-1">
                                <div className="flex justify-between">
                                  <span>Subtotal:</span>
                                  <span>{step.details.subtotal}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Tax:</span>
                                  <span>{step.details.tax}</span>
                                </div>
                                <div className="flex justify-between font-medium">
                                  <span>Total:</span>
                                  <span>{step.details.total}</span>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {index === 4 && (
                            <div className="space-y-2">
                              <div className="bg-green-50 p-2 rounded border border-green-200">
                                <div className="flex justify-between text-sm">
                                  <span>Sale Total:</span>
                                  <span>{step.details.saleTotal}</span>
                                </div>
                                <div className="flex justify-between text-sm text-green-600">
                                  <span>Exchange Deduction:</span>
                                  <span>-{step.details.exchangeDeduction}</span>
                                </div>
                                <div className="flex justify-between font-bold text-lg border-t border-green-200 pt-1 mt-1">
                                  <span>Final Amount:</span>
                                  <span>{step.details.finalAmount}</span>
                                </div>
                              </div>
                              <div className="text-center">
                                <Badge variant="default" className="bg-green-500">
                                  Customer Saves {step.details.savings}!
                                </Badge>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    )}
                  </Card>

                  {/* Arrow */}
                  {index < demoSteps.length - 1 && (
                    <div className="hidden md:block absolute top-8 -right-2 z-10">
                      <ArrowRight className={`
                        h-4 w-4 transition-colors duration-500
                        ${isActive ? 'text-primary' : 'text-muted-foreground'}
                      `} />
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Key Benefits</CardTitle>
          <CardDescription className="text-center">
            Why this integrated exchange system is perfect for jewelry businesses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold">Compliance Ready</h3>
              <p className="text-sm text-muted-foreground">
                Automatic purchase bill generation with GST calculations for complete compliance
              </p>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold">Transparent Billing</h3>
              <p className="text-sm text-muted-foreground">
                Clear invoices showing both new items and exchange deductions for customer trust
              </p>
            </div>
            
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Scale className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold">Complete Audit Trail</h3>
              <p className="text-sm text-muted-foreground">
                Track every exchange from creation to utilization with full audit history
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold">Ready to Try It?</h2>
        <p className="text-muted-foreground">
          Navigate to the Exchange tab to start creating exchange transactions and see the system in action!
        </p>
        <div className="flex justify-center gap-2">
          <Button size="lg">
            <Scale className="h-4 w-4 mr-2" />
            Go to Exchange
          </Button>
          <Button variant="outline" size="lg">
            <ShoppingCart className="h-4 w-4 mr-2" />
            Try Sales with Exchange
          </Button>
        </div>
      </div>
    </div>
  )
}
