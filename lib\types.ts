export interface InventoryItem {
  id: string
  name: string
  category: string
  metalType: string
  grossWeight: number // Total weight including stones
  stoneWeight: number
  netWeight: number // Metal weight only (grossWeight - stoneWeight)
  stoneAmount: number
  purity: string
  makingCharges: number
  currentValue: number
  stock: number
  stoneDetails: string
  description: string
  hsnCode?: string // HSN code for tax purposes
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  name: string
  phone: string
  email: string
  address: string
  gstNumber?: string
  totalPurchases: number
  lastVisit: string
  createdAt: string
  updatedAt: string
}

export interface Sale {
  id: string
  customer: Customer
  items: SaleItem[]
  subtotal: number
  cgst: number
  sgst: number
  total: number
  status: "draft" | "paid" | "pending" | "cancelled"
  date: string
  createdAt: string
  updatedAt: string
}

export interface SaleItem {
  id: string
  item: InventoryItem
  grossWeight: number
  stoneWeight: number
  netWeight: number // Add net weight to sale items
  rate: number
  makingCharges: number
  stoneAmount: number
  amount: number
}

export interface Scheme {
  id: string
  name: string
  customer: Customer
  totalAmount: number
  paidAmount: number
  monthlyAmount: number
  duration: number
  startDate: string
  status: "active" | "completed" | "cancelled"
  createdAt: string
  updatedAt: string
}

export interface RepairOrder {
  id: string
  customer: Customer
  item: string
  description: string
  orderType: "repair" | "custom" | "resize" | "polish"
  receivedDate: string
  promisedDate: string
  status: "pending" | "in-progress" | "completed" | "delivered"
  charges: number
  specialInstructions?: string
  createdAt: string
  updatedAt: string
}

export interface Purchase {
  id: string
  supplier: string
  items: string
  amount: number
  status: "pending" | "received" | "cancelled"
  date: string
  createdAt: string
  updatedAt: string
}

export interface MetalRates {
  gold: {
    "22K": string
    "18K": string
  }
  silver: {
    "925": string
  }
}

export interface User {
  id: string
  name: string
  email: string
  role: "admin" | "manager" | "staff"
  permissions: Permission[]
  createdAt: string
  updatedAt: string
}

export interface Permission {
  module: string
  actions: ("create" | "read" | "update" | "delete" | "export")[]
}

export interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  sortOrder: number
  image?: string
  tags: string[]
  makingChargePercentage: number
  wastagePercentage: number
  createdAt: string
  updatedAt: string
}
