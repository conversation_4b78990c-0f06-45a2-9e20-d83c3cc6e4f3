export interface InventoryItem {
  id: string
  item_code?: string
  name: string
  description?: string
  category?: string
  category_id?: string
  metal_type?: string
  purity?: string

  // Weight information (optional as they might be null in DB)
  gross_weight?: number
  stone_weight?: number
  net_weight?: number
  diamond_weight?: number
  wastage_weight?: number
  chargeable_weight?: number

  // Pricing information (optional as they might be null in DB)
  purchase_rate?: number
  metal_amount?: number
  making_charges?: number
  stone_charges?: number
  other_charges?: number
  total_cost?: number
  selling_price?: number
  mrp?: number

  // Additional information
  size?: string
  gender?: string
  occasion?: string
  design_number?: string
  location?: string
  hsn_code?: string

  // Status and inventory
  status?: string
  condition_status?: string
  stock_quantity?: number
  min_stock_level?: number

  // Hallmarking
  requires_hallmarking?: boolean
  is_hallmarked?: boolean
  hallmark_number?: string

  // Audit fields
  created_at?: string
  updated_at?: string
  created_by?: string

  // Legacy fields for backward compatibility (optional)
  stoneAmount?: number // mapped from stone_charges
  currentValue?: number // mapped from selling_price
  stock?: number // mapped from stock_quantity
  stoneDetails?: string // mapped from description
  hsnCode?: string // mapped from hsn_code
  metalType?: string // mapped from metal_type
  grossWeight?: number // mapped from gross_weight
  stoneWeight?: number // mapped from stone_weight
  netWeight?: number // mapped from net_weight
  makingCharges?: number // mapped from making_charges
  createdAt?: string
  updatedAt?: string
}

export interface Customer {
  id: string
  customer_code?: string
  first_name?: string
  last_name?: string
  name?: string // computed field
  phone?: string
  email?: string
  address?: string
  address_line1?: string
  address_line2?: string
  city?: string
  state?: string
  postal_code?: string
  gst_number?: string
  pan_number?: string
  total_purchases?: number
  loyalty_points?: number
  loyalty_tier?: string
  preferred_language?: string
  last_purchase_date?: string
  created_at?: string
  updated_at?: string

  // Legacy fields for backward compatibility
  gstNumber?: string // mapped from gst_number
  totalPurchases?: number // mapped from total_purchases
  lastVisit?: string // mapped from last_purchase_date
  createdAt?: string
  updatedAt?: string
}

export interface Sale {
  id: string
  invoice_number?: string
  customer?: Customer
  customer_id?: string
  customer_name?: string
  items?: SaleItem[]
  subtotal?: number
  discount_amount?: number
  cgst?: number
  sgst?: number
  igst?: number
  total?: number
  grand_total?: number
  final_amount?: number
  status?: "draft" | "paid" | "pending" | "cancelled" | "confirmed"
  payment_status?: string
  sale_type?: string
  payment_mode?: string
  date?: string
  invoice_date?: string
  created_at?: string
  updated_at?: string

  // Exchange integration fields
  exchangeItems?: SalesExchangeItem[]
  exchangeDeduction?: number
  exchange_deduction?: number
  finalTotal?: number // total - exchangeDeduction

  // Legacy fields for backward compatibility
  createdAt?: string
  updatedAt?: string
}

export interface SaleItem {
  id: string
  item: InventoryItem
  grossWeight: number
  stoneWeight: number
  netWeight: number // Add net weight to sale items
  rate: number
  makingCharges: number
  stoneAmount: number
  amount: number
}

export interface Scheme {
  id: string
  name: string
  customer: Customer
  totalAmount: number
  paidAmount: number
  monthlyAmount: number
  duration: number
  startDate: string
  status: "active" | "completed" | "cancelled"
  createdAt: string
  updatedAt: string
}

export interface RepairOrder {
  id: string
  customer: Customer
  item: string
  description: string
  orderType: "repair" | "custom" | "resize" | "polish"
  receivedDate: string
  promisedDate: string
  status: "pending" | "in-progress" | "completed" | "delivered"
  charges: number
  specialInstructions?: string
  createdAt: string
  updatedAt: string
}

export interface Purchase {
  id: string
  supplier: string
  items: string
  amount: number
  status: "pending" | "received" | "cancelled"
  date: string
  createdAt: string
  updatedAt: string
}

export interface MetalRates {
  gold: {
    "22K": string
    "18K": string
  }
  silver: {
    "925": string
  }
}

export interface User {
  id: string
  name: string
  email: string
  role: "admin" | "manager" | "staff"
  permissions?: Permission[] | null
  createdAt: string
  updatedAt: string
}

export interface Permission {
  module: string
  actions: ("create" | "read" | "update" | "delete" | "export")[]
}

export interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  sortOrder: number
  image?: string
  tags: string[]
  makingChargePercentage: number
  wastagePercentage: number
  createdAt: string
  updatedAt: string
}

// Exchange System Types
export interface ExchangeRate {
  id: string
  metalType: 'gold' | 'silver'
  purity: string
  ratePerGram: number
  effectiveDate: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ExchangeTransaction {
  id: string
  transactionNumber: string
  customerId?: string
  customer?: Customer
  transactionDate: string
  totalAmount: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  notes?: string
  status: 'pending' | 'completed' | 'cancelled'
  items: ExchangeItem[]
  createdBy?: string
  createdAt: string
  updatedAt: string
  // Billing integration fields
  purchaseBillId?: string
  purchaseBillNumber?: string
  purchaseBillGenerated?: boolean
  purchaseBillDate?: string
}

export interface ExchangeItem {
  id: string
  transactionId: string
  itemDescription: string
  metalType: 'gold' | 'silver'
  purity: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  ratePerGram: number
  amount: number
  itemCondition: 'good' | 'fair' | 'poor'
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeRateHistory {
  id: string
  metalType: 'gold' | 'silver'
  purity: string
  oldRate?: number
  newRate: number
  changeReason?: string
  effectiveDate: string
  changedBy?: string
  createdAt: string
}

// Exchange Billing Integration Types
export interface ExchangePurchaseBill {
  id: string
  billNumber: string
  exchangeTransactionId: string
  exchangeTransaction?: ExchangeTransaction
  customerId?: string
  customer?: Customer
  billDate: string
  totalAmount: number
  cgstAmount: number
  sgstAmount: number
  totalWithTax: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  paymentStatus: 'pending' | 'paid' | 'partial'
  notes?: string
  createdBy?: string
  createdAt: string
  updatedAt: string
}

export interface SalesExchangeItem {
  id: string
  saleId: string
  exchangeTransactionId: string
  exchangeItemId: string
  exchangeItem?: ExchangeItem
  deductionAmount: number
  appliedRate: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeVoucher {
  id: string
  voucherNumber: string
  exchangeTransactionId: string
  exchangeTransaction?: ExchangeTransaction
  voucherType: 'purchase' | 'exchange' | 'adjustment'
  voucherDate: string
  customerId?: string
  customer?: Customer
  totalAmount: number
  description?: string
  termsConditions?: string
  validityDate?: string
  status: 'active' | 'used' | 'expired' | 'cancelled'
  usedInSaleId?: string
  createdBy?: string
  createdAt: string
  updatedAt: string
}

export interface BillSequence {
  id: string
  sequenceType: 'exchange_purchase' | 'exchange_voucher' | 'sales' | 'purchase'
  prefix: string
  currentNumber: number
  financialYear: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeAuditTrail {
  id: string
  exchangeTransactionId: string
  actionType: 'created' | 'updated' | 'billed' | 'voucher_generated' | 'used_in_sale' | 'cancelled'
  actionDescription: string
  oldValues?: any
  newValues?: any
  relatedBillId?: string
  relatedSaleId?: string
  performedBy?: string
  performedAt: string
}
