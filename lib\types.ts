export interface InventoryItem {
  id: string
  name: string
  category: string
  metalType: string
  grossWeight: number // Total weight including stones
  stoneWeight: number
  netWeight: number // Metal weight only (grossWeight - stoneWeight)
  stoneAmount: number
  purity: string
  makingCharges: number
  currentValue: number
  stock: number
  stoneDetails: string
  description: string
  hsnCode?: string // HSN code for tax purposes
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  name: string
  phone: string
  email: string
  address: string
  gstNumber?: string
  totalPurchases: number
  lastVisit: string
  createdAt: string
  updatedAt: string
}

export interface Sale {
  id: string
  customer: Customer
  items: SaleItem[]
  subtotal: number
  cgst: number
  sgst: number
  total: number
  status: "draft" | "paid" | "pending" | "cancelled"
  date: string
  createdAt: string
  updatedAt: string
  // Exchange integration fields
  exchangeItems?: SalesExchangeItem[]
  exchangeDeduction?: number
  finalTotal?: number // total - exchangeDeduction
}

export interface SaleItem {
  id: string
  item: InventoryItem
  grossWeight: number
  stoneWeight: number
  netWeight: number // Add net weight to sale items
  rate: number
  makingCharges: number
  stoneAmount: number
  amount: number
}

export interface Scheme {
  id: string
  name: string
  customer: Customer
  totalAmount: number
  paidAmount: number
  monthlyAmount: number
  duration: number
  startDate: string
  status: "active" | "completed" | "cancelled"
  createdAt: string
  updatedAt: string
}

export interface RepairOrder {
  id: string
  customer: Customer
  item: string
  description: string
  orderType: "repair" | "custom" | "resize" | "polish"
  receivedDate: string
  promisedDate: string
  status: "pending" | "in-progress" | "completed" | "delivered"
  charges: number
  specialInstructions?: string
  createdAt: string
  updatedAt: string
}

export interface Purchase {
  id: string
  supplier: string
  items: string
  amount: number
  status: "pending" | "received" | "cancelled"
  date: string
  createdAt: string
  updatedAt: string
}

export interface MetalRates {
  gold: {
    "22K": string
    "18K": string
  }
  silver: {
    "925": string
  }
}

export interface User {
  id: string
  name: string
  email: string
  role: "admin" | "manager" | "staff"
  permissions?: Permission[] | null
  createdAt: string
  updatedAt: string
}

export interface Permission {
  module: string
  actions: ("create" | "read" | "update" | "delete" | "export")[]
}

export interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  sortOrder: number
  image?: string
  tags: string[]
  makingChargePercentage: number
  wastagePercentage: number
  createdAt: string
  updatedAt: string
}

// Exchange System Types
export interface ExchangeRate {
  id: string
  metalType: 'gold' | 'silver'
  purity: string
  ratePerGram: number
  effectiveDate: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ExchangeTransaction {
  id: string
  transactionNumber: string
  customerId?: string
  customer?: Customer
  transactionDate: string
  totalAmount: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  notes?: string
  status: 'pending' | 'completed' | 'cancelled'
  items: ExchangeItem[]
  createdBy?: string
  createdAt: string
  updatedAt: string
  // Billing integration fields
  purchaseBillId?: string
  purchaseBillNumber?: string
  purchaseBillGenerated?: boolean
  purchaseBillDate?: string
}

export interface ExchangeItem {
  id: string
  transactionId: string
  itemDescription: string
  metalType: 'gold' | 'silver'
  purity: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  ratePerGram: number
  amount: number
  itemCondition: 'good' | 'fair' | 'poor'
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeRateHistory {
  id: string
  metalType: 'gold' | 'silver'
  purity: string
  oldRate?: number
  newRate: number
  changeReason?: string
  effectiveDate: string
  changedBy?: string
  createdAt: string
}

// Exchange Billing Integration Types
export interface ExchangePurchaseBill {
  id: string
  billNumber: string
  exchangeTransactionId: string
  exchangeTransaction?: ExchangeTransaction
  customerId?: string
  customer?: Customer
  billDate: string
  totalAmount: number
  cgstAmount: number
  sgstAmount: number
  totalWithTax: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  paymentStatus: 'pending' | 'paid' | 'partial'
  notes?: string
  createdBy?: string
  createdAt: string
  updatedAt: string
}

export interface SalesExchangeItem {
  id: string
  saleId: string
  exchangeTransactionId: string
  exchangeItemId: string
  exchangeItem?: ExchangeItem
  deductionAmount: number
  appliedRate: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeVoucher {
  id: string
  voucherNumber: string
  exchangeTransactionId: string
  exchangeTransaction?: ExchangeTransaction
  voucherType: 'purchase' | 'exchange' | 'adjustment'
  voucherDate: string
  customerId?: string
  customer?: Customer
  totalAmount: number
  description?: string
  termsConditions?: string
  validityDate?: string
  status: 'active' | 'used' | 'expired' | 'cancelled'
  usedInSaleId?: string
  createdBy?: string
  createdAt: string
  updatedAt: string
}

export interface BillSequence {
  id: string
  sequenceType: 'exchange_purchase' | 'exchange_voucher' | 'sales' | 'purchase'
  prefix: string
  currentNumber: number
  financialYear: string
  createdAt: string
  updatedAt: string
}

export interface ExchangeAuditTrail {
  id: string
  exchangeTransactionId: string
  actionType: 'created' | 'updated' | 'billed' | 'voucher_generated' | 'used_in_sale' | 'cancelled'
  actionDescription: string
  oldValues?: any
  newValues?: any
  relatedBillId?: string
  relatedSaleId?: string
  performedBy?: string
  performedAt: string
}
