import { NextRequest, NextResponse } from 'next/server'
import { customerService } from '@/lib/database/services'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const updates = await request.json()
    const customer = await customerService.update(params.id, updates)
    
    if (customer) {
      return NextResponse.json({ customer })
    } else {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error updating customer:', error)
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await customerService.delete(params.id)
    
    if (success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    )
  }
}
