import { BaseService } from '../base-service'
import { InventoryItem } from '../../types'
import { generateJewelryItemId, getHSNCode } from '../../utils/jewelry-id-generator'

export class InventoryService extends BaseService<InventoryItem> {
  protected tableName = 'inventory'

  // Override create to use jewelry-specific ID generation
  async create(data: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<InventoryItem> {
    // Generate jewelry-specific ID
    const jewelryId = generateJewelryItemId(
      data.category,
      data.metalType,
      data.purity
    )

    // Generate HSN code
    const hsnCode = getHSNCode(data.category, data.metalType)

    const now = new Date().toISOString()

    const fullData = {
      ...data,
      id: jewelryId,
      hsnCode,
      createdAt: now,
      updatedAt: now
    }

    const transformedData = this.transformKeysToSnake(fullData)
    const keys = Object.keys(transformedData)
    const values = Object.values(transformedData)
    const placeholders = keys.map(() => '?').join(', ')

    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)

    return fullData as InventoryItem
  }

  async findByCategory(category: string): Promise<InventoryItem[]> {
    return this.findAll({ category })
  }

  async findByMetalType(metalType: string): Promise<InventoryItem[]> {
    return this.findAll({ metalType })
  }

  async findLowStock(threshold: number = 5): Promise<InventoryItem[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE stock <= ? ORDER BY stock ASC`
    const rows = await this.executeQuery(sql, [threshold])
    return rows.map(row => this.transformKeys(row) as InventoryItem)
  }

  async updateStock(id: string, quantity: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET stock = stock + ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [quantity, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  async searchItems(query: string): Promise<InventoryItem[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE name LIKE ? OR category LIKE ? OR description LIKE ? 
      ORDER BY created_at DESC
    `
    const searchTerm = `%${query}%`
    const rows = await this.executeQuery(sql, [searchTerm, searchTerm, searchTerm])
    return rows.map(row => this.transformKeys(row) as InventoryItem)
  }

  async getInventoryStats(): Promise<{
    totalItems: number
    totalValue: number
    lowStockItems: number
    categories: { category: string; count: number }[]
  }> {
    const totalItems = await this.count()
    
    // Get total value
    const valueResult = await this.executeQuery(
      `SELECT SUM(current_value * stock) as total_value FROM ${this.tableName}`
    )
    const totalValue = valueResult[0]?.total_value || 0

    // Get low stock count
    const lowStockItems = (await this.findLowStock()).length

    // Get categories
    const categoryResult = await this.executeQuery(
      `SELECT category, COUNT(*) as count FROM ${this.tableName} GROUP BY category ORDER BY count DESC`
    )
    const categories = categoryResult.map((row: any) => ({
      category: row.category,
      count: row.count
    }))

    return {
      totalItems,
      totalValue,
      lowStockItems,
      categories
    }
  }

  async getTopSellingItems(limit: number = 10): Promise<InventoryItem[]> {
    const sql = `
      SELECT i.*, COALESCE(SUM(si.gross_weight), 0) as total_sold
      FROM ${this.tableName} i
      LEFT JOIN sale_items si ON i.id = si.inventory_id
      GROUP BY i.id
      ORDER BY total_sold DESC
      LIMIT ?
    `
    const rows = await this.executeQuery(sql, [limit])
    return rows.map(row => this.transformKeys(row) as InventoryItem)
  }

  async updateCurrentValue(id: string, newValue: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET current_value = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [newValue, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  // Get HSN code for an item
  getItemHSNCode(category: string, metalType?: string): string {
    return getHSNCode(category, metalType)
  }

  async bulkUpdateValues(updates: { id: string; currentValue: number }[]): Promise<void> {
    const connection = await this.pool.getConnection()
    try {
      await connection.beginTransaction()
      
      for (const update of updates) {
        await connection.execute(
          `UPDATE ${this.tableName} SET current_value = ?, updated_at = ? WHERE id = ?`,
          [update.currentValue, new Date().toISOString(), update.id]
        )
      }
      
      await connection.commit()
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }
}
