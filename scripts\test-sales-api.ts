#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function testSalesAPI() {
  console.log('🧪 Testing Sales API and Data Flow...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Test 1: Check if sales table exists and has data
    console.log('🔍 Test 1: Checking sales table structure and data...')
    
    try {
      const [salesData] = await connection.execute(`
        SELECT COUNT(*) as total_sales FROM sales
      `)
      console.log(`   📊 Total sales in database: ${(salesData as any[])[0].total_sales}`)
      
      if ((salesData as any[])[0].total_sales > 0) {
        const [sampleSales] = await connection.execute(`
          SELECT id, invoice_number, customer_name, subtotal, final_amount, status, invoice_date, created_at
          FROM sales 
          LIMIT 3
        `)
        
        console.log('   📋 Sample sales data:')
        ;(sampleSales as any[]).forEach((sale, index) => {
          console.log(`      Sale ${index + 1}:`)
          console.log(`         ID: ${sale.id}`)
          console.log(`         Invoice: ${sale.invoice_number || 'N/A'}`)
          console.log(`         Customer: ${sale.customer_name || 'N/A'}`)
          console.log(`         Amount: ₹${(parseFloat(sale.final_amount) || parseFloat(sale.subtotal) || 0).toLocaleString()}`)
          console.log(`         Status: ${sale.status}`)
          console.log(`         Date: ${sale.invoice_date || sale.created_at}`)
          console.log('')
        })
      }
    } catch (error) {
      console.log(`   ❌ Error checking sales table: ${error}`)
    }

    // Test 2: Check customers table for JOIN compatibility
    console.log('👥 Test 2: Checking customers table for JOIN compatibility...')
    
    try {
      const [customerData] = await connection.execute(`
        SELECT id, first_name, last_name, phone, email, total_purchases
        FROM customers 
        LIMIT 3
      `)
      
      console.log('   📋 Sample customer data:')
      ;(customerData as any[]).forEach((customer, index) => {
        const fullName = `${customer.first_name || ''} ${customer.last_name || ''}`.trim()
        console.log(`      Customer ${index + 1}:`)
        console.log(`         ID: ${customer.id}`)
        console.log(`         Name: ${fullName}`)
        console.log(`         Phone: ${customer.phone}`)
        console.log(`         Total Purchases: ₹${(parseFloat(customer.total_purchases) || 0).toLocaleString()}`)
        console.log('')
      })
    } catch (error) {
      console.log(`   ❌ Error checking customers table: ${error}`)
    }

    // Test 3: Test the JOIN query that the sales service uses
    console.log('🔗 Test 3: Testing sales-customers JOIN query...')
    
    try {
      const [joinData] = await connection.execute(`
        SELECT s.*, c.id as customer_id, 
               CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name,
               c.phone as customer_phone, c.email as customer_email, 
               CONCAT(COALESCE(c.address_line1, ''), ' ', COALESCE(c.address_line2, '')) as customer_address,
               c.gst_number as customer_gst_number, c.total_purchases as customer_total_purchases, 
               c.last_visit as customer_last_visit, c.created_at as customer_created_at, 
               c.updated_at as customer_updated_at
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        ORDER BY s.created_at DESC
        LIMIT 3
      `)
      
      console.log(`   📊 JOIN query returned ${(joinData as any[]).length} results`)
      
      if ((joinData as any[]).length > 0) {
        console.log('   📋 JOIN query results:')
        ;(joinData as any[]).forEach((row, index) => {
          console.log(`      Result ${index + 1}:`)
          console.log(`         Sale ID: ${row.id}`)
          console.log(`         Customer Name: ${(row.customer_name || '').trim() || 'Walk-in Customer'}`)
          console.log(`         Final Amount: ₹${(parseFloat(row.final_amount) || parseFloat(row.subtotal) || 0).toLocaleString()}`)
          console.log(`         Status: ${row.status}`)
          console.log('')
        })
      }
    } catch (error) {
      console.log(`   ❌ Error with JOIN query: ${error}`)
    }

    // Test 4: Test the API endpoint directly
    console.log('🌐 Test 4: Testing sales API endpoint...')
    
    try {
      const response = await fetch('http://localhost:3000/api/sales')
      console.log(`   📡 API Response Status: ${response.status}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`   📊 API returned ${data.sales?.length || 0} sales`)
        
        if (data.sales && data.sales.length > 0) {
          console.log('   📋 API response sample:')
          data.sales.slice(0, 2).forEach((sale: any, index: number) => {
            console.log(`      Sale ${index + 1}:`)
            console.log(`         ID: ${sale.id}`)
            console.log(`         Customer: ${sale.customer?.name || 'N/A'}`)
            console.log(`         Total: ₹${(sale.total || 0).toLocaleString()}`)
            console.log(`         Items: ${sale.items?.length || 0}`)
            console.log('')
          })
        }
      } else {
        const errorText = await response.text()
        console.log(`   ❌ API Error: ${errorText}`)
      }
    } catch (error) {
      console.log(`   ❌ Error calling API: ${error}`)
    }

    // Test 5: Check sale_items table
    console.log('📦 Test 5: Checking sale_items table...')
    
    try {
      const [saleItemsData] = await connection.execute(`
        SELECT COUNT(*) as total_items FROM sale_items
      `)
      console.log(`   📊 Total sale items in database: ${(saleItemsData as any[])[0].total_items}`)
      
      if ((saleItemsData as any[])[0].total_items > 0) {
        const [sampleItems] = await connection.execute(`
          SELECT si.*, i.name as item_name
          FROM sale_items si
          LEFT JOIN inventory i ON si.inventory_id = i.id
          LIMIT 3
        `)
        
        console.log('   📋 Sample sale items:')
        ;(sampleItems as any[]).forEach((item, index) => {
          console.log(`      Item ${index + 1}:`)
          console.log(`         Sale ID: ${item.sale_id}`)
          console.log(`         Item Name: ${item.item_name || 'N/A'}`)
          console.log(`         Amount: ₹${(parseFloat(item.amount) || 0).toLocaleString()}`)
          console.log('')
        })
      }
    } catch (error) {
      console.log(`   ❌ Error checking sale_items table: ${error}`)
    }

    console.log('\n📋 Test Summary:')
    console.log('=' .repeat(60))
    console.log('✅ Database connection: Working')
    console.log('✅ Sales table: Checked')
    console.log('✅ Customers table: Checked')
    console.log('✅ JOIN query: Tested')
    console.log('✅ API endpoint: Tested')
    console.log('✅ Sale items: Checked')
    console.log('=' .repeat(60))

    console.log('\n🎉 Sales API Testing Completed!')

  } catch (error) {
    console.error('\n❌ Sales API testing failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the sales API test
testSalesAPI().catch(console.error)
