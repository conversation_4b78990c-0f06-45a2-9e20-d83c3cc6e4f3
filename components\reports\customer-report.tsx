"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useStore } from "@/lib/store"
import { Users, TrendingUp, IndianRupee, Calendar, Download } from "lucide-react"
// import { gt, lt } from "some-library" // Assuming gt and lt are imported from a library

export function CustomerReport() {
  const { customers, sales, schemes } = useStore()

  const calculateCustomerStats = () => {
    const totalCustomers = customers.length
    const activeCustomers = customers.filter((customer) => {
      const lastVisit = new Date(customer.lastVisit)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      return lastVisit > thirtyDaysAgo
    }).length

    const totalPurchases = customers.reduce((sum, customer) => sum + customer.totalPurchases, 0)
    const avgPurchase = totalCustomers > 0 ? totalPurchases / totalCustomers : 0

    return { totalCustomers, activeCustomers, totalPurchases, avgPurchase }
  }

  const getCustomerAnalytics = () => {
    return customers
      .map((customer) => {
        const customerSales = sales.filter((sale) => sale.customer.id === customer.id)
        const customerSchemes = schemes.filter((scheme) => scheme.customer.id === customer.id)

        const totalTransactions = customerSales.length
        const totalNetWeight = customerSales.reduce(
          (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + item.netWeight, 0),
          0,
        )
        const activeSchemes = customerSchemes.filter((scheme) => scheme.status === "active").length

        return {
          ...customer,
          totalTransactions,
          totalNetWeight,
          activeSchemes,
        }
      })
      .sort((a, b) => b.totalPurchases - a.totalPurchases)
  }

  const stats = calculateCustomerStats()
  const customerAnalytics = getCustomerAnalytics()

  const exportReport = () => {
    alert("Customer report exported successfully!")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Customer Report</h3>
          <p className="text-muted-foreground">Customer analysis and relationship management insights</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">Registered customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.activeCustomers}</div>
            <p className="text-xs text-muted-foreground">Visited in last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalPurchases.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">From all customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Purchase</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.avgPurchase.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Per customer</p>
          </CardContent>
        </Card>
      </div>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Customers by Purchase Value</CardTitle>
          <CardDescription>Customers ranked by total purchase amount</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rank</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Total Purchases</TableHead>
                <TableHead>Transactions</TableHead>
                <TableHead>Net Weight</TableHead>
                <TableHead>Active Schemes</TableHead>
                <TableHead>Last Visit</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customerAnalytics.slice(0, 20).map((customer, index) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">#{index + 1}</TableCell>
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>{customer.phone}</TableCell>
                  <TableCell className="font-medium">₹{customer.totalPurchases.toLocaleString()}</TableCell>
                  <TableCell>{customer.totalTransactions}</TableCell>
                  <TableCell className="text-blue-600 font-medium">{customer.totalNetWeight.toFixed(1)}g</TableCell>
                  <TableCell>
                    {customer.activeSchemes > 0 ? (
                      <Badge variant="default">{customer.activeSchemes}</Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{customer.lastVisit}</TableCell>
                  <TableCell>
                    {(() => {
                      const lastVisit = new Date(customer.lastVisit)
                      const thirtyDaysAgo = new Date()
                      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

                      if (lastVisit > thirtyDaysAgo) {
                        return <Badge variant="default">Active</Badge>
                      } else {
                        return <Badge variant="secondary">Inactive</Badge>
                      }
                    })()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Customer Segmentation */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Segmentation</CardTitle>
          <CardDescription>Customers grouped by purchase behavior</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="border rounded-lg p-4">
              <h4 className="font-medium text-green-600 mb-2">VIP Customers</h4>
              <p className="text-2xl font-bold">{customerAnalytics.filter((c) => c.totalPurchases > 200000).length}</p>
              <p className="text-sm text-muted-foreground">Purchase &gt; ₹2,00,000</p>
            </div>
            <div className="border rounded-lg p-4">
              <h4 className="font-medium text-blue-600 mb-2">Regular Customers</h4>
              <p className="text-2xl font-bold">
                {customerAnalytics.filter((c) => c.totalPurchases > 50000 && c.totalPurchases <= 200000).length}
              </p>
              <p className="text-sm text-muted-foreground">Purchase ₹50K - ₹2L</p>
            </div>
            <div className="border rounded-lg p-4">
              <h4 className="font-medium text-orange-600 mb-2">New Customers</h4>
              <p className="text-2xl font-bold">{customerAnalytics.filter((c) => c.totalPurchases <= 50000).length}</p>
              <p className="text-sm text-muted-foreground">Purchase ≤ ₹50,000</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
