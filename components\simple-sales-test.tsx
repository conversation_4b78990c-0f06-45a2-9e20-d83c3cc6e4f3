"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw } from 'lucide-react'

export function SimpleSalesTest() {
  const [salesData, setSalesData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastFetch, setLastFetch] = useState<string | null>(null)

  const fetchSalesDirectly = async () => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('🔍 Fetching sales directly from API...')
      const response = await fetch('/api/sales')
      console.log('📡 Response status:', response.status)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      console.log('📊 API Response:', data)
      
      setSalesData(data.sales || [])
      setLastFetch(new Date().toLocaleTimeString())
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Error fetching sales:', err)
    } finally {
      setLoading(false)
    }
  }

  // Auto-fetch on component mount
  useEffect(() => {
    fetchSalesDirectly()
  }, [])

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Direct Sales API Test</span>
          <Button 
            onClick={fetchSalesDirectly} 
            disabled={loading}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Status:</strong> {loading ? 'Loading...' : error ? 'Error' : 'Success'}
            </div>
            <div>
              <strong>Count:</strong> {salesData.length}
            </div>
            <div>
              <strong>Last Fetch:</strong> {lastFetch || 'Never'}
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-3 text-red-700">
              <strong>Error:</strong> {error}
            </div>
          )}

          {salesData.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Sales Data:</h4>
              <div className="bg-gray-50 rounded p-3 max-h-40 overflow-y-auto">
                {salesData.map((sale, index) => (
                  <div key={sale.id || index} className="text-sm border-b border-gray-200 pb-2 mb-2 last:border-b-0 last:mb-0">
                    <div><strong>ID:</strong> {sale.id?.substring(0, 8)}...</div>
                    <div><strong>Customer:</strong> {sale.customer?.name || 'N/A'}</div>
                    <div><strong>Total:</strong> ₹{(sale.total || 0).toLocaleString()}</div>
                    <div><strong>Items:</strong> {sale.items?.length || 0}</div>
                    <div><strong>Status:</strong> {sale.status}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {salesData.length === 0 && !loading && !error && (
            <div className="text-center py-4 text-gray-500">
              No sales data received from API
            </div>
          )}

          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <strong>Debug Info:</strong><br/>
            API Endpoint: /api/sales<br/>
            Component State: {JSON.stringify({ 
              salesCount: salesData.length, 
              loading, 
              hasError: !!error 
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
