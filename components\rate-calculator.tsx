"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TrendingUp, Calculator } from "lucide-react"
import { useStore } from "@/lib/store"

export function RateCalculator() {
  const { getMetalRate } = useStore()
  const [formData, setFormData] = useState({
    metalType: "",
    purity: "",
    weight: "",
    makingCharges: "",
  })

  const [result, setResult] = useState<{
    metalValue: number
    makingCharges: number
    totalValue: number
    rate: number
  } | null>(null)

  const calculateValue = () => {
    const weight = Number.parseFloat(formData.weight)
    const making = Number.parseFloat(formData.makingCharges) || 0

    if (!weight || !formData.metalType || !formData.purity) {
      alert("Please fill all required fields")
      return
    }

    const rate = getMetalRate(formData.metalType, formData.purity)
    const metalValue = weight * rate
    const totalValue = metalValue + making

    setResult({
      metalValue,
      makingCharges: making,
      totalValue,
      rate,
    })
  }

  const resetCalculator = () => {
    setFormData({
      metalType: "",
      purity: "",
      weight: "",
      makingCharges: "",
    })
    setResult(null)
  }

  const getCurrentRate = () => {
    if (!formData.metalType || !formData.purity) return 0
    return getMetalRate(formData.metalType, formData.purity)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          Rate Calculator
        </CardTitle>
        <CardDescription>Calculate jewelry value based on current metal rates</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="metalType">Metal Type</Label>
            <Select
              value={formData.metalType || undefined}
              onValueChange={(value) => setFormData({ ...formData, metalType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select metal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gold">Gold</SelectItem>
                <SelectItem value="silver">Silver</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="purity">Purity</Label>
            <Select value={formData.purity || undefined} onValueChange={(value) => setFormData({ ...formData, purity: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select purity" />
              </SelectTrigger>
              <SelectContent>
                {formData.metalType === "gold" ? (
                  <>
                    <SelectItem value="22K">22K Gold (91.6%)</SelectItem>
                    <SelectItem value="18K">18K Gold (75%)</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="925">925 Silver (Sterling)</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {formData.metalType && formData.purity && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-green-800">Current Rate:</span>
              <span className="text-lg font-bold text-green-600">₹{getCurrentRate()}/g</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="weight">Weight (grams)</Label>
            <Input
              id="weight"
              type="number"
              step="0.1"
              value={formData.weight}
              onChange={(e) => setFormData({ ...formData, weight: e.target.value })}
              placeholder="10.5"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="makingCharges">Making Charges (₹)</Label>
            <Input
              id="makingCharges"
              type="number"
              value={formData.makingCharges}
              onChange={(e) => setFormData({ ...formData, makingCharges: e.target.value })}
              placeholder="5000"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={calculateValue} className="flex-1">
            <Calculator className="h-4 w-4 mr-2" />
            Calculate
          </Button>
          <Button variant="outline" onClick={resetCalculator}>
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">Value Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>
                  Metal Value ({formData.weight}g × ₹{result.rate}):
                </span>
                <span className="font-medium">₹{result.metalValue.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Making Charges:</span>
                <span className="font-medium">₹{result.makingCharges.toLocaleString()}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="font-semibold">Total Value:</span>
                <span className="font-bold text-green-600">₹{result.totalValue.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
