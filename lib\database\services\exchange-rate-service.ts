import { BaseService } from '../base-service'
import { ExchangeRate, ExchangeRateHistory } from '../../types'
import { RowDataPacket } from 'mysql2'

export class ExchangeRateService extends BaseService<ExchangeRate> {
  protected tableName = 'exchange_rates'

  // Get current active rates
  async getCurrentRates(): Promise<ExchangeRate[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE is_active = true 
      ORDER BY metal_type, purity
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql)
    return rows.map(row => this.transformKeys(row) as ExchangeRate)
  }

  // Get rates by metal type
  async getRatesByMetal(metalType: 'gold' | 'silver'): Promise<ExchangeRate[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE metal_type = ? AND is_active = true 
      ORDER BY purity
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [metalType])
    return rows.map(row => this.transformKeys(row) as ExchangeRate)
  }

  // Get specific rate by metal and purity
  async getRate(metalType: 'gold' | 'silver', purity: string): Promise<ExchangeRate | null> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE metal_type = ? AND purity = ? AND is_active = true 
      LIMIT 1
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [metalType, purity])
    
    if (rows.length === 0) return null
    return this.transformKeys(rows[0]) as ExchangeRate
  }

  // Update rate with history tracking
  async updateRate(
    metalType: 'gold' | 'silver', 
    purity: string, 
    newRate: number, 
    changeReason?: string,
    changedBy?: string
  ): Promise<boolean> {
    const connection = await this.pool.getConnection()
    
    try {
      await connection.beginTransaction()
      
      // Get current rate
      const currentRate = await this.getRate(metalType, purity)
      
      if (currentRate) {
        // Update existing rate
        const updateSql = `
          UPDATE ${this.tableName} 
          SET rate_per_gram = ?, effective_date = CURDATE(), updated_at = ? 
          WHERE metal_type = ? AND purity = ? AND is_active = true
        `
        await connection.execute(updateSql, [
          newRate, 
          new Date().toISOString(), 
          metalType, 
          purity
        ])

        // Add to history
        const historyData = {
          id: this.generateId(),
          metalType,
          purity,
          oldRate: currentRate.ratePerGram,
          newRate,
          changeReason,
          effectiveDate: new Date().toISOString().split('T')[0],
          changedBy,
          createdAt: new Date().toISOString()
        }

        const transformedHistoryData = this.transformKeysToSnake(historyData)
        const historyKeys = Object.keys(transformedHistoryData)
        const historyValues = Object.values(transformedHistoryData)
        const historyPlaceholders = historyKeys.map(() => '?').join(', ')

        const historySql = `INSERT INTO exchange_rate_history (${historyKeys.join(', ')}) VALUES (${historyPlaceholders})`
        await connection.execute(historySql, historyValues)
      } else {
        // Create new rate
        const rateData = {
          id: this.generateId(),
          metalType,
          purity,
          ratePerGram: newRate,
          effectiveDate: new Date().toISOString().split('T')[0],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        const transformedRateData = this.transformKeysToSnake(rateData)
        const rateKeys = Object.keys(transformedRateData)
        const rateValues = Object.values(transformedRateData)
        const ratePlaceholders = rateKeys.map(() => '?').join(', ')

        const rateSql = `INSERT INTO ${this.tableName} (${rateKeys.join(', ')}) VALUES (${ratePlaceholders})`
        await connection.execute(rateSql, rateValues)

        // Add to history
        const historyData = {
          id: this.generateId(),
          metalType,
          purity,
          oldRate: null,
          newRate,
          changeReason: changeReason || 'Initial rate setup',
          effectiveDate: new Date().toISOString().split('T')[0],
          changedBy,
          createdAt: new Date().toISOString()
        }

        const transformedHistoryData = this.transformKeysToSnake(historyData)
        const historyKeys = Object.keys(transformedHistoryData)
        const historyValues = Object.values(transformedHistoryData)
        const historyPlaceholders = historyKeys.map(() => '?').join(', ')

        const historySql = `INSERT INTO exchange_rate_history (${historyKeys.join(', ')}) VALUES (${historyPlaceholders})`
        await connection.execute(historySql, historyValues)
      }
      
      await connection.commit()
      return true
      
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Get rate history
  async getRateHistory(metalType?: 'gold' | 'silver', purity?: string, limit: number = 50): Promise<ExchangeRateHistory[]> {
    let sql = `SELECT * FROM exchange_rate_history`
    const params: any[] = []
    const conditions: string[] = []

    if (metalType) {
      conditions.push('metal_type = ?')
      params.push(metalType)
    }

    if (purity) {
      conditions.push('purity = ?')
      params.push(purity)
    }

    if (conditions.length > 0) {
      sql += ` WHERE ${conditions.join(' AND ')}`
    }

    sql += ` ORDER BY effective_date DESC, created_at DESC LIMIT ?`
    params.push(limit)

    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    return rows.map(row => this.transformKeys(row) as ExchangeRateHistory)
  }

  // Bulk update rates
  async bulkUpdateRates(rates: { metalType: 'gold' | 'silver', purity: string, rate: number }[], changedBy?: string): Promise<void> {
    const connection = await this.pool.getConnection()
    
    try {
      await connection.beginTransaction()
      
      for (const rateUpdate of rates) {
        await this.updateRate(
          rateUpdate.metalType, 
          rateUpdate.purity, 
          rateUpdate.rate, 
          'Bulk rate update',
          changedBy
        )
      }
      
      await connection.commit()
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Calculate net weight based on purity
  calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
    return grossWeight - stoneWeight
  }

  // Calculate amount based on net weight and rate
  calculateAmount(netWeight: number, ratePerGram: number): number {
    return Math.round(netWeight * ratePerGram * 100) / 100
  }

  // Get common purities for a metal type
  getCommonPurities(metalType: 'gold' | 'silver'): string[] {
    if (metalType === 'gold') {
      return ['24K', '22K', '18K', '14K', '916', '750']
    } else {
      return ['999', '925', '900']
    }
  }
}
