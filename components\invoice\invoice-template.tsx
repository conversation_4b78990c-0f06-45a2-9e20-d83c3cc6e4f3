"use client"

import { forwardRef } from "react"
import { Sale, Customer, InventoryItem } from "@/lib/types"
import { useStore } from "@/lib/store"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface InvoiceTemplateProps {
  sale: Sale
  customer: Customer
  items: Array<{
    item: InventoryItem
    quantity: number
    weight: number
    rate: number
    amount: number
  }>
  template?: "standard" | "detailed" | "minimal"
}

export const InvoiceTemplate = forwardRef<HTMLDivElement, InvoiceTemplateProps>(
  ({ sale, customer, items, template = "standard" }, ref) => {
    const { settings } = useStore()

    const calculateTotals = () => {
      const subtotal = items.reduce((sum, item) => sum + item.amount, 0)
      const cgstAmount = (subtotal * parseFloat(settings.cgstRate || "1.5")) / 100
      const sgstAmount = (subtotal * parseFloat(settings.sgstRate || "1.5")) / 100
      const total = subtotal + cgstAmount + sgstAmount
      
      return { subtotal, cgstAmount, sgstAmount, total }
    }

    const totals = calculateTotals()

    if (template === "minimal") {
      return (
        <div ref={ref} className="max-w-sm mx-auto p-4 bg-white text-xs">
          <div className="text-center mb-4">
            <h1 className="text-lg font-bold">{settings.businessName}</h1>
            <p className="text-xs">{settings.address}</p>
            <p className="text-xs">Ph: {settings.phone} | GST: {settings.gstNumber}</p>
          </div>

          <Separator className="my-2" />

          <div className="flex justify-between mb-2">
            <span>Invoice: {sale.invoiceNumber}</span>
            <span>{new Date(sale.date).toLocaleDateString()}</span>
          </div>

          <div className="mb-2">
            <p><strong>Customer:</strong> {customer.name}</p>
            <p>Ph: {customer.phone}</p>
          </div>

          <Separator className="my-2" />

          <div className="space-y-1">
            {items.map((saleItem, index) => (
              <div key={index} className="text-xs">
                <div className="flex justify-between">
                  <span className="truncate">{saleItem.item.name}</span>
                  <span>₹{saleItem.amount.toLocaleString()}</span>
                </div>
                <div className="text-gray-500">
                  {saleItem.item.metalType} {saleItem.item.purity} - {saleItem.weight}g
                </div>
              </div>
            ))}
          </div>

          <Separator className="my-2" />

          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>₹{totals.subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>CGST ({settings.cgstRate}%):</span>
              <span>₹{totals.cgstAmount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>SGST ({settings.sgstRate}%):</span>
              <span>₹{totals.sgstAmount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between font-bold border-t pt-1">
              <span>Total:</span>
              <span>₹{totals.total.toLocaleString()}</span>
            </div>
          </div>

          <div className="text-center mt-4 text-xs text-gray-500">
            <p>Thank you for your business!</p>
          </div>
        </div>
      )
    }

    if (template === "detailed") {
      return (
        <div ref={ref} className="max-w-4xl mx-auto p-8 bg-white">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-3xl font-bold text-yellow-600">{settings.businessName}</h1>
              <div className="mt-2 text-gray-600">
                <p>{settings.address}</p>
                <p>Phone: {settings.phone}</p>
                <p>Email: {settings.email}</p>
                <p>GST No: {settings.gstNumber}</p>
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-2xl font-bold text-gray-800">INVOICE</h2>
              <div className="mt-2">
                <p><strong>Invoice No:</strong> {sale.invoiceNumber}</p>
                <p><strong>Date:</strong> {new Date(sale.date).toLocaleDateString()}</p>
                <p><strong>Time:</strong> {new Date(sale.createdAt).toLocaleTimeString()}</p>
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-2">Bill To:</h3>
            <div className="bg-gray-50 p-4 rounded">
              <p><strong>{customer.name}</strong></p>
              <p>Phone: {customer.phone}</p>
              <p>Email: {customer.email}</p>
              <p>Address: {customer.address}</p>
            </div>
          </div>

          {/* Items Table */}
          <div className="mb-8">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Item</th>
                  <th className="border border-gray-300 p-2 text-center">Metal/Purity</th>
                  <th className="border border-gray-300 p-2 text-center">Weight (g)</th>
                  <th className="border border-gray-300 p-2 text-center">Rate/g</th>
                  <th className="border border-gray-300 p-2 text-center">Making</th>
                  <th className="border border-gray-300 p-2 text-right">Amount</th>
                </tr>
              </thead>
              <tbody>
                {items.map((saleItem, index) => (
                  <tr key={index}>
                    <td className="border border-gray-300 p-2">
                      <div>
                        <p className="font-medium">{saleItem.item.name}</p>
                        <p className="text-sm text-gray-500">{saleItem.item.category}</p>
                      </div>
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      <Badge variant="outline">
                        {saleItem.item.metalType} {saleItem.item.purity}
                      </Badge>
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {saleItem.weight}g
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      ₹{saleItem.rate.toLocaleString()}
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      ₹{saleItem.item.makingCharges.toLocaleString()}
                    </td>
                    <td className="border border-gray-300 p-2 text-right font-medium">
                      ₹{saleItem.amount.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-8">
            <div className="w-80">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>₹{totals.subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>CGST ({settings.cgstRate}%):</span>
                  <span>₹{totals.cgstAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>SGST ({settings.sgstRate}%):</span>
                  <span>₹{totals.sgstAmount.toLocaleString()}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount:</span>
                  <span>₹{totals.total.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold mb-2">Terms & Conditions:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• All sales are final</li>
                  <li>• Goods once sold will not be taken back</li>
                  <li>• All disputes subject to local jurisdiction</li>
                  <li>• Payment due within 30 days</li>
                </ul>
              </div>
              <div className="text-right">
                <div className="mb-8">
                  <p className="text-sm text-gray-600">Authorized Signature</p>
                  <div className="border-b border-gray-300 w-48 ml-auto mt-8"></div>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-8 text-gray-500">
            <p>Thank you for choosing {settings.businessName}!</p>
          </div>
        </div>
      )
    }

    // Standard template
    return (
      <div ref={ref} className="max-w-2xl mx-auto p-6 bg-white">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-yellow-600">{settings.businessName}</h1>
          <p className="text-gray-600">{settings.address}</p>
          <p className="text-gray-600">Phone: {settings.phone} | GST: {settings.gstNumber}</p>
        </div>

        <Separator className="my-4" />

        {/* Invoice Info */}
        <div className="flex justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">INVOICE</h2>
            <p>Invoice No: {sale.invoiceNumber}</p>
            <p>Date: {new Date(sale.date).toLocaleDateString()}</p>
          </div>
          <div className="text-right">
            <h3 className="font-semibold">Bill To:</h3>
            <p>{customer.name}</p>
            <p>{customer.phone}</p>
          </div>
        </div>

        {/* Items */}
        <div className="mb-6">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Item</th>
                <th className="text-center p-2">Weight</th>
                <th className="text-center p-2">Rate</th>
                <th className="text-right p-2">Amount</th>
              </tr>
            </thead>
            <tbody>
              {items.map((saleItem, index) => (
                <tr key={index} className="border-b">
                  <td className="p-2">
                    <div>
                      <p className="font-medium">{saleItem.item.name}</p>
                      <p className="text-sm text-gray-500">
                        {saleItem.item.metalType} {saleItem.item.purity}
                      </p>
                    </div>
                  </td>
                  <td className="text-center p-2">{saleItem.weight}g</td>
                  <td className="text-center p-2">₹{saleItem.rate.toLocaleString()}</td>
                  <td className="text-right p-2">₹{saleItem.amount.toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="flex justify-end">
          <div className="w-64">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>₹{totals.subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>CGST ({settings.cgstRate}%):</span>
                <span>₹{totals.cgstAmount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>SGST ({settings.sgstRate}%):</span>
                <span>₹{totals.sgstAmount.toLocaleString()}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>₹{totals.total.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-8 text-gray-500">
          <p>Thank you for your business!</p>
        </div>
      </div>
    )
  }
)

InvoiceTemplate.displayName = "InvoiceTemplate"
