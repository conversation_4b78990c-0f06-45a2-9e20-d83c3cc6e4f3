"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { CategoryForm } from "@/components/forms/category-form"
import { DataTable } from "@/components/ui/data-table"
import { useStore } from "@/lib/store"
import { Category } from "@/lib/types"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Tag, 
  TrendingUp, 
  Package,
  Filter,
  Grid,
  List
} from "lucide-react"
import { ConfirmDialog } from "@/components/ui/confirm-dialog"

export function CategoriesPage() {
  const { 
    categories, 
    loadCategories, 
    deleteCategory, 
    hasPermission 
  } = useStore()

  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | undefined>()
  const [viewingCategory, setViewingCategory] = useState<Category | undefined>()
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: "",
    description: "",
    onConfirm: () => {},
  })
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid")
  const [filterActive, setFilterActive] = useState<"all" | "active" | "inactive">("all")

  useEffect(() => {
    loadCategories()
  }, [loadCategories])

  const filteredCategories = categories.filter((category) => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesFilter = filterActive === "all" || 
                         (filterActive === "active" && category.isActive) ||
                         (filterActive === "inactive" && !category.isActive)

    return matchesSearch && matchesFilter
  })

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
  }

  const handleView = (category: Category) => {
    setViewingCategory(category)
  }

  const handleDelete = (category: Category) => {
    setConfirmDialog({
      open: true,
      title: "Delete Category",
      description: `Are you sure you want to delete "${category.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        try {
          await deleteCategory(category.id)
          setConfirmDialog({ ...confirmDialog, open: false })
        } catch (error) {
          console.error("Error deleting category:", error)
          alert("Error deleting category. Please try again.")
        }
      },
    })
  }

  const categoryColumns = [
    { key: "name", label: "Name" },
    { 
      key: "description", 
      label: "Description",
      render: (category: Category) => (
        <div className="max-w-xs truncate">
          {category.description || "No description"}
        </div>
      )
    },
    {
      key: "tags",
      label: "Tags",
      render: (category: Category) => (
        <div className="flex flex-wrap gap-1">
          {category.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {category.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{category.tags.length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "makingChargePercentage",
      label: "Making Charge",
      render: (category: Category) => `${category.makingChargePercentage}%`,
    },
    {
      key: "wastagePercentage",
      label: "Wastage",
      render: (category: Category) => `${category.wastagePercentage}%`,
    },
    {
      key: "isActive",
      label: "Status",
      render: (category: Category) => (
        <Badge variant={category.isActive ? "default" : "secondary"}>
          {category.isActive ? "Active" : "Inactive"}
        </Badge>
      ),
    },
    { key: "sortOrder", label: "Sort Order" },
  ]

  const CategoryCard = ({ category }: { category: Category }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{category.name}</CardTitle>
            <CardDescription className="mt-1">
              {category.description || "No description"}
            </CardDescription>
          </div>
          <Badge variant={category.isActive ? "default" : "secondary"}>
            {category.isActive ? "Active" : "Inactive"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Tags */}
          {category.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {category.tags.slice(0, 4).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
              {category.tags.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{category.tags.length - 4} more
                </Badge>
              )}
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span>Making: {category.makingChargePercentage}%</span>
            </div>
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span>Wastage: {category.wastagePercentage}%</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleView(category)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            {hasPermission("categories", "update") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleEdit(category)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {hasPermission("categories", "delete") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDelete(category)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Categories</h1>
          <p className="text-muted-foreground">
            Manage product categories and their settings
          </p>
        </div>
        {hasPermission("categories", "create") && (
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {categories.filter(cat => cat.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Making Charge</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {categories.length > 0 
                ? (categories.reduce((sum, cat) => sum + cat.makingChargePercentage, 0) / categories.length).toFixed(1)
                : 0}%
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Wastage</CardTitle>
            <Package className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {categories.length > 0 
                ? (categories.reduce((sum, cat) => sum + cat.wastagePercentage, 0) / categories.length).toFixed(1)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex gap-2 items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select
            value={filterActive}
            onChange={(e) => setFilterActive(e.target.value as "all" | "active" | "inactive")}
            className="px-3 py-2 border rounded-md"
          >
            <option value="all">All Categories</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>

        <div className="flex gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "table" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("table")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCategories.map((category) => (
            <CategoryCard key={category.id} category={category} />
          ))}
        </div>
      ) : (
        <DataTable
          data={filteredCategories}
          columns={categoryColumns}
          onView={hasPermission("categories", "read") ? handleView : undefined}
          onEdit={hasPermission("categories", "update") ? handleEdit : undefined}
          onDelete={hasPermission("categories", "delete") ? handleDelete : undefined}
          searchPlaceholder="Search categories..."
          searchKey="name"
          title="Categories"
        />
      )}

      {filteredCategories.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm ? "Try adjusting your search terms" : "Get started by creating your first category"}
          </p>
          {hasPermission("categories", "create") && !searchTerm && (
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          )}
        </div>
      )}

      {/* Add Category Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Category</DialogTitle>
          </DialogHeader>
          <CategoryForm
            onSubmit={() => setIsAddDialogOpen(false)}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={!!editingCategory} onOpenChange={(open) => !open && setEditingCategory(undefined)}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          {editingCategory && (
            <CategoryForm
              category={editingCategory}
              onSubmit={() => setEditingCategory(undefined)}
              onCancel={() => setEditingCategory(undefined)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Category Dialog */}
      <Dialog open={!!viewingCategory} onOpenChange={(open) => !open && setViewingCategory(undefined)}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Category Details</DialogTitle>
          </DialogHeader>
          {viewingCategory && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{viewingCategory.name}</h3>
                <p className="text-muted-foreground">{viewingCategory.description || "No description"}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Making Charge</label>
                  <p className="text-lg font-semibold text-green-600">{viewingCategory.makingChargePercentage}%</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Wastage</label>
                  <p className="text-lg font-semibold text-orange-600">{viewingCategory.wastagePercentage}%</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Status</label>
                <div className="mt-1">
                  <Badge variant={viewingCategory.isActive ? "default" : "secondary"}>
                    {viewingCategory.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>

              {viewingCategory.tags.length > 0 && (
                <div>
                  <label className="text-sm font-medium">Tags</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {viewingCategory.tags.map((tag) => (
                      <Badge key={tag} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div>
                  <label className="font-medium">Created</label>
                  <p>{new Date(viewingCategory.createdAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="font-medium">Updated</label>
                  <p>{new Date(viewingCategory.updatedAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Confirm Dialog */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
      />
    </div>
  )
}
