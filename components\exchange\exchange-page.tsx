"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Filter, Download, Eye, Edit, Trash2, FileText } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ExchangeTransaction } from "@/lib/types"
import { ExchangeForm } from "./exchange-form"
import { ExchangeRateManager } from "./exchange-rate-manager"
import { ExchangeReports } from "./exchange-reports"
import { ExchangePurchaseBill } from "./exchange-purchase-bill"
import { ExchangeDashboard } from "./exchange-dashboard"
import { ExchangeDemo } from "./exchange-demo"
import { formatCurrency, formatDate } from "@/lib/utils"

interface ExchangePageProps {
  initialTransactions?: ExchangeTransaction[]
}

export function ExchangePage({ initialTransactions = [] }: ExchangePageProps) {
  // Mock data for development - replace with actual API calls
  const mockTransactions: ExchangeTransaction[] = [
    {
      id: 'exg_001',
      transactionNumber: 'EXG-20240131-001',
      customerId: 'cust_001',
      customer: {
        id: 'cust_001',
        name: 'Rajesh Kumar',
        phone: '9876543210',
        email: '<EMAIL>',
        address: '123 Main Street, Mumbai',
        totalPurchases: 150000,
        lastVisit: '2024-01-31',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-31'
      },
      transactionDate: '2024-01-31',
      totalAmount: 33000,
      paymentMethod: 'cash',
      notes: 'Old gold exchange for new purchase',
      status: 'completed',
      items: [
        {
          id: 'item_001',
          transactionId: 'exg_001',
          itemDescription: 'GOLD OLD BAR',
          metalType: 'gold',
          purity: '22K',
          grossWeight: 10.000,
          stoneWeight: 2.500,
          netWeight: 7.500,
          ratePerGram: 4400,
          amount: 33000,
          itemCondition: 'good',
          notes: 'Good condition old gold bar',
          createdAt: '2024-01-31',
          updatedAt: '2024-01-31'
        }
      ],
      createdAt: '2024-01-31',
      updatedAt: '2024-01-31'
    },
    {
      id: 'exg_002',
      transactionNumber: 'EXG-********-001',
      transactionDate: '2024-01-30',
      totalAmount: 15600,
      paymentMethod: 'bank_transfer',
      notes: 'Silver jewelry exchange',
      status: 'pending',
      items: [
        {
          id: 'item_002',
          transactionId: 'exg_002',
          itemDescription: 'OLD SILVER BANGLES',
          metalType: 'silver',
          purity: '925',
          grossWeight: 200.000,
          stoneWeight: 0,
          netWeight: 200.000,
          ratePerGram: 78,
          amount: 15600,
          itemCondition: 'fair',
          notes: 'Set of 4 silver bangles',
          createdAt: '2024-01-30',
          updatedAt: '2024-01-30'
        }
      ],
      createdAt: '2024-01-30',
      updatedAt: '2024-01-30'
    }
  ]

  const [transactions, setTransactions] = useState<ExchangeTransaction[]>(
    initialTransactions.length > 0 ? initialTransactions : mockTransactions
  )
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<ExchangeTransaction | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(false)
  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false)
  const [billTransaction, setBillTransaction] = useState<ExchangeTransaction | null>(null)

  // Filter transactions based on search and status
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.transactionNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.customer?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.items.some(item => 
        item.itemDescription.toLowerCase().includes(searchTerm.toLowerCase())
      )
    
    const matchesStatus = statusFilter === "all" || transaction.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  // Calculate summary statistics
  const stats = {
    totalTransactions: filteredTransactions.length,
    totalAmount: filteredTransactions.reduce((sum, t) => sum + t.totalAmount, 0),
    pendingTransactions: filteredTransactions.filter(t => t.status === 'pending').length,
    completedTransactions: filteredTransactions.filter(t => t.status === 'completed').length,
  }

  const handleNewExchange = () => {
    setSelectedTransaction(null)
    setIsFormOpen(true)
  }

  const handleEditExchange = (transaction: ExchangeTransaction) => {
    setSelectedTransaction(transaction)
    setIsFormOpen(true)
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setSelectedTransaction(null)
  }

  const handleFormSuccess = (transaction: ExchangeTransaction) => {
    if (selectedTransaction) {
      // Update existing transaction
      setTransactions(prev =>
        prev.map(t => t.id === transaction.id ? transaction : t)
      )
    } else {
      // Add new transaction
      setTransactions(prev => [transaction, ...prev])
    }
    handleFormClose()
  }

  const handleGenerateBill = (transaction: ExchangeTransaction) => {
    setBillTransaction(transaction)
    setIsBillDialogOpen(true)
  }

  const handleBillClose = () => {
    setIsBillDialogOpen(false)
    setBillTransaction(null)
  }

  const handleBillGenerated = (bill: any) => {
    // Update transaction with bill information
    setTransactions(prev =>
      prev.map(t =>
        t.id === bill.exchangeTransactionId
          ? { ...t, purchaseBillGenerated: true, purchaseBillNumber: bill.billNumber }
          : t
      )
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Completed</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Old Gold/Silver Exchange</h1>
          <p className="text-muted-foreground">
            Manage old gold and silver exchange transactions
          </p>
        </div>
        <Button onClick={handleNewExchange} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Exchange
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingTransactions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedTransactions}</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="demo" className="space-y-4">
        <TabsList>
          <TabsTrigger value="demo">Demo</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="rates">Exchange Rates</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="demo">
          <ExchangeDemo />
        </TabsContent>

        <TabsContent value="dashboard">
          <ExchangeDashboard />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                <Select
                  value={statusFilter}
                  onValueChange={(value) => setStatusFilter(value)}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Transactions List */}
          <Card>
            <CardHeader>
              <CardTitle>Exchange Transactions</CardTitle>
              <CardDescription>
                {filteredTransactions.length} transactions found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-4">
                        <div>
                          <p className="font-medium">{transaction.transactionNumber}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(transaction.transactionDate)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {transaction.customer?.name || 'Walk-in Customer'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.items.length} item(s)
                          </p>
                        </div>
                        <div>
                          <p className="font-medium">{formatCurrency(transaction.totalAmount)}</p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.paymentMethod}
                          </p>
                        </div>
                        {getStatusBadge(transaction.status)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditExchange(transaction)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      {transaction.status === 'completed' && !transaction.purchaseBillGenerated && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGenerateBill(transaction)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                      )}
                      {transaction.purchaseBillGenerated && (
                        <Badge variant="secondary" className="text-xs">
                          Bill: {transaction.purchaseBillNumber}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
                
                {filteredTransactions.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No exchange transactions found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates">
          <ExchangeRateManager />
        </TabsContent>

        <TabsContent value="reports">
          <ExchangeReports transactions={transactions} />
        </TabsContent>
      </Tabs>

      {/* Exchange Form Dialog */}
      {isFormOpen && (
        <ExchangeForm
          transaction={selectedTransaction}
          onClose={handleFormClose}
          onSuccess={handleFormSuccess}
        />
      )}

      {/* Exchange Purchase Bill Dialog */}
      {isBillDialogOpen && billTransaction && (
        <ExchangePurchaseBill
          exchangeTransaction={billTransaction}
          onClose={handleBillClose}
          onBillGenerated={handleBillGenerated}
        />
      )}
    </div>
  )
}
