"use client"

import { useEffect, useState } from 'react'
import { useStore } from '../store'

export function useDatabase() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { loadAllData, isLoading } = useStore()

  useEffect(() => {
    let mounted = true

    async function initializeDatabase() {
      try {
        // For now, assume database is connected and load data
        // In a real implementation, this would test the connection via API
        setIsConnected(true)

        // Load all data from database
        await loadAllData()
        if (mounted) {
          setIsInitialized(true)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Database initialization failed')
          console.error('Database initialization error:', err)
        }
      }
    }

    initializeDatabase()

    return () => {
      mounted = false
    }
  }, [loadAllData])

  return {
    isInitialized,
    isConnected,
    isLoading,
    error,
    retry: () => {
      setError(null)
      setIsInitialized(false)
      setIsConnected(false)
      // Re-trigger the effect by updating a dependency
      loadAllData()
    }
  }
}
