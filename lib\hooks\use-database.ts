"use client"

import { useEffect, useState } from 'react'
import { useStore } from '../store'

// Global flag to prevent multiple initializations
let globalInitializationStarted = false
let globalInitializationPromise: Promise<void> | null = null

export function useDatabase() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { loadAllData, isLoading } = useStore()

  useEffect(() => {
    const initializeDatabase = async () => {
      if (globalInitializationStarted) {
        // If initialization is already in progress, wait for it
        if (globalInitializationPromise) {
          try {
            await globalInitializationPromise
            setIsConnected(true)
            setIsInitialized(true)
          } catch (err) {
            setError(err instanceof Error ? err.message : 'Database initialization failed')
          }
        }
        return
      }

      globalInitializationStarted = true

      globalInitializationPromise = (async () => {
        try {
          // For now, assume database is connected and load data
          // In a real implementation, this would test the connection via API
          setIsConnected(true)

          // Load all data from database
          await loadAllData()
          setIsInitialized(true)
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Database initialization failed')
          console.error('Database initialization error:', err)
          globalInitializationStarted = false // Reset on error to allow retry
          globalInitializationPromise = null
          throw err
        }
      })()

      await globalInitializationPromise
    }

    initializeDatabase()
  }, []) // Empty dependency array to run only once

  return {
    isInitialized,
    isConnected,
    isLoading,
    error,
    retry: () => {
      setError(null)
      setIsInitialized(false)
      setIsConnected(false)
      globalInitializationStarted = false // Reset the global flag to allow retry
      globalInitializationPromise = null

      // Re-trigger initialization
      const initializeDatabase = async () => {
        if (globalInitializationStarted) {
          return
        }

        globalInitializationStarted = true

        globalInitializationPromise = (async () => {
          try {
            setIsConnected(true)
            await loadAllData()
            setIsInitialized(true)
          } catch (err) {
            setError(err instanceof Error ? err.message : 'Database initialization failed')
            console.error('Database initialization error:', err)
            globalInitializationStarted = false // Reset on error
            globalInitializationPromise = null
            throw err
          }
        })()

        await globalInitializationPromise
      }
      initializeDatabase()
    }
  }
}
