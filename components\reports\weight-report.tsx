"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useStore } from "@/lib/store"
import { Scale, Download, Printer, FileText } from "lucide-react"

export function WeightReport() {
  const { inventory, sales, exportToPDF, exportToExcel, printData } = useStore()

  // Calculate weight statistics
  const totalGrossWeight = inventory.reduce((sum, item) => sum + item.grossWeight * item.stock, 0)
  const totalStoneWeight = inventory.reduce((sum, item) => sum + item.stoneWeight * item.stock, 0)
  const totalNetWeight = inventory.reduce((sum, item) => sum + item.netWeight * item.stock, 0)

  // Sales weight data
  const totalSoldWeight = sales.reduce(
    (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + item.netWeight, 0),
    0,
  )

  // Category-wise breakdown
  const categoryBreakdown = inventory.reduce(
    (acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = {
          grossWeight: 0,
          stoneWeight: 0,
          netWeight: 0,
          items: 0,
        }
      }
      acc[item.category].grossWeight += item.grossWeight * item.stock
      acc[item.category].stoneWeight += item.stoneWeight * item.stock
      acc[item.category].netWeight += item.netWeight * item.stock
      acc[item.category].items += item.stock
      return acc
    },
    {} as Record<string, any>,
  )

  const handleExport = (type: "pdf" | "excel" | "print") => {
    const reportData = inventory.map((item) => ({
      ...item,
      totalGrossWeight: item.grossWeight * item.stock,
      totalStoneWeight: item.stoneWeight * item.stock,
      totalNetWeight: item.netWeight * item.stock,
    }))

    switch (type) {
      case "pdf":
        exportToPDF(reportData, "Weight Report")
        break
      case "excel":
        exportToExcel(reportData, "Weight Report")
        break
      case "print":
        printData(reportData, "Weight Report")
        break
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Weight Analysis Report</h3>
          <p className="text-muted-foreground">Comprehensive weight breakdown and analysis</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleExport("pdf")}>
            <FileText className="h-4 w-4 mr-2" />
            PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport("excel")}>
            <Download className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={() => handleExport("print")}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Gross Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalGrossWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Including stones</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Stone Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{totalStoneWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Stones only</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Net Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totalNetWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Pure metal</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sold Weight</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{totalSoldWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Net weight sold</p>
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Category-wise Weight Breakdown</CardTitle>
          <CardDescription>Weight distribution across jewelry categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(categoryBreakdown).map(([category, data]) => (
              <div key={category} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{category}</h4>
                  <Badge variant="outline">{data.items} items</Badge>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Gross Weight</p>
                    <p className="font-medium">{data.grossWeight.toFixed(1)}g</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Stone Weight</p>
                    <p className="font-medium text-purple-600">{data.stoneWeight.toFixed(1)}g</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Net Weight</p>
                    <p className="font-medium text-blue-600">{data.netWeight.toFixed(1)}g</p>
                  </div>
                </div>
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(data.netWeight / totalNetWeight) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {((data.netWeight / totalNetWeight) * 100).toFixed(1)}% of total net weight
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
