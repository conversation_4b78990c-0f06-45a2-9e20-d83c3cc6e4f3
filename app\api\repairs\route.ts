import { NextRequest, NextResponse } from 'next/server'
import { repairService } from '@/lib/database/services'

export async function GET() {
  try {
    const repairs = await repairService.findAll()
    return NextResponse.json({ repairs })
  } catch (error) {
    console.error('Error fetching repairs:', error)
    return NextResponse.json(
      { error: 'Failed to fetch repairs' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const repairData = await request.json()
    const repair = await repairService.create(repairData)
    return NextResponse.json({ repair })
  } catch (error) {
    console.error('Error creating repair:', error)
    return NextResponse.json(
      { error: 'Failed to create repair' },
      { status: 500 }
    )
  }
}
