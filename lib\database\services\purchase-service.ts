import { BaseService } from '../base-service'
import { Purchase } from '../../types'

export class PurchaseService extends BaseService<Purchase> {
  protected tableName = 'purchases'

  async findBySupplier(supplier: string): Promise<Purchase[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE supplier LIKE ? ORDER BY created_at DESC`
    const rows = await this.executeQuery(sql, [`%${supplier}%`])
    return rows.map(row => this.transformKeys(row) as Purchase)
  }

  async findByStatus(status: string): Promise<Purchase[]> {
    return this.findAll({ status })
  }

  async findByDateRange(startDate: string, endDate: string): Promise<Purchase[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE date BETWEEN ? AND ? ORDER BY date DESC`
    const rows = await this.executeQuery(sql, [startDate, endDate])
    return rows.map(row => this.transformKeys(row) as Purchase)
  }

  async updateStatus(id: string, status: Purchase['status']): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET status = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [status, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  async getPendingPurchases(): Promise<Purchase[]> {
    return this.findByStatus('pending')
  }

  async getReceivedPurchases(): Promise<Purchase[]> {
    return this.findByStatus('received')
  }

  async getPurchaseStats(startDate?: string, endDate?: string): Promise<{
    totalPurchases: number
    totalAmount: number
    pendingPurchases: number
    receivedPurchases: number
    cancelledPurchases: number
    topSuppliers: { supplier: string; totalAmount: number; count: number }[]
  }> {
    let whereClause = ''
    const params: any[] = []
    
    if (startDate && endDate) {
      whereClause = 'WHERE date BETWEEN ? AND ?'
      params.push(startDate, endDate)
    }

    const totalPurchases = await this.executeQuery(
      `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`,
      params
    )

    const amountResult = await this.executeQuery(
      `SELECT SUM(amount) as total_amount FROM ${this.tableName} ${whereClause}`,
      params
    )

    const pendingPurchases = await this.count({ status: 'pending' })
    const receivedPurchases = await this.count({ status: 'received' })
    const cancelledPurchases = await this.count({ status: 'cancelled' })

    const supplierResult = await this.executeQuery(
      `SELECT supplier, SUM(amount) as total_amount, COUNT(*) as count 
       FROM ${this.tableName} ${whereClause} 
       GROUP BY supplier 
       ORDER BY total_amount DESC 
       LIMIT 10`,
      params
    )

    return {
      totalPurchases: totalPurchases[0]?.count || 0,
      totalAmount: amountResult[0]?.total_amount || 0,
      pendingPurchases,
      receivedPurchases,
      cancelledPurchases,
      topSuppliers: supplierResult.map((row: any) => ({
        supplier: row.supplier,
        totalAmount: row.total_amount,
        count: row.count
      }))
    }
  }

  async searchPurchases(query: string): Promise<Purchase[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE supplier LIKE ? OR items LIKE ? 
      ORDER BY created_at DESC
    `
    const searchTerm = `%${query}%`
    const rows = await this.executeQuery(sql, [searchTerm, searchTerm])
    return rows.map(row => this.transformKeys(row) as Purchase)
  }

  async getMonthlyPurchaseData(year: number): Promise<{ month: number; amount: number; count: number }[]> {
    const sql = `
      SELECT 
        MONTH(date) as month,
        SUM(amount) as amount,
        COUNT(*) as count
      FROM ${this.tableName}
      WHERE YEAR(date) = ? AND status = 'received'
      GROUP BY MONTH(date)
      ORDER BY month
    `
    const rows = await this.executeQuery(sql, [year])
    
    // Fill in missing months with zero values
    const result = []
    for (let month = 1; month <= 12; month++) {
      const found = rows.find((row: any) => row.month === month)
      result.push({
        month,
        amount: found?.amount || 0,
        count: found?.count || 0
      })
    }
    
    return result
  }
}
