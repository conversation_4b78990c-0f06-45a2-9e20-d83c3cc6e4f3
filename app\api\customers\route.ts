import { NextRequest, NextResponse } from 'next/server'
import { customerService } from '@/lib/database/services'

export async function GET() {
  try {
    const customers = await customerService.findAll()
    return NextResponse.json({ customers })
  } catch (error) {
    console.error('Error fetching customers:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const customerData = await request.json()
    const customer = await customerService.create(customerData)
    return NextResponse.json({ customer })
  } catch (error) {
    console.error('Error creating customer:', error)
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}
