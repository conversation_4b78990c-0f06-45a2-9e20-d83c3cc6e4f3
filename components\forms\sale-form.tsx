"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useStore } from "@/lib/store"
import type { Sale, InventoryItem, SaleItem } from "@/lib/types"
import { Plus, Trash2 } from "lucide-react"

interface SaleFormProps {
  sale?: Sale
  onSubmit: () => void
  onCancel: () => void
}

export function SaleForm({ sale, onSubmit, onCancel }: SaleFormProps) {
  const { addSale, updateSale, customers, inventory, settings, getMetalRate } = useStore()
  const [formData, setFormData] = useState({
    customerId: "",
    date: "",
    status: "paid",
  })
  const [saleItems, setSaleItems] = useState<SaleItem[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (sale) {
      setFormData({
        customerId: sale.customer.id,
        date: sale.date,
        status: sale.status,
      })
      setSaleItems(sale.items)
    } else {
      // Set default date to today
      const today = new Date().toISOString().split("T")[0]
      setFormData((prev) => ({ ...prev, date: today }))
    }
  }, [sale])

  const addSaleItem = () => {
    const newItem: SaleItem = {
      id: `item_${Date.now()}`,
      item: {} as InventoryItem,
      grossWeight: 0,
      stoneWeight: 0,
      netWeight: 0,
      rate: 0,
      makingCharges: 0,
      stoneAmount: 0,
      amount: 0,
    }
    setSaleItems([...saleItems, newItem])
  }

  const removeSaleItem = (index: number) => {
    setSaleItems(saleItems.filter((_, i) => i !== index))
  }

  const updateSaleItem = (index: number, field: string, value: any) => {
    const updatedItems = [...saleItems]
    const item = updatedItems[index]

    if (field === "itemId") {
      const inventoryItem = inventory.find((inv) => inv.id === value)
      if (inventoryItem) {
        item.item = inventoryItem
        item.grossWeight = inventoryItem.grossWeight
        item.stoneWeight = inventoryItem.stoneWeight
        item.netWeight = inventoryItem.netWeight
        item.rate = getMetalRate(inventoryItem.metalType, inventoryItem.purity)
        item.makingCharges = inventoryItem.makingCharges
        item.stoneAmount = inventoryItem.stoneAmount
      }
    } else {
      ;(item as any)[field] = value
    }

    // Recalculate net weight if gross or stone weight changes
    if (field === "grossWeight" || field === "stoneWeight") {
      item.netWeight = Math.max(0, item.grossWeight - item.stoneWeight)
    }

    // Recalculate amount
    const metalValue = item.netWeight * item.rate
    item.amount = metalValue + item.makingCharges + item.stoneAmount

    updatedItems[index] = item
    setSaleItems(updatedItems)
  }

  const calculateTotals = () => {
    const subtotal = saleItems.reduce((sum, item) => sum + item.amount, 0)
    const cgstRate = Number.parseFloat(settings.cgstRate) / 100
    const sgstRate = Number.parseFloat(settings.sgstRate) / 100
    const cgst = subtotal * cgstRate
    const sgst = subtotal * sgstRate
    const total = subtotal + cgst + sgst

    return { subtotal, cgst, sgst, total }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.customerId) {
      newErrors.customerId = "Customer is required"
    }

    if (!formData.date) {
      newErrors.date = "Sale date is required"
    }

    if (saleItems.length === 0) {
      newErrors.items = "At least one item is required"
    }

    saleItems.forEach((item, index) => {
      if (!item.item.id) {
        newErrors[`item_${index}`] = "Item selection is required"
      }
      if (item.grossWeight <= 0) {
        newErrors[`grossWeight_${index}`] = "Gross weight must be greater than 0"
      }
      if (item.stoneWeight < 0) {
        newErrors[`stoneWeight_${index}`] = "Stone weight cannot be negative"
      }
      if (item.stoneWeight >= item.grossWeight) {
        newErrors[`stoneWeight_${index}`] = "Stone weight must be less than gross weight"
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const customer = customers.find((c) => c.id === formData.customerId)
      if (!customer) {
        setErrors({ submit: "Selected customer not found" })
        return
      }

      const { subtotal, cgst, sgst, total } = calculateTotals()

      const saleData = {
        customer,
        items: saleItems,
        subtotal,
        cgst,
        sgst,
        total,
        status: formData.status as "draft" | "paid" | "pending" | "cancelled",
        date: formData.date,
      }

      if (sale) {
        updateSale(sale.id, saleData)
      } else {
        await addSale(saleData)
      }

      // Reset form
      setFormData({
        customerId: "",
        date: "",
        status: "paid",
      })
      setSaleItems([])
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving sale:", error)
      setErrors({ submit: "Failed to save sale. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const { subtotal, cgst, sgst, total } = calculateTotals()

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customerId">Customer *</Label>
          <Select value={formData.customerId || undefined} onValueChange={(value) => handleInputChange("customerId", value)}>
            <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
              <SelectValue placeholder="Select customer" />
            </SelectTrigger>
            <SelectContent>
              {customers.map((customer) => (
                <SelectItem key={customer.id} value={customer.id}>
                  {customer.name} - {customer.phone}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="date">Sale Date *</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange("date", e.target.value)}
            required
            className={errors.date ? "border-red-500" : ""}
          />
          {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Sale Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Sale Items</CardTitle>
          <Button type="button" onClick={addSaleItem} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {errors.items && <p className="text-sm text-red-500">{errors.items}</p>}

          {saleItems.map((saleItem, index) => (
            <Card key={saleItem.id} className="p-4">
              <div className="flex justify-between items-start mb-4">
                <h4 className="font-medium">Item {index + 1}</h4>
                <Button type="button" variant="ghost" size="sm" onClick={() => removeSaleItem(index)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="space-y-2">
                  <Label>Select Item *</Label>
                  <Select
                    value={saleItem.item.id || undefined}
                    onValueChange={(value) => updateSaleItem(index, "itemId", value)}
                  >
                    <SelectTrigger className={errors[`item_${index}`] ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select inventory item" />
                    </SelectTrigger>
                    <SelectContent>
                      {inventory.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          <div className="flex flex-col">
                            <span>{item.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {item.metalType} {item.purity} - {item.netWeight}g - Stock: {item.stock}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors[`item_${index}`] && <p className="text-sm text-red-500">{errors[`item_${index}`]}</p>}
                </div>

                <div className="space-y-2">
                  <Label>Rate (₹/g)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={saleItem.rate}
                    onChange={(e) => updateSaleItem(index, "rate", Number.parseFloat(e.target.value) || 0)}
                    placeholder="Rate per gram"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="space-y-2">
                  <Label>Gross Weight (g) *</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={saleItem.grossWeight}
                    onChange={(e) => updateSaleItem(index, "grossWeight", Number.parseFloat(e.target.value) || 0)}
                    placeholder="25.50"
                    className={errors[`grossWeight_${index}`] ? "border-red-500" : ""}
                  />
                  {errors[`grossWeight_${index}`] && (
                    <p className="text-sm text-red-500">{errors[`grossWeight_${index}`]}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Stone Weight (g)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={saleItem.stoneWeight}
                    onChange={(e) => updateSaleItem(index, "stoneWeight", Number.parseFloat(e.target.value) || 0)}
                    placeholder="2.50"
                    className={errors[`stoneWeight_${index}`] ? "border-red-500" : ""}
                  />
                  {errors[`stoneWeight_${index}`] && (
                    <p className="text-sm text-red-500">{errors[`stoneWeight_${index}`]}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Net Weight (g)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={saleItem.netWeight}
                    readOnly
                    className="bg-gray-50"
                    placeholder="Auto-calculated"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="space-y-2">
                  <Label>Making Charges (₹)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={saleItem.makingCharges}
                    onChange={(e) => updateSaleItem(index, "makingCharges", Number.parseFloat(e.target.value) || 0)}
                    placeholder="8500"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Stone Amount (₹)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={saleItem.stoneAmount}
                    onChange={(e) => updateSaleItem(index, "stoneAmount", Number.parseFloat(e.target.value) || 0)}
                    placeholder="15000"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Total Amount</Label>
                  <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                    <p className="font-semibold text-green-700">₹{saleItem.amount.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              {saleItem.item.id && (
                <div className="flex gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">{saleItem.item.category}</Badge>
                  <Badge variant="outline">
                    {saleItem.item.metalType} {saleItem.item.purity}
                  </Badge>
                  <span>Stock: {saleItem.item.stock}</span>
                </div>
              )}
            </Card>
          ))}

          {saleItems.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No items added yet. Click "Add Item" to start.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Totals */}
      {saleItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Invoice Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>₹{subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>CGST ({settings.cgstRate}%):</span>
                <span>₹{cgst.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>SGST ({settings.sgstRate}%):</span>
                <span>₹{sgst.toLocaleString()}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>₹{total.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting || saleItems.length === 0}>
          {isSubmitting ? "Saving..." : sale ? "Update Sale" : "Create Sale"}
        </Button>
      </div>
    </form>
  )
}
