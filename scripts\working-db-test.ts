#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function testDatabaseOperations() {
  console.log('🧪 Testing database operations...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  console.log('Database config:')
  console.log('Host:', dbConfig.host)
  console.log('User:', dbConfig.user)
  console.log('Database:', dbConfig.database)
  console.log()

  try {
    // Test 1: Database Connection
    console.log('1. Testing database connection...')
    const connection = await mysql.createConnection(dbConfig)
    console.log('✅ Database connection successful')

    // Test 2: Check tables exist
    console.log('2. Checking database tables...')
    const [tables] = await connection.execute('SHOW TABLES')
    const tableList = (tables as any[]).map(row => Object.values(row)[0])
    console.log(`✅ Found ${tableList.length} tables:`, tableList.join(', '))

    // Test 3: Check users
    console.log('3. Testing users table...')
    const [users] = await connection.execute('SELECT id, name, email, role FROM users')
    console.log(`✅ Found ${(users as any[]).length} users`)
    if ((users as any[]).length > 0) {
      const firstUser = (users as any[])[0]
      console.log(`✅ First user: ${firstUser.name} (${firstUser.email}) - ${firstUser.role}`)
    }

    // Test 4: Check customers
    console.log('4. Testing customers table...')
    const [customers] = await connection.execute('SELECT COUNT(*) as count FROM customers')
    const customerCount = (customers as any[])[0].count
    console.log(`✅ Found ${customerCount} customers`)

    // Test 5: Check inventory
    console.log('5. Testing inventory table...')
    const [inventory] = await connection.execute('SELECT COUNT(*) as count FROM inventory')
    const inventoryCount = (inventory as any[])[0].count
    console.log(`✅ Found ${inventoryCount} inventory items`)

    // Test 6: Check settings
    console.log('6. Testing settings table...')
    const [settings] = await connection.execute('SELECT business_name, currency FROM settings LIMIT 1')
    if ((settings as any[]).length > 0) {
      const setting = (settings as any[])[0]
      console.log(`✅ Settings loaded: ${setting.business_name} (${setting.currency})`)
    } else {
      console.log('⚠️  No settings found')
    }

    // Test 7: Test a simple insert and delete
    console.log('7. Testing CRUD operations...')
    
    // Insert test customer
    const testId = crypto.randomUUID()
    await connection.execute(
      'INSERT INTO customers (id, name, phone, email, address, total_purchases, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
      [testId, 'Test Customer', '+91 99999 99999', '<EMAIL>', 'Test Address', 0]
    )
    console.log('✅ Test customer created')
    
    // Update test customer
    await connection.execute(
      'UPDATE customers SET name = ? WHERE id = ?',
      ['Updated Test Customer', testId]
    )
    console.log('✅ Test customer updated')
    
    // Delete test customer
    await connection.execute('DELETE FROM customers WHERE id = ?', [testId])
    console.log('✅ Test customer deleted')

    await connection.end()
    
    console.log('\n🎉 All database tests passed successfully!')
    console.log('\nDatabase is ready for use!')
    console.log('You can now start the application with: pnpm run dev')
    
    return true

  } catch (error) {
    console.error('❌ Database test failed:', error)
    return false
  }
}

async function main() {
  const success = await testDatabaseOperations()
  process.exit(success ? 0 : 1)
}

main()
