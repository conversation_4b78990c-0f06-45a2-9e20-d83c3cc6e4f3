import mysql from 'mysql2/promise'

export interface DatabaseConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
  connectionLimit: number
}

export function getDatabaseConfig(): DatabaseConfig {
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'username',
    password: process.env.DB_PASSWORD !== undefined ? process.env.DB_PASSWORD : 'password',
    database: process.env.DB_NAME || 'jjjewellers_db',
    connectionLimit: 10,
  }
}

export const databaseConfig: DatabaseConfig = getDatabaseConfig()

// Create connection pool
let pool: mysql.Pool | null = null

export function getPool(): mysql.Pool {
  if (!pool) {
    const config = getDatabaseConfig()
    pool = mysql.createPool({
      ...config,
      waitForConnections: true,
      queueLimit: 0
    })
  }
  return pool
}

export function resetPool(): void {
  if (pool) {
    pool.end()
    pool = null
  }
}

export async function testConnection(): Promise<boolean> {
  try {
    const connection = await getPool().getConnection()
    await connection.ping()
    connection.release()
    console.log('Database connection successful')
    return true
  } catch (error) {
    console.error('Database connection failed:', error)
    return false
  }
}

export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
  }
}
