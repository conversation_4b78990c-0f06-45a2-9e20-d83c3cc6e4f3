import { NextRequest, NextResponse } from 'next/server'
import { inventoryService } from '@/lib/database/services'

export async function GET() {
  try {
    const inventory = await inventoryService.findAll()
    return NextResponse.json({ inventory })
  } catch (error) {
    console.error('Error fetching inventory:', error)
    return NextResponse.json(
      { error: 'Failed to fetch inventory' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const itemData = await request.json()
    const item = await inventoryService.create(itemData)
    return NextResponse.json({ item })
  } catch (error) {
    console.error('Error creating inventory item:', error)
    return NextResponse.json(
      { error: 'Failed to create inventory item' },
      { status: 500 }
    )
  }
}
