import { NextRequest, NextResponse } from 'next/server'
import { settingsService } from '@/lib/database/services'

export async function GET() {
  try {
    const settings = await settingsService.getSettings()
    return NextResponse.json({ settings })
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const settingsData = await request.json()
    const settings = await settingsService.updateSettings(settingsData)
    return NextResponse.json({ settings })
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}
