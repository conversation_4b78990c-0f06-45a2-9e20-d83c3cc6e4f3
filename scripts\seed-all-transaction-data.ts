#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedAllTransactionData() {
  console.log('🌱 Seeding All Transaction & Supporting Data for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Get reference data
    const [users] = await connection.execute('SELECT id, username, role FROM users')
    const [customers] = await connection.execute('SELECT id, customer_code, first_name, last_name FROM customers')
    const [inventory] = await connection.execute('SELECT id, item_code, name, selling_price, metal_type, purity, gross_weight, stone_weight, wastage_percentage FROM inventory WHERE status = "active"')

    const userMap = new Map()
    const customerMap = new Map()
    const inventoryMap = new Map()

    ;(users as any[]).forEach(user => userMap.set(user.username, user))
    ;(customers as any[]).forEach(customer => customerMap.set(customer.customer_code, customer))
    ;(inventory as any[]).forEach(item => inventoryMap.set(item.item_code, item))

    // Step 1: Create Bill Sequences
    console.log('📄 Step 1: Setting up bill sequences...')
    
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
    
    const sequences = [
      { type: 'sales_invoice', name: 'Sales Invoice', prefix: 'INV', number: 5 },
      { type: 'exchange_transaction', name: 'Exchange Transaction', prefix: 'EXG', number: 3 },
      { type: 'purchase_bill', name: 'Purchase Bill', prefix: 'PUR', number: 2 },
      { type: 'exchange_purchase', name: 'Exchange Purchase Bill', prefix: 'EPB', number: 3 },
      { type: 'scheme_receipt', name: 'Scheme Receipt', prefix: 'SCH', number: 0 },
      { type: 'repair_receipt', name: 'Repair Receipt', prefix: 'REP', number: 0 }
    ]

    for (const seq of sequences) {
      await connection.execute(`
        INSERT INTO bill_sequences (
          id, sequence_type, sequence_name, prefix, current_number, financial_year,
          reset_annually, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, TRUE, TRUE, NOW(), NOW())
      `, [randomUUID(), seq.type, seq.name, seq.prefix, seq.number, financialYear])
    }
    console.log(`   ✅ Set up ${sequences.length} bill sequences`)

    // Step 2: Create Suppliers
    console.log('\n🏭 Step 2: Creating suppliers...')
    
    const suppliers = [
      {
        id: randomUUID(),
        code: 'SUP001',
        company: 'Chennai Gold Crafts Pvt Ltd',
        contact: 'Ravi Shankar',
        phone: '+91-44-12345678',
        email: '<EMAIL>',
        city: 'Chennai',
        state: 'Tamil Nadu',
        type: 'manufacturer',
        gst: '33ABCDE1234F1Z1',
        rating: 4.5
      },
      {
        id: randomUUID(),
        code: 'SUP002',
        company: 'Coimbatore Silver Works',
        contact: 'Meera Devi',
        phone: '+91-422-87654321',
        email: '<EMAIL>',
        city: 'Coimbatore',
        state: 'Tamil Nadu',
        type: 'wholesaler',
        gst: '33FGHIJ5678K2L3',
        rating: 4.2
      },
      {
        id: randomUUID(),
        code: 'SUP003',
        company: 'Madurai Temple Jewelry',
        contact: 'Suresh Kumar',
        phone: '+91-452-98765432',
        email: '<EMAIL>',
        city: 'Madurai',
        state: 'Tamil Nadu',
        type: 'artisan',
        gst: '33MNOPQ9012R3S4',
        rating: 4.8
      },
      {
        id: randomUUID(),
        code: 'SUP004',
        company: 'Salem Gold Refiners',
        contact: 'Lakshmi Narayanan',
        phone: '+91-427-11223344',
        email: '<EMAIL>',
        city: 'Salem',
        state: 'Tamil Nadu',
        type: 'refiner',
        gst: '33TUVWX3456Y7Z8',
        rating: 4.3
      }
    ]

    for (const supplier of suppliers) {
      await connection.execute(`
        INSERT INTO suppliers (
          id, supplier_code, supplier_type, company_name, contact_person, contact_phone, contact_email,
          city, state, country, gst_number, credit_limit, credit_days, rating,
          is_active, is_preferred, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'India', ?, 500000.00, 30, ?, TRUE, ?, NOW(), NOW())
      `, [
        supplier.id, supplier.code, supplier.type, supplier.company, supplier.contact,
        supplier.phone, supplier.email, supplier.city, supplier.state, supplier.gst,
        supplier.rating, supplier.rating >= 4.5
      ])
    }
    console.log(`   ✅ Created ${suppliers.length} suppliers`)

    // Step 3: Create Exchange Transactions
    console.log('\n🔄 Step 3: Creating exchange transactions...')
    
    const exchangeTransactions = [
      {
        id: randomUUID(),
        number: `EXG/${financialYear}/001`,
        customer_id: customerMap.get('TRP000001')?.id,
        customer_name: 'Rajesh Murugan',
        customer_phone: '+91-9876543301',
        total_amount: 285000.00,
        status: 'completed',
        notes: 'Old gold jewelry exchange - traditional designs'
      },
      {
        id: randomUUID(),
        number: `EXG/${financialYear}/002`,
        customer_id: customerMap.get('TRP000003')?.id,
        customer_name: 'Murugan Raman',
        customer_phone: '+91-9876543303',
        total_amount: 156000.00,
        status: 'completed',
        notes: 'Gold chain and bangles exchange'
      },
      {
        id: randomUUID(),
        number: `EXG/${financialYear}/003`,
        customer_id: customerMap.get('TRP000005')?.id,
        customer_name: 'Senthil Kumar',
        customer_phone: '+91-9876543305',
        total_amount: 425000.00,
        status: 'completed',
        notes: 'Premium gold jewelry collection exchange'
      }
    ]

    for (const transaction of exchangeTransactions) {
      await connection.execute(`
        INSERT INTO exchange_transactions (
          id, transaction_number, transaction_date, customer_id, customer_name, customer_phone,
          total_items, total_amount, payment_method, status, notes,
          received_by, evaluated_by, processed_by, created_by, created_at, updated_at
        ) VALUES (?, ?, CURDATE(), ?, ?, ?, 3, ?, 'cash', ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        transaction.id, transaction.number, transaction.customer_id, transaction.customer_name,
        transaction.customer_phone, transaction.total_amount, transaction.status, transaction.notes,
        userMap.get('sales1')?.id, userMap.get('manager')?.id, userMap.get('admin')?.id, userMap.get('admin')?.id
      ])
    }
    console.log(`   ✅ Created ${exchangeTransactions.length} exchange transactions`)

    // Step 4: Create Exchange Items
    console.log('\n💎 Step 4: Creating exchange items...')
    
    const exchangeItems = [
      // Transaction 1 items
      { transaction_id: exchangeTransactions[0].id, item_number: 1, description: 'Gold Necklace Traditional Heavy', metal: 'gold', purity: '22K', gross: 35.000, stone: 5.000, rate: 6590.00, condition: 'good' },
      { transaction_id: exchangeTransactions[0].id, item_number: 2, description: 'Gold Bangles Pair Antique', metal: 'gold', purity: '22K', gross: 28.000, stone: 0.000, rate: 6590.00, condition: 'very_good' },
      { transaction_id: exchangeTransactions[0].id, item_number: 3, description: 'Gold Earrings Temple Design', metal: 'gold', purity: '22K', gross: 12.000, stone: 3.000, rate: 6590.00, condition: 'good' },
      
      // Transaction 2 items
      { transaction_id: exchangeTransactions[1].id, item_number: 1, description: 'Gold Chain Mens Thick', metal: 'gold', purity: '22K', gross: 22.000, stone: 0.000, rate: 6590.00, condition: 'excellent' },
      { transaction_id: exchangeTransactions[1].id, item_number: 2, description: 'Gold Bangles Single Heavy', metal: 'gold', purity: '22K', gross: 18.500, stone: 0.000, rate: 6590.00, condition: 'good' },
      
      // Transaction 3 items
      { transaction_id: exchangeTransactions[2].id, item_number: 1, description: 'Gold Necklace Set Designer', metal: 'gold', purity: '22K', gross: 45.000, stone: 8.000, rate: 6590.00, condition: 'excellent' },
      { transaction_id: exchangeTransactions[2].id, item_number: 2, description: 'Gold Bracelet Diamond Studded', metal: 'gold', purity: '18K', gross: 15.000, stone: 5.000, rate: 5370.00, condition: 'very_good' },
      { transaction_id: exchangeTransactions[2].id, item_number: 3, description: 'Gold Ring Wedding Set', metal: 'gold', purity: '22K', gross: 25.000, stone: 2.000, rate: 6590.00, condition: 'good' }
    ]

    for (const item of exchangeItems) {
      await connection.execute(`
        INSERT INTO exchange_items (
          id, transaction_id, item_number, item_description, item_type, metal_type, purity,
          gross_weight, stone_weight, rate_per_gram, item_condition, hallmark_available,
          hallmark_verified, purity_verified, status, evaluated_by, evaluated_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 'jewelry', ?, ?, ?, ?, ?, ?, TRUE, TRUE, TRUE, 'approved', ?, NOW(), NOW(), NOW())
      `, [
        randomUUID(), item.transaction_id, item.item_number, item.description, item.metal, item.purity,
        item.gross, item.stone, item.rate, item.condition, userMap.get('manager')?.id
      ])
    }
    console.log(`   ✅ Created ${exchangeItems.length} exchange items`)

    console.log('\n🎉 All Transaction & Supporting Data Seeded Successfully!')

  } catch (error) {
    console.error('\n❌ Transaction data seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the transaction data seeding
seedAllTransactionData().catch(console.error)
