// Enhanced validation for Exchange System
// Provides comprehensive validation for all exchange-related operations

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface ExchangeItemValidation {
  itemDescription: string
  metalType: 'gold' | 'silver'
  purity: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  ratePerGram: number
  amount: number
  itemCondition: 'good' | 'fair' | 'poor'
}

export interface ExchangeTransactionValidation {
  transactionNumber: string
  customerId: string
  transactionDate: string
  totalAmount: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
  status: 'pending' | 'completed' | 'cancelled'
  items: ExchangeItemValidation[]
}

export interface PurchaseBillValidation {
  exchangeTransactionId: string
  customerId: string
  billDate: string
  totalAmount: number
  cgstAmount: number
  sgstAmount: number
  totalWithTax: number
  paymentMethod: 'cash' | 'bank_transfer' | 'adjustment'
}

export class ExchangeValidator {
  
  // Validate exchange item
  static validateExchangeItem(item: ExchangeItemValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Required field validation
    if (!item.itemDescription || item.itemDescription.trim().length === 0) {
      errors.push('Item description is required')
    }

    if (!item.metalType) {
      errors.push('Metal type is required')
    }

    if (!item.purity) {
      errors.push('Purity is required')
    }

    // Negative value validation
    if (item.grossWeight < 0) {
      errors.push('Gross weight cannot be negative')
    }

    if (item.stoneWeight < 0) {
      errors.push('Stone weight cannot be negative')
    }

    if (item.netWeight < 0) {
      errors.push('Net weight cannot be negative')
    }

    if (item.ratePerGram <= 0) {
      errors.push('Rate per gram must be greater than zero')
    }

    if (item.amount < 0) {
      errors.push('Amount cannot be negative')
    }

    // Zero value warnings
    if (item.grossWeight === 0) {
      warnings.push('Gross weight is zero - please verify')
    }

    if (item.netWeight === 0) {
      warnings.push('Net weight is zero - please verify')
    }

    // Weight logic validation
    if (item.netWeight > item.grossWeight) {
      errors.push('Net weight cannot be greater than gross weight')
    }

    if (item.stoneWeight > item.grossWeight) {
      errors.push('Stone weight cannot be greater than gross weight')
    }

    // Business logic validation
    const expectedNetWeight = item.grossWeight - item.stoneWeight
    if (Math.abs(item.netWeight - expectedNetWeight) > 0.001) {
      errors.push(`Net weight should be ${expectedNetWeight.toFixed(3)}g (gross weight - stone weight)`)
    }

    // Amount calculation validation
    const expectedAmount = item.netWeight * item.ratePerGram
    if (Math.abs(item.amount - expectedAmount) > 1.00) {
      errors.push(`Amount should be ₹${expectedAmount.toFixed(2)} (net weight × rate per gram)`)
    }

    // Purity validation
    const validGoldPurities = ['24K', '22K', '18K', '14K', '10K']
    const validSilverPurities = ['999', '925', '900', '800']
    
    if (item.metalType === 'gold' && !validGoldPurities.includes(item.purity)) {
      errors.push(`Invalid gold purity. Valid options: ${validGoldPurities.join(', ')}`)
    }

    if (item.metalType === 'silver' && !validSilverPurities.includes(item.purity)) {
      errors.push(`Invalid silver purity. Valid options: ${validSilverPurities.join(', ')}`)
    }

    // Rate reasonableness validation
    if (item.metalType === 'gold') {
      if (item.ratePerGram < 1000 || item.ratePerGram > 20000) {
        warnings.push('Gold rate seems unusual (expected range: ₹1,000 - ₹20,000 per gram)')
      }
    }

    if (item.metalType === 'silver') {
      if (item.ratePerGram < 10 || item.ratePerGram > 500) {
        warnings.push('Silver rate seems unusual (expected range: ₹10 - ₹500 per gram)')
      }
    }

    // Weight reasonableness validation
    if (item.grossWeight > 10000) {
      warnings.push('Gross weight is very high (over 10kg) - please verify')
    }

    if (item.grossWeight < 0.001) {
      warnings.push('Gross weight is very low (under 1mg) - please verify')
    }

    // Description validation
    if (item.itemDescription.length > 255) {
      errors.push('Item description is too long (maximum 255 characters)')
    }

    // Special character validation
    const invalidChars = /[<>\"'&]/
    if (invalidChars.test(item.itemDescription)) {
      errors.push('Item description contains invalid characters')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Validate exchange transaction
  static validateExchangeTransaction(transaction: ExchangeTransactionValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Required field validation
    if (!transaction.transactionNumber || transaction.transactionNumber.trim().length === 0) {
      errors.push('Transaction number is required')
    }

    if (!transaction.customerId || transaction.customerId.trim().length === 0) {
      errors.push('Customer ID is required')
    }

    if (!transaction.transactionDate) {
      errors.push('Transaction date is required')
    }

    // Negative value validation
    if (transaction.totalAmount < 0) {
      errors.push('Total amount cannot be negative')
    }

    // Date validation
    const transactionDate = new Date(transaction.transactionDate)
    const today = new Date()
    const minDate = new Date('2020-01-01')
    const maxDate = new Date(today.getTime() + 24 * 60 * 60 * 1000) // Tomorrow

    if (transactionDate < minDate) {
      errors.push('Transaction date cannot be before January 1, 2020')
    }

    if (transactionDate > maxDate) {
      errors.push('Transaction date cannot be in the future')
    }

    // Transaction number format validation
    const transactionNumberPattern = /^EXG-\d{8}-\d{3}$/
    if (!transactionNumberPattern.test(transaction.transactionNumber)) {
      errors.push('Transaction number must follow format: EXG-YYYYMMDD-XXX')
    }

    // Items validation
    if (!transaction.items || transaction.items.length === 0) {
      errors.push('At least one exchange item is required')
    }

    if (transaction.items && transaction.items.length > 50) {
      warnings.push('Large number of items in single transaction - consider splitting')
    }

    // Validate each item
    let totalCalculatedAmount = 0
    transaction.items?.forEach((item, index) => {
      const itemValidation = this.validateExchangeItem(item)
      
      // Prefix item errors with item number
      itemValidation.errors.forEach(error => {
        errors.push(`Item ${index + 1}: ${error}`)
      })

      itemValidation.warnings.forEach(warning => {
        warnings.push(`Item ${index + 1}: ${warning}`)
      })

      totalCalculatedAmount += item.amount
    })

    // Total amount validation
    if (Math.abs(transaction.totalAmount - totalCalculatedAmount) > 1.00) {
      errors.push(`Total amount (₹${transaction.totalAmount}) does not match sum of item amounts (₹${totalCalculatedAmount.toFixed(2)})`)
    }

    // Status validation
    const validStatuses = ['pending', 'completed', 'cancelled']
    if (!validStatuses.includes(transaction.status)) {
      errors.push(`Invalid status. Valid options: ${validStatuses.join(', ')}`)
    }

    // Payment method validation
    const validPaymentMethods = ['cash', 'bank_transfer', 'adjustment']
    if (!validPaymentMethods.includes(transaction.paymentMethod)) {
      errors.push(`Invalid payment method. Valid options: ${validPaymentMethods.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Validate purchase bill
  static validatePurchaseBill(bill: PurchaseBillValidation): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Required field validation
    if (!bill.exchangeTransactionId) {
      errors.push('Exchange transaction ID is required')
    }

    if (!bill.customerId) {
      errors.push('Customer ID is required')
    }

    if (!bill.billDate) {
      errors.push('Bill date is required')
    }

    // Negative value validation
    if (bill.totalAmount < 0) {
      errors.push('Total amount cannot be negative')
    }

    if (bill.cgstAmount < 0) {
      errors.push('CGST amount cannot be negative')
    }

    if (bill.sgstAmount < 0) {
      errors.push('SGST amount cannot be negative')
    }

    if (bill.totalWithTax < 0) {
      errors.push('Total with tax cannot be negative')
    }

    // Date validation
    const billDate = new Date(bill.billDate)
    const today = new Date()
    const minDate = new Date('2020-01-01')
    const maxDate = new Date(today.getTime() + 24 * 60 * 60 * 1000) // Tomorrow

    if (billDate < minDate) {
      errors.push('Bill date cannot be before January 1, 2020')
    }

    if (billDate > maxDate) {
      errors.push('Bill date cannot be in the future')
    }

    // Tax calculation validation
    const expectedTotal = bill.totalAmount + bill.cgstAmount + bill.sgstAmount
    if (Math.abs(bill.totalWithTax - expectedTotal) > 1.00) {
      errors.push(`Total with tax (₹${bill.totalWithTax}) does not match total amount + CGST + SGST (₹${expectedTotal.toFixed(2)})`)
    }

    // Tax rate reasonableness
    if (bill.totalAmount > 0) {
      const cgstRate = (bill.cgstAmount / bill.totalAmount) * 100
      const sgstRate = (bill.sgstAmount / bill.totalAmount) * 100

      if (cgstRate < 0 || cgstRate > 20) {
        warnings.push(`CGST rate (${cgstRate.toFixed(2)}%) seems unusual`)
      }

      if (sgstRate < 0 || sgstRate > 20) {
        warnings.push(`SGST rate (${sgstRate.toFixed(2)}%) seems unusual`)
      }

      if (Math.abs(cgstRate - sgstRate) > 0.1) {
        warnings.push('CGST and SGST rates are different - please verify')
      }
    }

    // Total reasonableness
    if (bill.totalWithTax < bill.totalAmount) {
      errors.push('Total with tax cannot be less than total amount')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Validate exchange rate
  static validateExchangeRate(rate: {
    metalType: 'gold' | 'silver'
    purity: string
    ratePerGram: number
    effectiveDate: string
  }): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Required field validation
    if (!rate.metalType) {
      errors.push('Metal type is required')
    }

    if (!rate.purity) {
      errors.push('Purity is required')
    }

    if (!rate.effectiveDate) {
      errors.push('Effective date is required')
    }

    // Negative value validation
    if (rate.ratePerGram <= 0) {
      errors.push('Rate per gram must be greater than zero')
    }

    // Date validation
    const effectiveDate = new Date(rate.effectiveDate)
    const today = new Date()
    const maxDate = new Date(today.getTime() + 365 * 24 * 60 * 60 * 1000) // 1 year from now

    if (effectiveDate > maxDate) {
      errors.push('Effective date cannot be more than 1 year in the future')
    }

    // Purity validation
    const validGoldPurities = ['24K', '22K', '18K', '14K', '10K']
    const validSilverPurities = ['999', '925', '900', '800']
    
    if (rate.metalType === 'gold' && !validGoldPurities.includes(rate.purity)) {
      errors.push(`Invalid gold purity. Valid options: ${validGoldPurities.join(', ')}`)
    }

    if (rate.metalType === 'silver' && !validSilverPurities.includes(rate.purity)) {
      errors.push(`Invalid silver purity. Valid options: ${validSilverPurities.join(', ')}`)
    }

    // Rate reasonableness validation
    if (rate.metalType === 'gold') {
      if (rate.ratePerGram < 1000 || rate.ratePerGram > 20000) {
        warnings.push('Gold rate seems unusual (expected range: ₹1,000 - ₹20,000 per gram)')
      }
    }

    if (rate.metalType === 'silver') {
      if (rate.ratePerGram < 10 || rate.ratePerGram > 500) {
        warnings.push('Silver rate seems unusual (expected range: ₹10 - ₹500 per gram)')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Validate sales exchange deduction
  static validateSalesExchangeDeduction(deduction: {
    saleTotal: number
    exchangeValue: number
    deductionAmount: number
  }): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Negative value validation
    if (deduction.saleTotal < 0) {
      errors.push('Sale total cannot be negative')
    }

    if (deduction.exchangeValue < 0) {
      errors.push('Exchange value cannot be negative')
    }

    if (deduction.deductionAmount < 0) {
      errors.push('Deduction amount cannot be negative')
    }

    // Business logic validation
    if (deduction.deductionAmount > deduction.exchangeValue) {
      errors.push('Deduction amount cannot exceed exchange value')
    }

    if (deduction.deductionAmount > deduction.saleTotal) {
      warnings.push('Deduction amount exceeds sale total - customer will receive credit')
    }

    // Zero value warnings
    if (deduction.deductionAmount === 0) {
      warnings.push('No deduction applied - exchange value not being used')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}
