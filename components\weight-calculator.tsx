"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Scale, Calculator } from "lucide-react"

export function WeightCalculator() {
  const [formData, setFormData] = useState({
    grossWeight: "",
    stoneWeight: "",
    wastage: "",
    unit: "grams",
  })

  const [result, setResult] = useState<{
    netWeight: number
    wastageAmount: number
    finalWeight: number
  } | null>(null)

  const calculateWeight = () => {
    const gross = Number.parseFloat(formData.grossWeight) || 0
    const stone = Number.parseFloat(formData.stoneWeight) || 0
    const wastagePercent = Number.parseFloat(formData.wastage) || 0

    if (gross === 0) {
      alert("Please enter gross weight")
      return
    }

    const netWeight = Math.max(0, gross - stone)
    const wastageAmount = (netWeight * wastagePercent) / 100
    const finalWeight = netWeight - wastageAmount

    setResult({
      netWeight,
      wastageAmount,
      finalWeight,
    })
  }

  const resetCalculator = () => {
    setFormData({
      grossWeight: "",
      stoneWeight: "",
      wastage: "",
      unit: "grams",
    })
    setResult(null)
  }

  const convertWeight = (weight: number) => {
    if (formData.unit === "tola") {
      return (weight / 11.664).toFixed(3) + " tola"
    }
    return weight.toFixed(3) + " g"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Scale className="h-5 w-5 text-blue-600" />
          Weight Calculator
        </CardTitle>
        <CardDescription>Calculate net weight after stones and wastage</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="grossWeight">Gross Weight</Label>
            <Input
              id="grossWeight"
              type="number"
              step="0.001"
              value={formData.grossWeight}
              onChange={(e) => setFormData({ ...formData, grossWeight: e.target.value })}
              placeholder="25.500"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="stoneWeight">Stone Weight</Label>
            <Input
              id="stoneWeight"
              type="number"
              step="0.001"
              value={formData.stoneWeight}
              onChange={(e) => setFormData({ ...formData, stoneWeight: e.target.value })}
              placeholder="2.500"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="wastage">Wastage (%)</Label>
            <Input
              id="wastage"
              type="number"
              step="0.1"
              value={formData.wastage}
              onChange={(e) => setFormData({ ...formData, wastage: e.target.value })}
              placeholder="2.0"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="unit">Display Unit</Label>
            <Select value={formData.unit || undefined} onValueChange={(value) => setFormData({ ...formData, unit: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="grams">Grams</SelectItem>
                <SelectItem value="tola">Tola</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={calculateWeight} className="flex-1">
            <Calculator className="h-4 w-4 mr-2" />
            Calculate
          </Button>
          <Button variant="outline" onClick={resetCalculator}>
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Weight Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Net Weight (Gross - Stone):</span>
                <span className="font-medium">{convertWeight(result.netWeight)}</span>
              </div>
              <div className="flex justify-between">
                <span>Wastage Amount:</span>
                <span className="font-medium text-red-600">{convertWeight(result.wastageAmount)}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="font-semibold">Final Weight:</span>
                <span className="font-bold text-blue-600">{convertWeight(result.finalWeight)}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
