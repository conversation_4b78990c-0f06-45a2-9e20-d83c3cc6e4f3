-- Create database
CREATE DATABASE IF NOT EXISTS jjjewellers_db;
USE jjjewellers_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHA<PERSON>(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  role ENUM('admin', 'manager', 'staff') NOT NULL DEFAULT 'staff',
  permissions JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_role (role)
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  gst_number VARCHA<PERSON>(50),
  total_purchases DECIMAL(15,2) DEFAULT 0.00,
  last_visit TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_phone (phone),
  INDEX idx_email (email)
);

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100),
  metal_type VARCHAR(50),
  gross_weight DECIMAL(10,3),
  stone_weight DECIMAL(10,3),
  net_weight DECIMAL(10,3),
  stone_amount DECIMAL(15,2),
  purity VARCHAR(10),
  making_charges DECIMAL(15,2),
  current_value DECIMAL(15,2),
  stock INT DEFAULT 0,
  stone_details TEXT,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_metal_type (metal_type),
  INDEX idx_stock (stock)
);

-- Sales table
CREATE TABLE IF NOT EXISTS sales (
  id VARCHAR(36) PRIMARY KEY,
  customer_id VARCHAR(36),
  subtotal DECIMAL(15,2),
  cgst DECIMAL(15,2),
  sgst DECIMAL(15,2),
  total DECIMAL(15,2),
  status ENUM('draft', 'paid', 'pending', 'cancelled') DEFAULT 'draft',
  date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_status (status),
  INDEX idx_date (date)
);

-- Sale items table
CREATE TABLE IF NOT EXISTS sale_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36),
  inventory_id VARCHAR(36),
  gross_weight DECIMAL(10,3),
  stone_weight DECIMAL(10,3),
  net_weight DECIMAL(10,3),
  rate DECIMAL(10,2),
  making_charges DECIMAL(15,2),
  stone_amount DECIMAL(15,2),
  amount DECIMAL(15,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE SET NULL,
  INDEX idx_sale (sale_id),
  INDEX idx_inventory (inventory_id)
);

-- Schemes table
CREATE TABLE IF NOT EXISTS schemes (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  customer_id VARCHAR(36),
  total_amount DECIMAL(15,2),
  paid_amount DECIMAL(15,2) DEFAULT 0.00,
  monthly_amount DECIMAL(15,2),
  duration INT,
  start_date DATE,
  status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_status (status)
);

-- Repairs table
CREATE TABLE IF NOT EXISTS repairs (
  id VARCHAR(36) PRIMARY KEY,
  customer_id VARCHAR(36),
  item VARCHAR(255),
  description TEXT,
  order_type ENUM('repair', 'custom', 'resize', 'polish') DEFAULT 'repair',
  received_date DATE,
  promised_date DATE,
  status ENUM('pending', 'in-progress', 'completed', 'delivered') DEFAULT 'pending',
  charges DECIMAL(15,2),
  special_instructions TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  INDEX idx_customer (customer_id),
  INDEX idx_status (status),
  INDEX idx_promised_date (promised_date)
);

-- Purchases table
CREATE TABLE IF NOT EXISTS purchases (
  id VARCHAR(36) PRIMARY KEY,
  supplier VARCHAR(255),
  items TEXT,
  amount DECIMAL(15,2),
  status ENUM('pending', 'received', 'cancelled') DEFAULT 'pending',
  date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_supplier (supplier),
  INDEX idx_status (status),
  INDEX idx_date (date)
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  business_name VARCHAR(255),
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  gst_number VARCHAR(50),
  metal_rates JSON,
  auto_update_rates BOOLEAN DEFAULT TRUE,
  cgst_rate DECIMAL(5,2),
  sgst_rate DECIMAL(5,2),
  low_stock_alert BOOLEAN DEFAULT TRUE,
  low_stock_threshold INT DEFAULT 5,
  scheme_reminders BOOLEAN DEFAULT TRUE,
  repair_reminders BOOLEAN DEFAULT TRUE,
  currency VARCHAR(10) DEFAULT 'INR',
  date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
  backup_frequency VARCHAR(20) DEFAULT 'daily',
  invoice_template VARCHAR(50) DEFAULT 'standard',
  print_logo BOOLEAN DEFAULT TRUE,
  print_terms BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
