#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { readFile } from 'fs/promises'

async function applyEnhancedValidation() {
  console.log('🔒 Applying Enhanced Validation and Constraints...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Read and execute the validation migration script
    console.log('📄 Reading validation migration script...')
    const migrationPath = resolve(process.cwd(), 'scripts/migrations/003_add_validation_constraints.sql')
    const migrationSQL = await readFile(migrationPath, 'utf8')
    
    console.log('🔧 Applying validation constraints...')
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.toLowerCase().includes('commit'))

    let successCount = 0
    let warningCount = 0

    for (const statement of statements) {
      try {
        if (statement.toLowerCase().includes('select ')) {
          // Skip SELECT statements (they're just for display)
          continue
        }

        await connection.execute(statement)
        successCount++
        
        // Log specific constraint additions
        if (statement.toLowerCase().includes('add constraint')) {
          const constraintMatch = statement.match(/add constraint (\w+)/i)
          if (constraintMatch) {
            console.log(`✅ Added constraint: ${constraintMatch[1]}`)
          }
        } else if (statement.toLowerCase().includes('create trigger')) {
          const triggerMatch = statement.match(/create trigger (\w+)/i)
          if (triggerMatch) {
            console.log(`✅ Created trigger: ${triggerMatch[1]}`)
          }
        } else if (statement.toLowerCase().includes('create index')) {
          const indexMatch = statement.match(/create index (\w+)/i)
          if (indexMatch) {
            console.log(`✅ Created index: ${indexMatch[1]}`)
          }
        }
        
      } catch (error) {
        warningCount++
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        
        // Some errors are expected (like constraint already exists)
        if (errorMessage.includes('already exists') || 
            errorMessage.includes('Duplicate key name') ||
            errorMessage.includes('Duplicate constraint name')) {
          console.log(`⚠️  Skipped (already exists): ${statement.substring(0, 50)}...`)
        } else {
          console.log(`❌ Error: ${errorMessage}`)
          console.log(`   Statement: ${statement.substring(0, 100)}...`)
        }
      }
    }

    console.log(`\n📊 Migration Summary:`)
    console.log(`✅ Successful operations: ${successCount}`)
    console.log(`⚠️  Warnings/Skipped: ${warningCount}`)

    // Test the constraints
    console.log('\n🧪 Testing validation constraints...')
    
    // Test 1: Try to insert negative weight (should fail)
    console.log('🔍 Test 1: Negative weight validation')
    try {
      await connection.execute(`
        INSERT INTO exchange_items 
        (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount) 
        VALUES ('test-negative', 'exg_001', 'TEST ITEM', 'gold', '22K', -1.0, 0.0, -1.0, 6200.00, -6200.00)
      `)
      console.log('❌ Negative weight validation failed - constraint not working')
    } catch (error) {
      console.log('✅ Negative weight validation working correctly')
    }

    // Test 2: Try to insert invalid purity (should fail)
    console.log('🔍 Test 2: Invalid purity validation')
    try {
      await connection.execute(`
        INSERT INTO exchange_rates 
        (id, metal_type, purity, rate_per_gram, effective_date, is_active) 
        VALUES ('test-invalid-purity', 'gold', 'INVALID', 6200.00, CURDATE(), TRUE)
      `)
      console.log('❌ Invalid purity validation failed - constraint not working')
    } catch (error) {
      console.log('✅ Invalid purity validation working correctly')
    }

    // Test 3: Try to insert inconsistent weight logic (should fail)
    console.log('🔍 Test 3: Weight logic validation')
    try {
      await connection.execute(`
        INSERT INTO exchange_items 
        (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount) 
        VALUES ('test-weight-logic', 'exg_001', 'TEST ITEM', 'gold', '22K', 5.0, 2.0, 8.0, 6200.00, 49600.00)
      `)
      console.log('❌ Weight logic validation failed - constraint not working')
    } catch (error) {
      console.log('✅ Weight logic validation working correctly')
    }

    // Test 4: Check foreign key constraints
    console.log('🔍 Test 4: Foreign key constraint validation')
    try {
      await connection.execute(`
        INSERT INTO exchange_transactions 
        (id, transaction_number, customer_id, transaction_date, total_amount, status) 
        VALUES ('test-fk', 'TEST-FK-001', 'invalid_customer_id', CURDATE(), 1000.00, 'pending')
      `)
      console.log('❌ Foreign key validation failed - constraint not working')
    } catch (error) {
      console.log('✅ Foreign key validation working correctly')
    }

    // Cleanup any test data that might have been inserted
    try {
      await connection.execute(`DELETE FROM exchange_items WHERE id LIKE 'test-%'`)
      await connection.execute(`DELETE FROM exchange_transactions WHERE id LIKE 'test-%'`)
      await connection.execute(`DELETE FROM exchange_rates WHERE id LIKE 'test-%'`)
    } catch (error) {
      // Ignore cleanup errors
    }

    // Verify validation summary view
    console.log('\n📊 Checking validation summary...')
    try {
      const [validationSummary] = await connection.execute(`SELECT * FROM v_exchange_validation_summary`)
      console.log('✅ Validation summary view created successfully')
      console.log('📋 Current validation status:')
      ;(validationSummary as any[]).forEach(row => {
        console.log(`   ${row.table_name}: ${row.total_records} records`)
      })
    } catch (error) {
      console.log('⚠️  Validation summary view not available')
    }

    console.log('\n🎉 Enhanced Validation Applied Successfully!')
    
    console.log('\n📋 VALIDATION FEATURES ADDED:')
    console.log('✅ Check constraints for negative values')
    console.log('✅ Foreign key constraints with proper CASCADE/RESTRICT')
    console.log('✅ Business logic triggers for amount validation')
    console.log('✅ Purity validation for gold and silver')
    console.log('✅ Weight logic validation (net ≤ gross)')
    console.log('✅ Date range validation')
    console.log('✅ Rate reasonableness checks')
    console.log('✅ Tax calculation validation')
    console.log('✅ Performance indexes')
    console.log('✅ Validation summary view')

    console.log('\n🔒 SYSTEM SECURITY ENHANCED:')
    console.log('✅ Data integrity enforced at database level')
    console.log('✅ Business rules validated automatically')
    console.log('✅ Referential integrity maintained')
    console.log('✅ Invalid data prevented from entry')
    console.log('✅ Calculation accuracy ensured')

  } catch (error) {
    console.error('\n❌ Enhanced validation failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the enhanced validation
applyEnhancedValidation().catch(console.error)
