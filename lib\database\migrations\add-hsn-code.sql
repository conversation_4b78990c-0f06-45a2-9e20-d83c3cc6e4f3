-- Migration: Add HSN code column to inventory table
-- This migration adds the hsn_code column to the inventory table for tax compliance

-- Modify the ID column to accommodate longer jewelry IDs
ALTER TABLE inventory MODIFY COLUMN id VARCHAR(50);

-- Add HSN code column
ALTER TABLE inventory
ADD COLUMN hsn_code VARCHAR(10) AFTER description;

-- Add index for HSN code for faster queries
ALTER TABLE inventory ADD INDEX idx_hsn_code (hsn_code);

-- Update existing records with appropriate HSN codes based on category
UPDATE inventory SET hsn_code = '7113' WHERE category IN ('Rings', 'Necklaces', 'Earrings', 'Bracelets', 'Pendants', 'Chains', 'Bangles', 'Anklets', 'Sets', 'Nose Pins', 'Toe Rings', 'Armlets', 'Waist Chains', 'Hair Ornaments', 'Brooches', 'Cufflinks', 'Tie Pins', 'Gold Items', 'Platinum Items', 'Diamond Items');

UPDATE inventory SET hsn_code = '7114' WHERE category IN ('Silver Items') OR metal_type LIKE '%silver%';

UPDATE inventory SET hsn_code = '9101' WHERE category = 'Watches';

UPDATE inventory SET hsn_code = '9113' WHERE category = 'Watch Straps';

UPDATE inventory SET hsn_code = '7103' WHERE category IN ('Precious Stones', 'Semi Precious Stones');

UPDATE inventory SET hsn_code = '7101' WHERE category = 'Pearls';

-- Set default HSN code for any remaining items
UPDATE inventory SET hsn_code = '7113' WHERE hsn_code IS NULL;
