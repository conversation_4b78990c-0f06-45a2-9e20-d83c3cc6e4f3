#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function verifyAllTables() {
  console.log('🔍 Verifying All Tables - JJ Jewellers Tiruppur System...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Check all tables exist
    console.log('🏗️  Step 1: Verifying all tables exist...')
    
    const [tables] = await connection.execute(`
      SELECT table_name, table_rows, data_length
      FROM information_schema.tables 
      WHERE table_schema = ? 
      ORDER BY table_name
    `, [dbConfig.database])

    console.log('📋 All Database Tables:')
    console.log('=' .repeat(60))
    let totalRows = 0
    ;(tables as any[]).forEach(table => {
      const rows = table.table_rows || 0
      totalRows += rows
      console.log(`   ${table.table_name} | ${rows} rows`)
    })
    console.log('=' .repeat(60))
    console.log(`   Total Tables: ${(tables as any[]).length} | Total Rows: ${totalRows}`)

    // Step 2: Check specific table data
    console.log('\n📊 Step 2: Checking table data counts...')
    
    const tableChecks = [
      'users', 'customers', 'inventory', 'categories', 'metal_rates', 'exchange_rates',
      'business_settings', 'system_settings', 'sales', 'sale_items', 'exchange_transactions',
      'exchange_items', 'suppliers', 'bill_sequences', 'purchases', 'purchase_items',
      'schemes', 'scheme_payments', 'repairs'
    ]

    const tableData = {}
    for (const tableName of tableChecks) {
      try {
        const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`)
        tableData[tableName] = (count as any[])[0].count
        console.log(`   ✅ ${tableName}: ${tableData[tableName]} records`)
      } catch (error) {
        tableData[tableName] = 'ERROR'
        console.log(`   ❌ ${tableName}: Table missing or error`)
      }
    }

    // Step 3: Test API endpoints that were failing
    console.log('\n🧪 Step 3: Testing previously failing endpoints...')
    
    const endpointTests = [
      { name: 'purchases', query: 'SELECT * FROM purchases ORDER BY created_at DESC LIMIT 5' },
      { name: 'purchase_items', query: 'SELECT * FROM purchase_items LIMIT 5' },
      { name: 'schemes', query: 'SELECT * FROM schemes ORDER BY created_at DESC LIMIT 5' },
      { name: 'repairs', query: 'SELECT * FROM repairs ORDER BY created_at DESC LIMIT 5' }
    ]

    for (const test of endpointTests) {
      try {
        const [results] = await connection.execute(test.query)
        console.log(`   ✅ ${test.name} endpoint: ${(results as any[]).length} records retrieved`)
      } catch (error) {
        console.log(`   ❌ ${test.name} endpoint: ${error}`)
      }
    }

    // Step 4: Check data relationships
    console.log('\n🔗 Step 4: Checking data relationships...')
    
    try {
      const [salesWithItems] = await connection.execute(`
        SELECT s.invoice_number, COUNT(si.id) as item_count
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        GROUP BY s.id, s.invoice_number
        LIMIT 3
      `)
      console.log(`   ✅ Sales-Items relationship: ${(salesWithItems as any[]).length} sales with items`)
    } catch (error) {
      console.log(`   ❌ Sales-Items relationship: ${error}`)
    }

    try {
      const [purchasesWithItems] = await connection.execute(`
        SELECT p.purchase_number, COUNT(pi.id) as item_count
        FROM purchases p
        LEFT JOIN purchase_items pi ON p.id = pi.purchase_id
        GROUP BY p.id, p.purchase_number
        LIMIT 3
      `)
      console.log(`   ✅ Purchases-Items relationship: ${(purchasesWithItems as any[]).length} purchases with items`)
    } catch (error) {
      console.log(`   ❌ Purchases-Items relationship: ${error}`)
    }

    // Step 5: Generate comprehensive system status
    console.log('\n📈 Step 5: Comprehensive system status...')
    
    const systemStatus = {
      database: dbConfig.database,
      total_tables: (tables as any[]).length,
      total_records: totalRows,
      core_tables: {
        users: tableData['users'] || 0,
        customers: tableData['customers'] || 0,
        inventory: tableData['inventory'] || 0,
        sales: tableData['sales'] || 0,
        purchases: tableData['purchases'] || 0,
        schemes: tableData['schemes'] || 0,
        repairs: tableData['repairs'] || 0
      },
      supporting_tables: {
        categories: tableData['categories'] || 0,
        metal_rates: tableData['metal_rates'] || 0,
        suppliers: tableData['suppliers'] || 0,
        bill_sequences: tableData['bill_sequences'] || 0
      },
      transaction_tables: {
        sale_items: tableData['sale_items'] || 0,
        purchase_items: tableData['purchase_items'] || 0,
        exchange_transactions: tableData['exchange_transactions'] || 0,
        exchange_items: tableData['exchange_items'] || 0,
        scheme_payments: tableData['scheme_payments'] || 0
      }
    }

    console.log('\n🎯 FINAL SYSTEM STATUS REPORT:')
    console.log('=' .repeat(80))
    console.log(`🗄️  Database: ${systemStatus.database}`)
    console.log(`📋 Total Tables: ${systemStatus.total_tables}`)
    console.log(`📊 Total Records: ${systemStatus.total_records}`)
    console.log('')
    console.log('📦 Core Business Tables:')
    Object.entries(systemStatus.core_tables).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`)
    })
    console.log('')
    console.log('🛠️  Supporting Tables:')
    Object.entries(systemStatus.supporting_tables).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`)
    })
    console.log('')
    console.log('🔄 Transaction Tables:')
    Object.entries(systemStatus.transaction_tables).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`)
    })
    console.log('=' .repeat(80))

    // Check if all critical tables have data
    const criticalTables = ['users', 'customers', 'inventory', 'categories', 'metal_rates', 'purchases']
    const missingData = criticalTables.filter(table => !tableData[table] || tableData[table] === 0)
    
    if (missingData.length === 0) {
      console.log('🟢 STATUS: ALL CRITICAL TABLES HAVE DATA')
      console.log('✅ System is fully operational and ready for use!')
    } else {
      console.log('🟡 STATUS: SOME TABLES NEED DATA')
      console.log(`⚠️  Tables needing data: ${missingData.join(', ')}`)
    }

    console.log('\n🎉 Complete System Verification Completed!')

  } catch (error) {
    console.error('\n❌ System verification failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the complete system verification
verifyAllTables().catch(console.error)
