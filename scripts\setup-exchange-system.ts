#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function setupExchangeSystem() {
  console.log('🚀 Setting up Exchange System Database...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  console.log('Database config:')
  console.log('Host:', dbConfig.host)
  console.log('User:', dbConfig.user)
  console.log('Database:', dbConfig.database)
  console.log()

  let connection: mysql.Connection | null = null

  try {
    // Connect to MySQL server first
    console.log('1. Connecting to MySQL server...')
    const serverConnection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
    })
    console.log('✅ Connected to MySQL server')

    // Create database if it doesn't exist
    console.log('2. Creating database if not exists...')
    await serverConnection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``)
    console.log(`✅ Database '${dbConfig.database}' ready`)
    await serverConnection.end()

    // Connect to the specific database
    console.log('3. Connecting to the database...')
    connection = await mysql.createConnection(dbConfig)
    console.log(`✅ Connected to database '${dbConfig.database}'`)

    // Create exchange system tables
    console.log('4. Creating exchange system tables...')
    
    // Exchange rates table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver') NOT NULL,
        purity VARCHAR(10) NOT NULL,
        rate_per_gram DECIMAL(10, 2) NOT NULL,
        effective_date DATE NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_active_rate (metal_type, purity, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date)
      )
    `)
    console.log('✅ Exchange rates table created')

    // Exchange transactions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_transactions (
        id VARCHAR(36) PRIMARY KEY,
        transaction_number VARCHAR(50) UNIQUE NOT NULL,
        customer_id VARCHAR(36),
        transaction_date DATE NOT NULL,
        total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
        notes TEXT,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        purchase_bill_generated BOOLEAN DEFAULT FALSE,
        purchase_bill_id VARCHAR(36),
        purchase_bill_number VARCHAR(50),
        purchase_bill_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_customer (customer_id),
        INDEX idx_transaction_date (transaction_date),
        INDEX idx_status (status),
        INDEX idx_transaction_number (transaction_number),
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
      )
    `)
    console.log('✅ Exchange transactions table created')

    // Exchange items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_items (
        id VARCHAR(36) PRIMARY KEY,
        transaction_id VARCHAR(36) NOT NULL,
        item_description VARCHAR(255) NOT NULL,
        metal_type ENUM('gold', 'silver') NOT NULL,
        purity VARCHAR(10) NOT NULL,
        gross_weight DECIMAL(8, 3) NOT NULL,
        stone_weight DECIMAL(8, 3) DEFAULT 0.000,
        net_weight DECIMAL(8, 3) NOT NULL,
        rate_per_gram DECIMAL(10, 2) NOT NULL,
        amount DECIMAL(12, 2) NOT NULL,
        item_condition ENUM('good', 'fair', 'poor') DEFAULT 'good',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_transaction (transaction_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity (purity),
        FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE
      )
    `)
    console.log('✅ Exchange items table created')

    // Bill sequences table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS bill_sequences (
        id VARCHAR(36) PRIMARY KEY,
        sequence_type VARCHAR(50) NOT NULL,
        prefix VARCHAR(10) NOT NULL,
        current_number INT NOT NULL DEFAULT 0,
        financial_year VARCHAR(10) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_sequence (sequence_type, financial_year)
      )
    `)
    console.log('✅ Bill sequences table created')

    // Exchange purchase bills table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_purchase_bills (
        id VARCHAR(36) PRIMARY KEY,
        bill_number VARCHAR(50) UNIQUE NOT NULL,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        customer_id VARCHAR(36),
        bill_date DATE NOT NULL,
        total_amount DECIMAL(12, 2) NOT NULL,
        cgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        sgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
        total_with_tax DECIMAL(12, 2) NOT NULL,
        payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
        payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_bill_number (bill_number),
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_customer (customer_id),
        INDEX idx_bill_date (bill_date),
        INDEX idx_payment_status (payment_status),
        FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
      )
    `)
    console.log('✅ Exchange purchase bills table created')

    // Sales exchange items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sales_exchange_items (
        id VARCHAR(36) PRIMARY KEY,
        sale_id VARCHAR(36) NOT NULL,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        exchange_item_id VARCHAR(36) NOT NULL,
        deduction_amount DECIMAL(12, 2) NOT NULL,
        applied_rate DECIMAL(10, 2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_sale (sale_id),
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_exchange_item (exchange_item_id),
        FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
        FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
        FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) ON DELETE CASCADE
      )
    `)
    console.log('✅ Sales exchange items table created')

    // Exchange audit trail table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_audit_trail (
        id VARCHAR(36) PRIMARY KEY,
        exchange_transaction_id VARCHAR(36) NOT NULL,
        action_type ENUM('created', 'updated', 'billed', 'voucher_generated', 'used_in_sale', 'cancelled') NOT NULL,
        action_description TEXT NOT NULL,
        old_values JSON,
        new_values JSON,
        related_bill_id VARCHAR(36),
        related_sale_id VARCHAR(36),
        performed_by VARCHAR(36),
        performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_exchange_transaction (exchange_transaction_id),
        INDEX idx_action_type (action_type),
        INDEX idx_performed_at (performed_at),
        INDEX idx_performed_by (performed_by),
        FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
        FOREIGN KEY (related_bill_id) REFERENCES exchange_purchase_bills(id) ON DELETE SET NULL,
        FOREIGN KEY (related_sale_id) REFERENCES sales(id) ON DELETE SET NULL
      )
    `)
    console.log('✅ Exchange audit trail table created')

    // Add foreign key constraint to link purchase bills back to transactions
    try {
      await connection.execute(`
        ALTER TABLE exchange_transactions 
        ADD CONSTRAINT fk_exchange_purchase_bill 
        FOREIGN KEY (purchase_bill_id) REFERENCES exchange_purchase_bills(id) ON DELETE SET NULL
      `)
    } catch (error) {
      // Constraint might already exist, ignore error
    }

    console.log('\n5. Inserting default exchange rates...')
    
    // Insert default exchange rates
    const rates = [
      { id: randomUUID(), metal_type: 'gold', purity: '24K', rate_per_gram: 6800.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '22K', rate_per_gram: 6200.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '18K', rate_per_gram: 5100.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '14K', rate_per_gram: 3950.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '999', rate_per_gram: 85.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '925', rate_per_gram: 78.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '900', rate_per_gram: 76.00 }
    ]

    for (const rate of rates) {
      await connection.execute(`
        INSERT IGNORE INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active) 
        VALUES (?, ?, ?, ?, CURDATE(), TRUE)
      `, [rate.id, rate.metal_type, rate.purity, rate.rate_per_gram])
    }
    console.log('✅ Default exchange rates inserted')

    // Insert bill sequence for current financial year
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
    
    await connection.execute(`
      INSERT IGNORE INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) 
      VALUES (?, 'exchange_purchase', 'EPB', 0, ?)
    `, [randomUUID(), financialYear])
    console.log('✅ Bill sequence initialized')

    console.log('\n6. Inserting comprehensive sample data...')
    await insertSampleData(connection)

    console.log('\n7. Verifying setup...')
    await verifySetup(connection)

    console.log('\n🎉 Exchange System setup completed successfully!')
    console.log('\n📋 Next Steps:')
    console.log('1. Start the application: npm run dev')
    console.log('2. Navigate to the Exchange tab')
    console.log('3. Try the Demo to see the complete workflow')
    console.log('4. Test exchange transactions and bill generation')
    console.log('5. Test sales with exchange integration\n')

  } catch (error) {
    console.error('\n❌ Setup failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('📡 Database connection closed')
    }
  }
}

async function insertSampleData(connection: mysql.Connection) {
  // Insert sample customers
  const customers = [
    { id: 'cust_001', name: 'Rajesh Kumar', phone: '9876543210', email: '<EMAIL>', address: '123 MG Road, Mumbai, Maharashtra 400001' },
    { id: 'cust_002', name: 'Priya Sharma', phone: '9876543211', email: '<EMAIL>', address: '456 Brigade Road, Bangalore, Karnataka 560001' },
    { id: 'cust_003', name: 'Amit Patel', phone: '9876543212', email: '<EMAIL>', address: '789 CG Road, Ahmedabad, Gujarat 380001' },
    { id: 'cust_004', name: 'Sunita Reddy', phone: '9876543213', email: '<EMAIL>', address: '321 Jubilee Hills, Hyderabad, Telangana 500001' },
    { id: 'cust_005', name: 'Vikram Singh', phone: '9876543214', email: '<EMAIL>', address: '654 Connaught Place, New Delhi, Delhi 110001' }
  ]

  for (const customer of customers) {
    await connection.execute(`
      INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, 150000.00, '2024-01-31', NOW(), NOW())
    `, [customer.id, customer.name, customer.phone, customer.email, customer.address])
  }
  console.log('✅ Sample customers inserted')

  // Insert sample inventory items
  const inventoryItems = [
    { id: 'inv_001', name: 'Gold Necklace Set Traditional', category: 'Necklace', metal_type: 'gold', gross_weight: 45.000, stone_weight: 5.000, net_weight: 40.000, stone_amount: 15000.00, purity: '22K', making_charges: 25000.00, current_value: 273000.00, stock: 5 },
    { id: 'inv_002', name: 'Gold Earrings Designer', category: 'Earrings', metal_type: 'gold', gross_weight: 12.000, stone_weight: 2.000, net_weight: 10.000, stone_amount: 8000.00, purity: '22K', making_charges: 8000.00, current_value: 78000.00, stock: 8 },
    { id: 'inv_003', name: 'Silver Bangles Set', category: 'Bangles', metal_type: 'silver', gross_weight: 150.000, stone_weight: 0.000, net_weight: 150.000, stone_amount: 0.00, purity: '925', making_charges: 5000.00, current_value: 16700.00, stock: 12 },
    { id: 'inv_004', name: 'Gold Ring Wedding', category: 'Ring', metal_type: 'gold', gross_weight: 8.000, stone_weight: 1.000, net_weight: 7.000, stone_amount: 5000.00, purity: '18K', making_charges: 3000.00, current_value: 43700.00, stock: 15 },
    { id: 'inv_005', name: 'Gold Chain Fancy', category: 'Chain', metal_type: 'gold', gross_weight: 25.000, stone_weight: 0.000, net_weight: 25.000, stone_amount: 0.00, purity: '22K', making_charges: 15000.00, current_value: 170000.00, stock: 6 }
  ]

  for (const item of inventoryItems) {
    await connection.execute(`
      INSERT IGNORE INTO inventory (id, name, category, metal_type, gross_weight, stone_weight, net_weight, stone_amount, purity, making_charges, current_value, stock, stone_details, description, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Various stones', 'High quality jewelry item', NOW(), NOW())
    `, [item.id, item.name, item.category, item.metal_type, item.gross_weight, item.stone_weight, item.net_weight, item.stone_amount, item.purity, item.making_charges, item.current_value, item.stock])
  }
  console.log('✅ Sample inventory items inserted')

  // Insert sample exchange transactions with realistic data
  const exchangeTransactions = [
    { id: 'exg_001', transaction_number: 'EXG-20240131-001', customer_id: 'cust_001', transaction_date: '2024-01-31', status: 'completed', purchase_bill_generated: true },
    { id: 'exg_002', transaction_number: 'EXG-20240130-001', customer_id: 'cust_002', transaction_date: '2024-01-30', status: 'completed', purchase_bill_generated: true },
    { id: 'exg_003', transaction_number: 'EXG-20240129-001', customer_id: 'cust_003', transaction_date: '2024-01-29', status: 'completed', purchase_bill_generated: false },
    { id: 'exg_004', transaction_number: 'EXG-20240128-001', customer_id: 'cust_004', transaction_date: '2024-01-28', status: 'completed', purchase_bill_generated: true },
    { id: 'exg_005', transaction_number: 'EXG-20240127-001', customer_id: 'cust_005', transaction_date: '2024-01-27', status: 'pending', purchase_bill_generated: false }
  ]

  for (const transaction of exchangeTransactions) {
    await connection.execute(`
      INSERT IGNORE INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, purchase_bill_generated, created_at, updated_at) 
      VALUES (?, ?, ?, ?, 0.00, 'cash', 'Sample exchange transaction', ?, ?, NOW(), NOW())
    `, [transaction.id, transaction.transaction_number, transaction.customer_id, transaction.transaction_date, transaction.status, transaction.purchase_bill_generated])
  }
  console.log('✅ Sample exchange transactions inserted')

  // Insert sample exchange items
  const exchangeItems = [
    { id: 'item_001', transaction_id: 'exg_001', item_description: 'GOLD OLD BAR', metal_type: 'gold', purity: '22K', gross_weight: 10.000, stone_weight: 2.500, net_weight: 7.500, rate_per_gram: 6200.00, amount: 46500.00 },
    { id: 'item_002', transaction_id: 'exg_002', item_description: 'OLD SILVER BANGLES', metal_type: 'silver', purity: '925', gross_weight: 220.000, stone_weight: 20.000, net_weight: 200.000, rate_per_gram: 78.00, amount: 15600.00 },
    { id: 'item_003', transaction_id: 'exg_003', item_description: 'GOLD CHAIN OLD', metal_type: 'gold', purity: '18K', gross_weight: 15.000, stone_weight: 3.000, net_weight: 12.000, rate_per_gram: 5100.00, amount: 61200.00 },
    { id: 'item_004', transaction_id: 'exg_004', item_description: 'GOLD EARRINGS PAIR', metal_type: 'gold', purity: '22K', gross_weight: 8.000, stone_weight: 3.000, net_weight: 5.000, rate_per_gram: 6200.00, amount: 31000.00 },
    { id: 'item_005', transaction_id: 'exg_005', item_description: 'GOLD NECKLACE HEAVY', metal_type: 'gold', purity: '22K', gross_weight: 25.000, stone_weight: 5.000, net_weight: 20.000, rate_per_gram: 6200.00, amount: 124000.00 }
  ]

  for (const item of exchangeItems) {
    await connection.execute(`
      INSERT IGNORE INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'good', 'Sample exchange item', NOW(), NOW())
    `, [item.id, item.transaction_id, item.item_description, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.rate_per_gram, item.amount])
  }
  console.log('✅ Sample exchange items inserted')

  // Update transaction totals
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 46500.00 WHERE id = 'exg_001'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 15600.00 WHERE id = 'exg_002'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 61200.00 WHERE id = 'exg_003'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 31000.00 WHERE id = 'exg_004'`)
  await connection.execute(`UPDATE exchange_transactions SET total_amount = 124000.00 WHERE id = 'exg_005'`)

  // Insert sample purchase bills
  const purchaseBills = [
    { id: 'bill_001', bill_number: 'EPB/2024-25/0001', exchange_transaction_id: 'exg_001', customer_id: 'cust_001', total_amount: 46500.00, cgst_amount: 697.50, sgst_amount: 697.50, total_with_tax: 47895.00 },
    { id: 'bill_002', bill_number: 'EPB/2024-25/0002', exchange_transaction_id: 'exg_002', customer_id: 'cust_002', total_amount: 15600.00, cgst_amount: 234.00, sgst_amount: 234.00, total_with_tax: 16068.00 },
    { id: 'bill_003', bill_number: 'EPB/2024-25/0003', exchange_transaction_id: 'exg_004', customer_id: 'cust_004', total_amount: 31000.00, cgst_amount: 465.00, sgst_amount: 465.00, total_with_tax: 31930.00 }
  ]

  for (const bill of purchaseBills) {
    await connection.execute(`
      INSERT IGNORE INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) 
      VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, ?, 'cash', 'paid', 'Sample purchase bill', NOW(), NOW())
    `, [bill.id, bill.bill_number, bill.exchange_transaction_id, bill.customer_id, bill.total_amount, bill.cgst_amount, bill.sgst_amount, bill.total_with_tax])
  }
  console.log('✅ Sample purchase bills inserted')

  // Update transactions with bill references
  await connection.execute(`UPDATE exchange_transactions SET purchase_bill_id = 'bill_001', purchase_bill_number = 'EPB/2024-25/0001', purchase_bill_date = CURDATE() WHERE id = 'exg_001'`)
  await connection.execute(`UPDATE exchange_transactions SET purchase_bill_id = 'bill_002', purchase_bill_number = 'EPB/2024-25/0002', purchase_bill_date = CURDATE() WHERE id = 'exg_002'`)
  await connection.execute(`UPDATE exchange_transactions SET purchase_bill_id = 'bill_003', purchase_bill_number = 'EPB/2024-25/0003', purchase_bill_date = CURDATE() WHERE id = 'exg_004'`)

  // Update bill sequence
  await connection.execute(`UPDATE bill_sequences SET current_number = 3 WHERE sequence_type = 'exchange_purchase'`)

  console.log('✅ All sample data inserted successfully')
}

async function verifySetup(connection: mysql.Connection) {
  const tables = ['exchange_rates', 'exchange_transactions', 'exchange_items', 'exchange_purchase_bills', 'sales_exchange_items', 'exchange_audit_trail', 'bill_sequences']
  
  for (const table of tables) {
    const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
    const count = (rows as any)[0].count
    console.log(`✅ Table '${table}': ${count} records`)
  }
}

// Run the setup
setupExchangeSystem().catch(console.error)
