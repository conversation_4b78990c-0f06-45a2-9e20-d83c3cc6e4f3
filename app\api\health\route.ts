import { NextResponse } from 'next/server'
import { getPool } from '@/lib/database/config'

export async function GET() {
  try {
    // Test database connection
    const pool = getPool()
    const connection = await pool.getConnection()
    
    // Test a simple query
    const [result] = await connection.execute('SELECT 1 as test')
    connection.release()
    
    // Get basic stats
    const [salesCount] = await connection.execute('SELECT COUNT(*) as count FROM sales')
    const [customersCount] = await connection.execute('SELECT COUNT(*) as count FROM customers')
    const [inventoryCount] = await connection.execute('SELECT COUNT(*) as count FROM inventory')
    
    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
      stats: {
        sales: (salesCount as any[])[0]?.count || 0,
        customers: (customersCount as any[])[0]?.count || 0,
        inventory: (inventoryCount as any[])[0]?.count || 0
      }
    })
  } catch (error) {
    console.error('Health check failed:', error)
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        database: 'disconnected',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
