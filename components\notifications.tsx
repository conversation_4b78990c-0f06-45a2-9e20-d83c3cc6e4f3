"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { AlertTriangle, Clock, TrendingDown, Package, Bell } from "lucide-react"
import { useStore } from "@/lib/store"

export function Notifications() {
  const { inventory, repairs, schemes, customers, settings } = useStore()

  // Calculate notifications
  const lowStockItems = inventory.filter((item) => item.stock <= Number.parseInt(settings.lowStockThreshold))
  const pendingRepairs = repairs.filter((repair) => repair.status === "pending")
  const overdueRepairs = repairs.filter((repair) => {
    const promisedDate = new Date(repair.promisedDate)
    const today = new Date()
    return repair.status !== "delivered" && promisedDate < today
  })
  const upcomingSchemePayments = schemes.filter((scheme) => {
    if (scheme.status !== "active") return false
    // Simple logic: if paid amount is less than expected for current month
    const monthsElapsed = Math.floor((Date.now() - new Date(scheme.startDate).getTime()) / (30 * 24 * 60 * 60 * 1000))
    const expectedPaid = monthsElapsed * scheme.monthlyAmount
    return scheme.paidAmount < expectedPaid
  })

  const notifications = [
    ...(lowStockItems.length > 0
      ? [
          {
            id: "low-stock",
            type: "warning" as const,
            icon: Package,
            title: "Low Stock Alert",
            message: `${lowStockItems.length} items are running low on stock`,
            count: lowStockItems.length,
            action: "View Items",
            actionFn: () => console.log("Navigate to inventory"),
          },
        ]
      : []),

    ...(pendingRepairs.length > 0
      ? [
          {
            id: "pending-repairs",
            type: "info" as const,
            icon: Clock,
            title: "Pending Repairs",
            message: `${pendingRepairs.length} repair orders are pending`,
            count: pendingRepairs.length,
            action: "View Orders",
            actionFn: () => console.log("Navigate to repairs"),
          },
        ]
      : []),

    ...(overdueRepairs.length > 0
      ? [
          {
            id: "overdue-repairs",
            type: "error" as const,
            icon: AlertTriangle,
            title: "Overdue Repairs",
            message: `${overdueRepairs.length} repairs are past their promised date`,
            count: overdueRepairs.length,
            action: "View Overdue",
            actionFn: () => console.log("Navigate to overdue repairs"),
          },
        ]
      : []),

    ...(upcomingSchemePayments.length > 0
      ? [
          {
            id: "scheme-payments",
            type: "info" as const,
            icon: TrendingDown,
            title: "Scheme Payment Reminders",
            message: `${upcomingSchemePayments.length} customers have pending scheme payments`,
            count: upcomingSchemePayments.length,
            action: "Send Reminders",
            actionFn: () => console.log("Send payment reminders"),
          },
        ]
      : []),
  ]

  if (notifications.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </CardTitle>
          <CardDescription>Stay updated with important alerts and reminders</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">All caught up! No notifications at the moment.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notifications
          <Badge variant="secondary">{notifications.length}</Badge>
        </CardTitle>
        <CardDescription>Stay updated with important alerts and reminders</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`flex items-start gap-3 p-4 rounded-lg border ${
                notification.type === "error"
                  ? "border-red-200 bg-red-50"
                  : notification.type === "warning"
                    ? "border-amber-200 bg-amber-50"
                    : "border-blue-200 bg-blue-50"
              }`}
            >
              <div
                className={`p-2 rounded-full ${
                  notification.type === "error"
                    ? "bg-red-100 text-red-600"
                    : notification.type === "warning"
                      ? "bg-amber-100 text-amber-600"
                      : "bg-blue-100 text-blue-600"
                }`}
              >
                <notification.icon className="h-4 w-4" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm">{notification.title}</h4>
                  <Badge
                    variant={
                      notification.type === "error"
                        ? "destructive"
                        : notification.type === "warning"
                          ? "secondary"
                          : "default"
                    }
                  >
                    {notification.count}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={notification.actionFn}
                  className="h-7 text-xs bg-transparent"
                >
                  {notification.action}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
