#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function resetAndSeedSimple() {
  console.log('🔄 Resetting Database with Fresh Sample Data...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Clean existing exchange data
    console.log('🧹 Step 1: Cleaning existing exchange data...')
    
    // Disable foreign key checks temporarily for cleanup
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    
    // Clean exchange data
    const cleanupQueries = [
      'DELETE FROM exchange_audit_trail',
      'DELETE FROM sales_exchange_items', 
      'DELETE FROM exchange_purchase_bills',
      'DELETE FROM exchange_items',
      'DELETE FROM exchange_transactions',
      'DELETE FROM exchange_rates',
      'DELETE FROM bill_sequences'
    ]

    for (const query of cleanupQueries) {
      try {
        await connection.execute(query)
        console.log(`   ✅ ${query}`)
      } catch (error) {
        console.log(`   ⚠️  ${query} - ${error}`)
      }
    }

    // Re-enable foreign key checks
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
    console.log('✅ Exchange data cleaned\n')

    // Step 2: Insert fresh exchange rates
    console.log('💰 Step 2: Inserting current exchange rates...')
    const rates = [
      { id: randomUUID(), metal_type: 'gold', purity: '24K', rate_per_gram: 7200.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '22K', rate_per_gram: 6600.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '18K', rate_per_gram: 5400.00 },
      { id: randomUUID(), metal_type: 'gold', purity: '14K', rate_per_gram: 4200.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '999', rate_per_gram: 90.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '925', rate_per_gram: 83.00 },
      { id: randomUUID(), metal_type: 'silver', purity: '900', rate_per_gram: 81.00 }
    ]

    for (const rate of rates) {
      await connection.execute(`
        INSERT INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active) 
        VALUES (?, ?, ?, ?, CURDATE(), TRUE)
      `, [rate.id, rate.metal_type, rate.purity, rate.rate_per_gram])
    }
    console.log(`   ✅ Inserted ${rates.length} current exchange rates`)

    // Step 3: Initialize bill sequence
    console.log('🔢 Step 3: Initializing bill sequence...')
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
    
    await connection.execute(`
      INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) 
      VALUES (?, 'exchange_purchase', 'EPB', 0, ?)
    `, [randomUUID(), financialYear])
    console.log('   ✅ Bill sequence initialized')

    // Step 4: Ensure sample customers exist
    console.log('👥 Step 4: Ensuring sample customers exist...')
    const customers = [
      { id: 'cust_001', name: 'Rajesh Kumar', phone: '9876543210', email: '<EMAIL>', address: '123 MG Road, Mumbai, Maharashtra 400001' },
      { id: 'cust_002', name: 'Priya Sharma', phone: '9876543211', email: '<EMAIL>', address: '456 Brigade Road, Bangalore, Karnataka 560001' },
      { id: 'cust_003', name: 'Amit Patel', phone: '9876543212', email: '<EMAIL>', address: '789 CG Road, Ahmedabad, Gujarat 380001' },
      { id: 'cust_004', name: 'Sunita Reddy', phone: '9876543213', email: '<EMAIL>', address: '321 Jubilee Hills, Hyderabad, Telangana 500001' },
      { id: 'cust_005', name: 'Vikram Singh', phone: '9876543214', email: '<EMAIL>', address: '654 Connaught Place, New Delhi, Delhi 110001' },
      { id: 'cust_006', name: 'Meera Joshi', phone: '9876543215', email: '<EMAIL>', address: '987 FC Road, Pune, Maharashtra 411005' }
    ]

    for (const customer of customers) {
      await connection.execute(`
        INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, 200000.00, CURDATE(), NOW(), NOW())
      `, [customer.id, customer.name, customer.phone, customer.email, customer.address])
    }
    console.log(`   ✅ Ensured ${customers.length} sample customers exist`)

    // Step 5: Create diverse exchange transactions
    console.log('🔄 Step 5: Creating diverse exchange transactions...')
    const transactions = [
      {
        id: 'exg_001',
        transaction_number: 'EXG-20250131-001',
        customer_id: 'cust_001',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'High-value gold bar exchange - excellent condition'
      },
      {
        id: 'exg_002', 
        transaction_number: 'EXG-20250130-001',
        customer_id: 'cust_002',
        transaction_date: '2025-01-30',
        status: 'completed',
        notes: 'Silver jewelry set exchange - traditional design'
      },
      {
        id: 'exg_003',
        transaction_number: 'EXG-20250129-001', 
        customer_id: 'cust_003',
        transaction_date: '2025-01-29',
        status: 'completed',
        notes: 'Mixed gold jewelry exchange - various purities'
      },
      {
        id: 'exg_004',
        transaction_number: 'EXG-20250128-001',
        customer_id: 'cust_004', 
        transaction_date: '2025-01-28',
        status: 'pending',
        notes: 'Pending evaluation - customer to return tomorrow'
      },
      {
        id: 'exg_005',
        transaction_number: 'EXG-20250127-001',
        customer_id: 'cust_005',
        transaction_date: '2025-01-27', 
        status: 'completed',
        notes: 'Antique gold necklace - heritage piece'
      },
      {
        id: 'exg_006',
        transaction_number: 'EXG-20250126-001',
        customer_id: 'cust_006',
        transaction_date: '2025-01-26',
        status: 'completed',
        notes: 'Modern silver bangles set - contemporary design'
      }
    ]

    for (const transaction of transactions) {
      await connection.execute(`
        INSERT INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, created_at, updated_at) 
        VALUES (?, ?, ?, ?, 0.00, 'cash', ?, ?, NOW(), NOW())
      `, [transaction.id, transaction.transaction_number, transaction.customer_id, transaction.transaction_date, transaction.notes, transaction.status])
    }
    console.log(`   ✅ Created ${transactions.length} exchange transactions`)

    // Step 6: Add diverse exchange items
    console.log('💎 Step 6: Adding diverse exchange items...')
    const items = [
      // Transaction 1: High-value gold bar
      { id: randomUUID(), transaction_id: 'exg_001', description: 'GOLD BAR 24K PURE', metal_type: 'gold', purity: '24K', gross_weight: 50.000, stone_weight: 0.000, net_weight: 50.000, rate_per_gram: 7200.00, amount: 360000.00, condition: 'good' },
      
      // Transaction 2: Silver jewelry set
      { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER BANGLES SET TRADITIONAL', metal_type: 'silver', purity: '925', gross_weight: 180.000, stone_weight: 0.000, net_weight: 180.000, rate_per_gram: 83.00, amount: 14940.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER ANKLETS PAIR', metal_type: 'silver', purity: '925', gross_weight: 120.000, stone_weight: 0.000, net_weight: 120.000, rate_per_gram: 83.00, amount: 9960.00, condition: 'fair' },
      
      // Transaction 3: Mixed gold jewelry
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD CHAIN 22K HEAVY', metal_type: 'gold', purity: '22K', gross_weight: 25.000, stone_weight: 0.000, net_weight: 25.000, rate_per_gram: 6600.00, amount: 165000.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD EARRINGS 18K DESIGNER', metal_type: 'gold', purity: '18K', gross_weight: 12.000, stone_weight: 4.000, net_weight: 8.000, rate_per_gram: 5400.00, amount: 43200.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD RING 14K WEDDING', metal_type: 'gold', purity: '14K', gross_weight: 8.000, stone_weight: 2.000, net_weight: 6.000, rate_per_gram: 4200.00, amount: 25200.00, condition: 'fair' },
      
      // Transaction 4: Pending evaluation
      { id: randomUUID(), transaction_id: 'exg_004', description: 'GOLD NECKLACE SET EVALUATION PENDING', metal_type: 'gold', purity: '22K', gross_weight: 35.000, stone_weight: 8.000, net_weight: 27.000, rate_per_gram: 6600.00, amount: 178200.00, condition: 'good' },
      
      // Transaction 5: Antique piece
      { id: randomUUID(), transaction_id: 'exg_005', description: 'ANTIQUE GOLD NECKLACE HERITAGE', metal_type: 'gold', purity: '22K', gross_weight: 45.000, stone_weight: 12.000, net_weight: 33.000, rate_per_gram: 6600.00, amount: 217800.00, condition: 'good' },
      
      // Transaction 6: Modern silver
      { id: randomUUID(), transaction_id: 'exg_006', description: 'MODERN SILVER BANGLES CONTEMPORARY', metal_type: 'silver', purity: '925', gross_weight: 150.000, stone_weight: 0.000, net_weight: 150.000, rate_per_gram: 83.00, amount: 12450.00, condition: 'good' }
    ]

    for (const item of items) {
      await connection.execute(`
        INSERT INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Fresh sample data', NOW(), NOW())
      `, [item.id, item.transaction_id, item.description, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.rate_per_gram, item.amount, item.condition])
    }
    console.log(`   ✅ Added ${items.length} diverse exchange items`)

    // Step 7: Update transaction totals
    console.log('🔢 Step 7: Updating transaction totals...')
    const totals = [
      { id: 'exg_001', total: 360000.00 },
      { id: 'exg_002', total: 24900.00 },
      { id: 'exg_003', total: 233400.00 },
      { id: 'exg_004', total: 178200.00 },
      { id: 'exg_005', total: 217800.00 },
      { id: 'exg_006', total: 12450.00 }
    ]

    for (const total of totals) {
      await connection.execute(`UPDATE exchange_transactions SET total_amount = ? WHERE id = ?`, [total.total, total.id])
    }
    console.log('   ✅ Updated all transaction totals')

    // Step 8: Generate purchase bills for completed transactions
    console.log('📄 Step 8: Generating purchase bills...')
    const completedTransactions = [
      { id: 'exg_001', customer_id: 'cust_001', amount: 360000.00 },
      { id: 'exg_002', customer_id: 'cust_002', amount: 24900.00 },
      { id: 'exg_003', customer_id: 'cust_003', amount: 233400.00 },
      { id: 'exg_005', customer_id: 'cust_005', amount: 217800.00 },
      { id: 'exg_006', customer_id: 'cust_006', amount: 12450.00 }
    ]
    
    for (let i = 0; i < completedTransactions.length; i++) {
      const transaction = completedTransactions[i]
      const billId = randomUUID()
      const billNumber = `EPB/${financialYear}/${String(i + 1).padStart(4, '0')}`
      const cgstAmount = (transaction.amount * 1.5) / 100
      const sgstAmount = (transaction.amount * 1.5) / 100
      const totalWithTax = transaction.amount + cgstAmount + sgstAmount
      
      await connection.execute(`
        INSERT INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) 
        VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, ?, 'cash', 'paid', 'Fresh sample bill', NOW(), NOW())
      `, [billId, billNumber, transaction.id, transaction.customer_id, transaction.amount, cgstAmount, sgstAmount, totalWithTax])
      
      // Update transaction with bill reference
      await connection.execute(`
        UPDATE exchange_transactions 
        SET purchase_bill_generated = TRUE, purchase_bill_id = ?, purchase_bill_number = ?, purchase_bill_date = CURDATE() 
        WHERE id = ?
      `, [billId, billNumber, transaction.id])
    }
    
    // Update bill sequence
    await connection.execute(`UPDATE bill_sequences SET current_number = ? WHERE sequence_type = 'exchange_purchase'`, [completedTransactions.length])
    console.log(`   ✅ Generated ${completedTransactions.length} purchase bills`)

    // Step 9: Create audit trail
    console.log('📋 Step 9: Creating audit trail...')
    const auditEntries = [
      { id: randomUUID(), transaction_id: 'exg_001', action: 'created', description: 'High-value gold bar exchange transaction created', values: '{"totalAmount": 360000, "items": 1}' },
      { id: randomUUID(), transaction_id: 'exg_001', action: 'billed', description: 'Purchase bill EPB/2025-26/0001 generated', values: '{"billNumber": "EPB/2025-26/0001", "totalWithTax": 370800}' },
      { id: randomUUID(), transaction_id: 'exg_002', action: 'created', description: 'Silver jewelry set exchange created', values: '{"totalAmount": 24900, "items": 2}' },
      { id: randomUUID(), transaction_id: 'exg_002', action: 'billed', description: 'Purchase bill EPB/2025-26/0002 generated', values: '{"billNumber": "EPB/2025-26/0002", "totalWithTax": 25647}' },
      { id: randomUUID(), transaction_id: 'exg_003', action: 'created', description: 'Mixed gold jewelry exchange created', values: '{"totalAmount": 233400, "items": 3}' },
      { id: randomUUID(), transaction_id: 'exg_003', action: 'billed', description: 'Purchase bill EPB/2025-26/0003 generated', values: '{"billNumber": "EPB/2025-26/0003", "totalWithTax": 240402}' },
      { id: randomUUID(), transaction_id: 'exg_004', action: 'created', description: 'Pending evaluation transaction created', values: '{"totalAmount": 178200, "items": 1, "status": "pending"}' },
      { id: randomUUID(), transaction_id: 'exg_005', action: 'created', description: 'Antique heritage necklace exchange created', values: '{"totalAmount": 217800, "items": 1}' },
      { id: randomUUID(), transaction_id: 'exg_005', action: 'billed', description: 'Purchase bill EPB/2025-26/0004 generated', values: '{"billNumber": "EPB/2025-26/0004", "totalWithTax": 224334}' },
      { id: randomUUID(), transaction_id: 'exg_006', action: 'created', description: 'Modern silver bangles exchange created', values: '{"totalAmount": 12450, "items": 1}' },
      { id: randomUUID(), transaction_id: 'exg_006', action: 'billed', description: 'Purchase bill EPB/2025-26/0005 generated', values: '{"billNumber": "EPB/2025-26/0005", "totalWithTax": 12823.5}' }
    ]

    for (const entry of auditEntries) {
      await connection.execute(`
        INSERT INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, performed_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
      `, [entry.id, entry.transaction_id, entry.action, entry.description, entry.values])
    }
    console.log(`   ✅ Created ${auditEntries.length} audit trail entries`)

    // Step 10: Verify setup
    console.log('\n🔍 Step 10: Verifying fresh database setup...')
    const tables = ['exchange_rates', 'exchange_transactions', 'exchange_items', 'exchange_purchase_bills', 'exchange_audit_trail', 'bill_sequences']
    
    for (const table of tables) {
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
      const count = (rows as any)[0].count
      console.log(`   ✅ ${table}: ${count} records`)
    }

    console.log('\n🎉 Database Reset and Fresh Seeding Completed Successfully!')

    console.log('\n📊 FRESH DATABASE STATUS:')
    console.log('=' .repeat(60))
    console.log('✅ Exchange Rates: 7 current rates (updated for 2025)')
    console.log('✅ Sample Customers: 6 diverse customers')
    console.log('✅ Exchange Transactions: 6 transactions (5 completed, 1 pending)')
    console.log('✅ Exchange Items: 9 diverse items (gold/silver, various purities)')
    console.log('✅ Purchase Bills: 5 bills with GST calculations')
    console.log('✅ Audit Trail: 11 comprehensive audit entries')
    console.log('✅ Bill Sequence: Properly initialized for 2025-26')
    console.log('✅ Data Integrity: All constraints active and validated')
    console.log('=' .repeat(60))

    console.log('\n🚀 READY FOR TESTING:')
    console.log('1. ✅ Fresh sample data with realistic scenarios')
    console.log('2. ✅ Updated exchange rates for current market')
    console.log('3. ✅ Diverse transaction types and statuses')
    console.log('4. ✅ Complete audit trail for compliance')
    console.log('5. ✅ Professional bill generation ready')
    console.log('6. ✅ All validation constraints active')

    console.log('\n📋 SAMPLE DATA HIGHLIGHTS:')
    console.log('💰 High-value transaction: ₹3,60,000 (Gold bar)')
    console.log('🥈 Silver jewelry sets: Traditional and modern designs')
    console.log('🏺 Antique heritage piece: ₹2,17,800 (Heritage necklace)')
    console.log('⏳ Pending evaluation: ₹1,78,200 (Customer to return)')
    console.log('💍 Mixed purities: 24K, 22K, 18K, 14K gold items')
    console.log('📄 Professional bills: EPB/2025-26/XXXX format')

  } catch (error) {
    console.error('\n❌ Database reset failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the reset and seed
resetAndSeedSimple().catch(console.error)
