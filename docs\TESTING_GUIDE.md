# Exchange System Testing Guide

## 🎯 Overview

This guide will walk you through testing the complete Exchange Billing Integration System, from database setup to full workflow testing.

## 📋 Prerequisites

Before testing, ensure you have:
- ✅ Node.js installed (v16 or higher)
- ✅ MySQL server running
- ✅ Application dependencies installed (`npm install`)
- ✅ Database credentials configured

## 🗄️ Database Setup

### Option 1: Automated Setup (Recommended)

#### Windows Users:
```bash
# Navigate to project directory
cd c:\proj\jewellers-software

# Run the setup script
scripts\setup-database.bat
```

#### Mac/Linux Users:
```bash
# Navigate to project directory
cd /path/to/jewellers-software

# Make script executable (if needed)
chmod +x scripts/setup-database.sh

# Run the setup script
./scripts/setup-database.sh
```

#### Using Node.js directly:
```bash
# Set database credentials (optional)
set DB_PASSWORD=your_mysql_password  # Windows
export DB_PASSWORD=your_mysql_password  # Mac/Linux

# Run setup script
node scripts/setup-database.js
```

### Option 2: Manual Setup

If automated setup fails, run SQL files manually:

1. **Open MySQL command line or phpMyAdmin**
2. **Create database** (if not exists):
   ```sql
   CREATE DATABASE jewellers_db;
   USE jewellers_db;
   ```
3. **Run migration scripts**:
   - Execute `scripts/migrations/001_create_exchange_tables.sql`
   - Execute `scripts/migrations/002_insert_sample_data.sql`

### Verify Database Setup

After setup, verify these tables exist:
- ✅ `exchange_rates` - Current exchange rates
- ✅ `exchange_transactions` - Main exchange records
- ✅ `exchange_items` - Individual items in exchanges
- ✅ `exchange_purchase_bills` - Purchase bill records
- ✅ `sales_exchange_items` - Links sales with exchanges
- ✅ `exchange_audit_trail` - Complete audit history
- ✅ `bill_sequences` - Sequential bill numbering

## 🚀 Start Testing

### 1. Launch the Application

```bash
# Start development server
npm run dev

# Application will be available at:
# http://localhost:3001 (or 3000 if available)
```

### 2. Access the Exchange System

1. **Open browser** and navigate to the application
2. **Login** (if authentication is enabled)
3. **Click "Exchange" tab** in the sidebar
4. You should see the Exchange page with tabs: Demo, Dashboard, Transactions, Rates, Reports

## 🎬 Test 1: Interactive Demo

**Purpose**: Understand the complete workflow

### Steps:
1. **Navigate to "Demo" tab**
2. **Click "Play Demo" button**
3. **Watch the 5-step process**:
   - Step 1: Customer brings old gold
   - Step 2: Exchange transaction created
   - Step 3: Purchase bill generated
   - Step 4: Customer makes new purchase
   - Step 5: Exchange applied to sale

### Expected Results:
- ✅ Demo plays automatically with 2-second intervals
- ✅ Each step shows detailed information
- ✅ Visual progress indicator updates
- ✅ Final step shows customer savings calculation

## 📊 Test 2: Dashboard Overview

**Purpose**: View transaction lifecycle and statistics

### Steps:
1. **Navigate to "Dashboard" tab**
2. **Review summary cards**:
   - Total Exchanges
   - Total Value
   - Bills Pending
   - Fully Utilized
   - Available Value
3. **Examine transaction lifecycle view**
4. **Click "View Details" on any transaction**

### Expected Results:
- ✅ Summary cards show correct statistics
- ✅ Transaction flow shows progress steps
- ✅ Utilization progress bars display correctly
- ✅ Detail dialog shows comprehensive information

## 📝 Test 3: Create Exchange Transaction

**Purpose**: Test the core exchange functionality

### Steps:
1. **Navigate to "Transactions" tab**
2. **Click "New Exchange" button**
3. **Fill out the form**:
   - Select customer: "Rajesh Kumar"
   - Set transaction date: Today's date
   - Add item details:
     - Description: "Test Gold Ring"
     - Metal Type: Gold
     - Purity: 22K
     - Gross Weight: 10.000g
     - Stone Weight: 2.000g
     - Rate will auto-fill from current rates
4. **Click "Save Exchange"**

### Expected Results:
- ✅ Net weight calculated automatically (8.000g)
- ✅ Amount calculated automatically (rate × net weight)
- ✅ Transaction saved successfully
- ✅ Transaction appears in list with "Pending" status
- ✅ Transaction number generated (EXG-YYYYMMDD-XXX format)

## 📄 Test 4: Purchase Bill Generation

**Purpose**: Test automatic bill generation with GST

### Steps:
1. **Find a completed exchange transaction** (use sample data)
2. **Click the "Generate Bill" button** (📄 icon)
3. **Review bill details**:
   - Business information
   - Customer details
   - Item breakdown
   - Tax calculations (CGST 1.5%, SGST 1.5%)
4. **Click "Generate Bill"**
5. **Test print functionality**
6. **Test PDF download** (mock functionality)

### Expected Results:
- ✅ Professional bill format displayed
- ✅ All calculations correct
- ✅ Sequential bill number generated (EPB/2024-25/XXXX)
- ✅ Print dialog opens correctly
- ✅ Bill marked as generated in transaction list

## 🛒 Test 5: Sales with Exchange Integration

**Purpose**: Test the enhanced sales form with exchange deductions

### Steps:
1. **Navigate to "Sales" tab**
2. **Click "Sale with Exchange" button** (orange button)
3. **Fill sale form**:
   - Select customer: "Rajesh Kumar"
   - Set date: Today
   - Add sale items from inventory
4. **Switch to "Exchange Items" tab**
5. **Click "Add Exchange"**
6. **Select available exchange items**
7. **Adjust deduction amounts if needed**
8. **Review totals in summary section**
9. **Click "Create Sale"**

### Expected Results:
- ✅ Enhanced form opens with two tabs
- ✅ Exchange items tab shows customer's available exchanges
- ✅ Automatic deduction calculations
- ✅ Bill summary shows:
   - Sale total
   - Exchange deduction
   - Final amount payable
- ✅ Sale created successfully

## 📋 Test 6: Enhanced Invoice Template

**Purpose**: Test transparent customer invoicing

### Steps:
1. **Create a sale with exchange** (from Test 5)
2. **View the generated invoice**
3. **Verify invoice sections**:
   - Business header
   - Customer details
   - Sale items table
   - Exchange items table (with deductions)
   - Clear totals breakdown
   - Exchange summary box

### Expected Results:
- ✅ Professional invoice layout
- ✅ Clear separation of sale and exchange items
- ✅ Transparent pricing breakdown
- ✅ Exchange summary highlights savings
- ✅ Print/PDF functionality works

## 💰 Test 7: Exchange Rate Management

**Purpose**: Test rate updates and history tracking

### Steps:
1. **Navigate to "Exchange Rates" tab**
2. **View current rates** for different metals/purities
3. **Click "Update Rate" on any rate**
4. **Change the rate value**
5. **Add change reason**
6. **Save the update**
7. **Verify rate history**

### Expected Results:
- ✅ Current rates displayed correctly
- ✅ Rate update form works
- ✅ History tracking captures changes
- ✅ New rates applied to future transactions

## 📈 Test 8: Reports and Analytics

**Purpose**: Test reporting functionality

### Steps:
1. **Navigate to "Reports" tab**
2. **Generate different reports**:
   - Daily exchange summary
   - Customer analysis
   - Utilization reports
3. **Test date range filters**
4. **Export reports** (if implemented)

### Expected Results:
- ✅ Reports generate correctly
- ✅ Data matches transaction records
- ✅ Filters work properly
- ✅ Export functionality works

## 🔍 Test 9: Audit Trail Verification

**Purpose**: Verify complete transaction history

### Steps:
1. **Create an exchange transaction**
2. **Generate purchase bill**
3. **Use in a sale**
4. **Check audit trail** in dashboard
5. **Verify all actions recorded**

### Expected Results:
- ✅ All actions logged with timestamps
- ✅ User information captured (if available)
- ✅ Before/after values recorded
- ✅ Related documents linked

## 🧪 Test 10: Edge Cases and Error Handling

**Purpose**: Test system robustness

### Test Cases:
1. **Invalid weight values** (negative, zero)
2. **Missing required fields**
3. **Duplicate transaction numbers**
4. **Exchange value exceeding sale total**
5. **Network connectivity issues**

### Expected Results:
- ✅ Appropriate error messages
- ✅ Form validation works
- ✅ Data integrity maintained
- ✅ Graceful error handling

## 📊 Sample Test Data

The system includes sample data for testing:

### Customers:
- Rajesh Kumar (9876543210)
- Priya Sharma (9876543211)
- Amit Patel (9876543212)
- Sunita Reddy (9876543213)
- Vikram Singh (9876543214)

### Exchange Transactions:
- EXG-20240131-001: Gold bar (₹33,000) - Billed
- EXG-20240130-001: Silver bangles (₹15,600) - Billed
- EXG-20240129-001: Gold chain (₹82,900) - Pending bill
- EXG-20240128-001: Mixed jewelry (₹51,400) - Billed
- EXG-20240127-001: Heavy necklace (₹124,000) - Pending

## ✅ Testing Checklist

Mark each item as you complete testing:

### Database Setup:
- [ ] Database tables created successfully
- [ ] Sample data inserted
- [ ] Exchange rates populated
- [ ] Bill sequences initialized

### Core Functionality:
- [ ] Exchange transaction creation
- [ ] Item weight/amount calculations
- [ ] Purchase bill generation
- [ ] Sales with exchange integration
- [ ] Enhanced invoice templates

### User Interface:
- [ ] Demo workflow plays correctly
- [ ] Dashboard shows accurate data
- [ ] Forms validate input properly
- [ ] Navigation works smoothly
- [ ] Print/export functions work

### Business Logic:
- [ ] GST calculations correct
- [ ] Sequential numbering works
- [ ] Exchange deductions accurate
- [ ] Audit trail complete
- [ ] Rate management functional

### Error Handling:
- [ ] Invalid input handled gracefully
- [ ] Network errors managed
- [ ] Data validation works
- [ ] User feedback clear

## 🐛 Common Issues and Solutions

### Database Connection Issues:
- **Problem**: Cannot connect to database
- **Solution**: Check MySQL server status, credentials, and network connectivity

### Missing Tables:
- **Problem**: Tables not found errors
- **Solution**: Re-run migration scripts, check database permissions

### Calculation Errors:
- **Problem**: Incorrect weight/amount calculations
- **Solution**: Verify exchange rates, check decimal precision settings

### Bill Generation Fails:
- **Problem**: Purchase bills not generating
- **Solution**: Check bill sequence table, verify transaction status

### Exchange Items Not Showing:
- **Problem**: No exchange items available for customer
- **Solution**: Ensure customer has completed exchange transactions

## 📞 Support

If you encounter issues during testing:

1. **Check the console** for error messages
2. **Verify database connectivity** and data
3. **Review the logs** for detailed error information
4. **Consult the documentation** for configuration details
5. **Contact support** with specific error details

## 🎉 Success Criteria

Testing is successful when:
- ✅ All core workflows complete without errors
- ✅ Calculations are accurate and consistent
- ✅ User interface is responsive and intuitive
- ✅ Data integrity is maintained throughout
- ✅ Business requirements are met
- ✅ Performance is acceptable for expected load

---

**Happy Testing!** 🚀

The Exchange Billing Integration System is designed to be robust, user-friendly, and business-ready. Following this testing guide will ensure you're confident in the system's capabilities before going live.
