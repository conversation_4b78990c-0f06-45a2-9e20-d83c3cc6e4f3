"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Scale, 
  FileText, 
  ShoppingCart, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Eye,
  Download
} from "lucide-react"
import { formatCurrency, formatDate } from "@/lib/utils"

interface ExchangeTransaction {
  id: string
  transactionNumber: string
  transactionDate: string
  totalAmount: number
  status: 'pending' | 'completed' | 'cancelled'
  customer?: {
    name: string
    phone: string
  }
  items: Array<{
    itemDescription: string
    metalType: 'gold' | 'silver'
    purity: string
    netWeight: number
    amount: number
  }>
  purchaseBillGenerated?: boolean
  purchaseBillNumber?: string
  usedInSales?: Array<{
    saleId: string
    invoiceNumber: string
    deductionAmount: number
    saleDate: string
  }>
}

export function ExchangeDashboard() {
  const [transactions, setTransactions] = useState<ExchangeTransaction[]>([])
  const [selectedTransaction, setSelectedTransaction] = useState<ExchangeTransaction | null>(null)
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false)

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockTransactions: ExchangeTransaction[] = [
      {
        id: 'exg_001',
        transactionNumber: 'EXG-20240131-001',
        transactionDate: '2024-01-31',
        totalAmount: 33000,
        status: 'completed',
        customer: {
          name: 'Rajesh Kumar',
          phone: '9876543210'
        },
        items: [
          {
            itemDescription: 'GOLD OLD BAR',
            metalType: 'gold',
            purity: '22K',
            netWeight: 7.500,
            amount: 33000
          }
        ],
        purchaseBillGenerated: true,
        purchaseBillNumber: 'EPB/2024-25/0001',
        usedInSales: [
          {
            saleId: 'sale_001',
            invoiceNumber: 'INV-2024-001',
            deductionAmount: 25000,
            saleDate: '2024-02-01'
          }
        ]
      },
      {
        id: 'exg_002',
        transactionNumber: 'EXG-20240130-001',
        transactionDate: '2024-01-30',
        totalAmount: 15600,
        status: 'completed',
        customer: {
          name: 'Priya Sharma',
          phone: '9876543211'
        },
        items: [
          {
            itemDescription: 'OLD SILVER BANGLES',
            metalType: 'silver',
            purity: '925',
            netWeight: 200.000,
            amount: 15600
          }
        ],
        purchaseBillGenerated: true,
        purchaseBillNumber: 'EPB/2024-25/0002'
      },
      {
        id: 'exg_003',
        transactionNumber: 'EXG-20240129-001',
        transactionDate: '2024-01-29',
        totalAmount: 45000,
        status: 'completed',
        items: [
          {
            itemDescription: 'GOLD CHAIN OLD',
            metalType: 'gold',
            purity: '18K',
            netWeight: 12.500,
            amount: 45000
          }
        ],
        purchaseBillGenerated: false
      }
    ]
    setTransactions(mockTransactions)
  }, [])

  const getTransactionStatusBadge = (transaction: ExchangeTransaction) => {
    if (transaction.status === 'pending') {
      return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
    }
    
    if (!transaction.purchaseBillGenerated) {
      return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Bill Pending</Badge>
    }
    
    if (transaction.usedInSales && transaction.usedInSales.length > 0) {
      const totalUsed = transaction.usedInSales.reduce((sum, sale) => sum + sale.deductionAmount, 0)
      const remainingValue = transaction.totalAmount - totalUsed
      
      if (remainingValue <= 0) {
        return <Badge variant="default"><CheckCircle className="h-3 w-3 mr-1" />Fully Utilized</Badge>
      } else {
        return <Badge variant="outline"><TrendingUp className="h-3 w-3 mr-1" />Partially Used</Badge>
      }
    }
    
    return <Badge variant="outline"><FileText className="h-3 w-3 mr-1" />Available</Badge>
  }

  const getUtilizationPercentage = (transaction: ExchangeTransaction) => {
    if (!transaction.usedInSales || transaction.usedInSales.length === 0) return 0
    const totalUsed = transaction.usedInSales.reduce((sum, sale) => sum + sale.deductionAmount, 0)
    return Math.min(100, (totalUsed / transaction.totalAmount) * 100)
  }

  const getRemainingValue = (transaction: ExchangeTransaction) => {
    if (!transaction.usedInSales || transaction.usedInSales.length === 0) return transaction.totalAmount
    const totalUsed = transaction.usedInSales.reduce((sum, sale) => sum + sale.deductionAmount, 0)
    return Math.max(0, transaction.totalAmount - totalUsed)
  }

  const handleViewHistory = (transaction: ExchangeTransaction) => {
    setSelectedTransaction(transaction)
    setIsHistoryDialogOpen(true)
  }

  // Calculate dashboard statistics
  const stats = {
    totalTransactions: transactions.length,
    totalValue: transactions.reduce((sum, t) => sum + t.totalAmount, 0),
    billsPending: transactions.filter(t => t.status === 'completed' && !t.purchaseBillGenerated).length,
    fullyUtilized: transactions.filter(t => {
      const totalUsed = t.usedInSales?.reduce((sum, sale) => sum + sale.deductionAmount, 0) || 0
      return totalUsed >= t.totalAmount
    }).length,
    availableValue: transactions.reduce((sum, t) => sum + getRemainingValue(t), 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Exchange Transaction Dashboard</h1>
        <p className="text-muted-foreground">
          Complete lifecycle tracking of exchange transactions
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exchanges</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bills Pending</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.billsPending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fully Utilized</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.fullyUtilized}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Value</CardTitle>
            <FileText className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.availableValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Transaction Lifecycle View */}
      <Card>
        <CardHeader>
          <CardTitle>Exchange Transaction Lifecycle</CardTitle>
          <CardDescription>
            Track the complete journey from exchange to utilization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {transactions.map((transaction) => (
              <Card key={transaction.id} className="p-4">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{transaction.transactionNumber}</h3>
                      {getTransactionStatusBadge(transaction)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(transaction.transactionDate)} • {transaction.customer?.name || 'Walk-in Customer'}
                    </p>
                    <p className="text-sm font-medium">{formatCurrency(transaction.totalAmount)}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewHistory(transaction)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>

                {/* Transaction Flow */}
                <div className="space-y-4">
                  {/* Items */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Exchange Items</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {transaction.items.map((item, index) => (
                        <div key={index} className="text-xs bg-muted/50 p-2 rounded">
                          <span className="font-medium">{item.itemDescription}</span>
                          <span className="text-muted-foreground ml-2">
                            {item.metalType} {item.purity} • {item.netWeight.toFixed(3)}g • {formatCurrency(item.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Progress Flow */}
                  <div className="space-y-3">
                    {/* Step 1: Exchange Created */}
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Exchange Transaction Created</p>
                        <p className="text-xs text-muted-foreground">{formatDate(transaction.transactionDate)}</p>
                      </div>
                    </div>

                    {/* Step 2: Purchase Bill */}
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        transaction.purchaseBillGenerated 
                          ? 'bg-green-100' 
                          : 'bg-orange-100'
                      }`}>
                        {transaction.purchaseBillGenerated ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-orange-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          Purchase Bill {transaction.purchaseBillGenerated ? 'Generated' : 'Pending'}
                        </p>
                        {transaction.purchaseBillNumber && (
                          <p className="text-xs text-muted-foreground">{transaction.purchaseBillNumber}</p>
                        )}
                      </div>
                    </div>

                    {/* Step 3: Sales Usage */}
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        transaction.usedInSales && transaction.usedInSales.length > 0
                          ? 'bg-blue-100' 
                          : 'bg-gray-100'
                      }`}>
                        <ShoppingCart className={`h-4 w-4 ${
                          transaction.usedInSales && transaction.usedInSales.length > 0
                            ? 'text-blue-600' 
                            : 'text-gray-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Sales Integration</p>
                        {transaction.usedInSales && transaction.usedInSales.length > 0 ? (
                          <div className="space-y-1">
                            {transaction.usedInSales.map((sale, index) => (
                              <p key={index} className="text-xs text-muted-foreground">
                                {sale.invoiceNumber} • {formatCurrency(sale.deductionAmount)} • {formatDate(sale.saleDate)}
                              </p>
                            ))}
                          </div>
                        ) : (
                          <p className="text-xs text-muted-foreground">Not used in any sales yet</p>
                        )}
                      </div>
                    </div>

                    {/* Utilization Progress */}
                    {transaction.usedInSales && transaction.usedInSales.length > 0 && (
                      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Utilization Progress</span>
                          <span className="text-sm text-muted-foreground">
                            {getUtilizationPercentage(transaction).toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={getUtilizationPercentage(transaction)} className="mb-2" />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Used: {formatCurrency(transaction.totalAmount - getRemainingValue(transaction))}</span>
                          <span>Remaining: {formatCurrency(getRemainingValue(transaction))}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Transaction History Dialog */}
      {isHistoryDialogOpen && selectedTransaction && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>{selectedTransaction.transactionNumber}</CardDescription>
              </div>
              <Button variant="ghost" onClick={() => setIsHistoryDialogOpen(false)}>
                ×
              </Button>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="overview">
                <TabsList>
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="audit">Audit Trail</TabsTrigger>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Transaction Details</h4>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Number:</span> {selectedTransaction.transactionNumber}</p>
                        <p><span className="font-medium">Date:</span> {formatDate(selectedTransaction.transactionDate)}</p>
                        <p><span className="font-medium">Amount:</span> {formatCurrency(selectedTransaction.totalAmount)}</p>
                        <p><span className="font-medium">Status:</span> {selectedTransaction.status}</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Customer</h4>
                      <div className="space-y-1 text-sm">
                        <p><span className="font-medium">Name:</span> {selectedTransaction.customer?.name || 'Walk-in Customer'}</p>
                        {selectedTransaction.customer?.phone && (
                          <p><span className="font-medium">Phone:</span> {selectedTransaction.customer.phone}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="audit" className="space-y-4">
                  <div className="text-center py-8 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4" />
                    <p>Audit trail functionality will be implemented with actual backend integration</p>
                  </div>
                </TabsContent>
                
                <TabsContent value="documents" className="space-y-4">
                  <div className="space-y-2">
                    {selectedTransaction.purchaseBillGenerated && (
                      <div className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">Purchase Bill</p>
                          <p className="text-sm text-muted-foreground">{selectedTransaction.purchaseBillNumber}</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    )}
                    {selectedTransaction.usedInSales?.map((sale, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">Sales Invoice</p>
                          <p className="text-sm text-muted-foreground">{sale.invoiceNumber}</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
