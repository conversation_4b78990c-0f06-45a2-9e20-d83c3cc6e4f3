import { BaseService } from '../base-service'
import { Customer } from '../../types'

export class CustomerService extends BaseService<Customer> {
  protected tableName = 'customers'

  async findByPhone(phone: string): Promise<Customer[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE phone LIKE ? ORDER BY created_at DESC`
    const rows = await this.executeQuery(sql, [`%${phone}%`])
    return rows.map(row => this.transformKeys(row) as Customer)
  }

  async findByName(name: string): Promise<Customer[]> {
    const sql = `SELECT * FROM ${this.tableName} WHERE name LIKE ? ORDER BY created_at DESC`
    const rows = await this.executeQuery(sql, [`%${name}%`])
    return rows.map(row => this.transformKeys(row) as Customer)
  }

  async updateTotalPurchases(customerId: string, amount: number): Promise<void> {
    const sql = `UPDATE ${this.tableName} SET total_purchases = total_purchases + ?, last_visit = ?, updated_at = ? WHERE id = ?`
    await this.executeUpdate(sql, [amount, new Date().toISOString(), new Date().toISOString(), customerId])
  }

  async getTopCustomers(limit: number = 10): Promise<Customer[]> {
    const sql = `SELECT * FROM ${this.tableName} ORDER BY total_purchases DESC LIMIT ?`
    const rows = await this.executeQuery(sql, [limit])
    return rows.map(row => this.transformKeys(row) as Customer)
  }

  async getRecentCustomers(limit: number = 10): Promise<Customer[]> {
    const sql = `SELECT * FROM ${this.tableName} ORDER BY last_visit DESC LIMIT ?`
    const rows = await this.executeQuery(sql, [limit])
    return rows.map(row => this.transformKeys(row) as Customer)
  }

  async searchCustomers(query: string): Promise<Customer[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE name LIKE ? OR phone LIKE ? OR email LIKE ? 
      ORDER BY created_at DESC
    `
    const searchTerm = `%${query}%`
    const rows = await this.executeQuery(sql, [searchTerm, searchTerm, searchTerm])
    return rows.map(row => this.transformKeys(row) as Customer)
  }

  async getCustomerStats(): Promise<{
    totalCustomers: number
    newThisMonth: number
    topSpender: Customer | null
  }> {
    const totalCustomers = await this.count()
    
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)
    
    const newThisMonth = await this.count({
      created_at: `>= '${thisMonth.toISOString()}'`
    })

    const topSpenders = await this.getTopCustomers(1)
    const topSpender = topSpenders.length > 0 ? topSpenders[0] : null

    return {
      totalCustomers,
      newThisMonth,
      topSpender
    }
  }
}
