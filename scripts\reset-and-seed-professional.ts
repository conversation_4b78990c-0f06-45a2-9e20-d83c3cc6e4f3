#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function resetAndSeedProfessional() {
  console.log('🔄 Resetting Database and Seeding Professional Sample Data...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Clean reset - preserve structure, clear data
    console.log('🧹 Step 1: Clean database reset...')
    
    // Disable foreign key checks for clean reset
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    
    // Clear all data from tables (preserve structure)
    const tablesToClear = [
      'exchange_audit_trail',
      'sales_exchange_items', 
      'exchange_purchase_bills',
      'exchange_items',
      'exchange_transactions',
      'sale_items',
      'sales',
      'inventory',
      'customers',
      'suppliers',
      'categories',
      'users',
      'business_settings',
      'system_settings',
      'bill_sequences',
      'exchange_rates',
      'metal_rates'
    ]

    for (const table of tablesToClear) {
      try {
        await connection.execute(`DELETE FROM ${table}`)
        console.log(`   ✅ Cleared ${table}`)
      } catch (error) {
        console.log(`   ⚠️  Could not clear ${table} (may not exist)`)
      }
    }

    // Reset auto-increment counters
    try {
      await connection.execute('ALTER TABLE business_settings AUTO_INCREMENT = 1')
      await connection.execute('ALTER TABLE system_settings AUTO_INCREMENT = 1')
    } catch (error) {
      console.log('   ⚠️  Could not reset auto-increment counters')
    }

    // Re-enable foreign key checks
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
    console.log('   ✅ Database reset completed')

    // Step 2: Seed business configuration
    console.log('\n🏢 Step 2: Seeding business configuration...')
    
    // Business settings
    await connection.execute(`
      INSERT INTO business_settings (
        business_name, business_type, address_line1, address_line2, city, state, 
        postal_code, country, phone, email, website, gst_number, pan_number,
        bank_name, bank_account_number, bank_ifsc, currency, timezone,
        financial_year_start, cgst_rate, sgst_rate, igst_rate
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'JJ Jewellers Premium', 'retail', '123 Gold Street', 'Jewelry Market Complex',
      'Mumbai', 'Maharashtra', '400001', 'India', '+91-22-********', 
      '<EMAIL>', 'www.jjjewellers.com', '27**********1Z5',
      '**********', 'HDFC Bank', '********901234', 'HDFC0001234',
      'INR', 'Asia/Kolkata', 'april', 1.50, 1.50, 3.00
    ])
    console.log('   ✅ Business settings configured')

    // System settings
    const systemSettings = [
      ['app_version', '2.0.0', 'string', 'Application version', true],
      ['maintenance_mode', 'false', 'boolean', 'Maintenance mode flag', true],
      ['backup_enabled', 'true', 'boolean', 'Automatic backup enabled', false],
      ['low_stock_threshold', '5', 'number', 'Low stock alert threshold', false],
      ['auto_update_rates', 'true', 'boolean', 'Auto update metal rates', false],
      ['invoice_template', 'premium', 'string', 'Default invoice template', false],
      ['print_logo', 'true', 'boolean', 'Print logo on invoices', false],
      ['email_notifications', 'true', 'boolean', 'Email notifications enabled', false],
      ['sms_notifications', 'true', 'boolean', 'SMS notifications enabled', false],
      ['loyalty_program', 'true', 'boolean', 'Customer loyalty program enabled', false],
      ['exchange_discount', '2.0', 'number', 'Exchange rate discount percentage', false]
    ]

    for (const [key, value, type, description, isSystem] of systemSettings) {
      await connection.execute(`
        INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_system) 
        VALUES (?, ?, ?, ?, ?)
      `, [key, value, type, description, isSystem])
    }
    console.log('   ✅ System settings configured')

    // Step 3: Create users
    console.log('\n👥 Step 3: Creating users...')
    
    const users = [
      {
        id: randomUUID(),
        username: 'admin',
        email: '<EMAIL>',
        password_hash: '$2b$10$defaulthash', // In production, use proper bcrypt
        first_name: 'System',
        last_name: 'Administrator',
        phone: '+91-9999999999',
        role: 'super_admin'
      },
      {
        id: randomUUID(),
        username: 'manager',
        email: '<EMAIL>',
        password_hash: '$2b$10$defaulthash',
        first_name: 'Store',
        last_name: 'Manager',
        phone: '+91-9999999998',
        role: 'manager'
      },
      {
        id: randomUUID(),
        username: 'sales1',
        email: '<EMAIL>',
        password_hash: '$2b$10$defaulthash',
        first_name: 'Rajesh',
        last_name: 'Kumar',
        phone: '+91-9999999997',
        role: 'sales_staff'
      },
      {
        id: randomUUID(),
        username: 'sales2',
        email: '<EMAIL>',
        password_hash: '$2b$10$defaulthash',
        first_name: 'Priya',
        last_name: 'Sharma',
        phone: '+91-9999999996',
        role: 'sales_staff'
      }
    ]

    for (const user of users) {
      await connection.execute(`
        INSERT INTO users (
          id, username, email, password_hash, first_name, last_name, phone, role,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
      `, [
        user.id, user.username, user.email, user.password_hash,
        user.first_name, user.last_name, user.phone, user.role
      ])
    }
    console.log(`   ✅ Created ${users.length} users`)

    // Step 4: Create categories
    console.log('\n📂 Step 4: Creating product categories...')
    
    const categories = [
      { name: 'Rings', code: 'RING', hsn: '71131900', charge_type: 'percentage', charge_value: 15.00 },
      { name: 'Necklaces', code: 'NECK', hsn: '71131100', charge_type: 'percentage', charge_value: 18.00 },
      { name: 'Earrings', code: 'EARR', hsn: '71131200', charge_type: 'percentage', charge_value: 12.00 },
      { name: 'Bangles', code: 'BANG', hsn: '71131300', charge_type: 'percentage', charge_value: 10.00 },
      { name: 'Chains', code: 'CHAI', hsn: '71131400', charge_type: 'percentage', charge_value: 8.00 },
      { name: 'Pendants', code: 'PEND', hsn: '71131500', charge_type: 'percentage', charge_value: 15.00 },
      { name: 'Bracelets', code: 'BRAC', hsn: '71131600', charge_type: 'percentage', charge_value: 12.00 },
      { name: 'Anklets', code: 'ANKL', hsn: '71131700', charge_type: 'percentage', charge_value: 10.00 },
      { name: 'Nose Pins', code: 'NOSE', hsn: '71131800', charge_type: 'percentage', charge_value: 20.00 },
      { name: 'Coins & Bars', code: 'COIN', hsn: '71131000', charge_type: 'fixed', charge_value: 500.00 }
    ]

    for (const category of categories) {
      await connection.execute(`
        INSERT INTO categories (
          id, name, category_code, hsn_code, making_charge_type, making_charge_value,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
      `, [
        randomUUID(), category.name, category.code, category.hsn,
        category.charge_type, category.charge_value
      ])
    }
    console.log(`   ✅ Created ${categories.length} product categories`)

    // Step 5: Create metal rates
    console.log('\n💰 Step 5: Setting up current metal rates...')
    
    const metalRates = [
      { metal: 'gold', purity: '24K', rate: 7200.00 },
      { metal: 'gold', purity: '22K', rate: 6600.00 },
      { metal: 'gold', purity: '18K', rate: 5400.00 },
      { metal: 'gold', purity: '14K', rate: 4200.00 },
      { metal: 'gold', purity: '10K', rate: 3000.00 },
      { metal: 'silver', purity: '999', rate: 90.00 },
      { metal: 'silver', purity: '925', rate: 83.00 },
      { metal: 'silver', purity: '900', rate: 81.00 },
      { metal: 'platinum', purity: '950', rate: 3200.00 },
      { metal: 'platinum', purity: '900', rate: 3000.00 }
    ]

    for (const rate of metalRates) {
      const rateId = randomUUID()
      
      // Insert into metal_rates table
      try {
        await connection.execute(`
          INSERT INTO metal_rates (
            id, metal_type, purity, rate_per_gram, effective_date, is_active,
            source, created_at, updated_at
          ) VALUES (?, ?, ?, ?, CURDATE(), TRUE, 'manual', NOW(), NOW())
        `, [rateId, rate.metal, rate.purity, rate.rate])
      } catch (error) {
        console.log(`   ⚠️  Could not insert into metal_rates: ${error}`)
      }
      
      // Insert into exchange_rates table
      await connection.execute(`
        INSERT INTO exchange_rates (
          id, metal_type, purity, rate_per_gram, effective_date, is_active,
          source, created_at, updated_at
        ) VALUES (?, ?, ?, ?, CURDATE(), TRUE, 'manual', NOW(), NOW())
      `, [randomUUID(), rate.metal, rate.purity, rate.rate])
    }
    console.log(`   ✅ Set up ${metalRates.length} metal rates`)

    // Step 6: Create suppliers
    console.log('\n🏭 Step 6: Creating suppliers...')
    
    const suppliers = [
      {
        id: randomUUID(),
        code: 'SUP001',
        company: 'Gold Craft Industries Pvt Ltd',
        contact: 'Rajesh Agarwal',
        phone: '+91-9876543210',
        email: '<EMAIL>',
        city: 'Mumbai',
        state: 'Maharashtra',
        type: 'manufacturer'
      },
      {
        id: randomUUID(),
        code: 'SUP002',
        company: 'Silver Palace Wholesale',
        contact: 'Priya Sharma',
        phone: '+91-9876543211',
        email: '<EMAIL>',
        city: 'Jaipur',
        state: 'Rajasthan',
        type: 'wholesaler'
      },
      {
        id: randomUUID(),
        code: 'SUP003',
        company: 'Diamond Dreams Ltd',
        contact: 'Amit Patel',
        phone: '+91-9876543212',
        email: '<EMAIL>',
        city: 'Surat',
        state: 'Gujarat',
        type: 'manufacturer'
      },
      {
        id: randomUUID(),
        code: 'SUP004',
        company: 'Platinum Plus',
        contact: 'Kavya Nair',
        phone: '+91-9876543213',
        email: '<EMAIL>',
        city: 'Kochi',
        state: 'Kerala',
        type: 'wholesaler'
      }
    ]

    for (const supplier of suppliers) {
      try {
        await connection.execute(`
          INSERT INTO suppliers (
            id, supplier_code, company_name, contact_person, phone, email,
            city, state, country, supplier_type, is_active, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'India', ?, TRUE, NOW(), NOW())
        `, [
          supplier.id, supplier.code, supplier.company, supplier.contact,
          supplier.phone, supplier.email, supplier.city, supplier.state, supplier.type
        ])
      } catch (error) {
        console.log(`   ⚠️  Could not create supplier ${supplier.code}: ${error}`)
      }
    }
    console.log(`   ✅ Created ${suppliers.length} suppliers`)

    // Step 7: Create customers
    console.log('\n👥 Step 7: Creating diverse customers...')
    
    const customers = [
      {
        id: randomUUID(),
        code: 'CUST000001',
        first_name: 'Arjun',
        last_name: 'Mehta',
        phone: '+91-9876543220',
        email: '<EMAIL>',
        city: 'Mumbai',
        state: 'Maharashtra',
        purchases: 250000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000002',
        first_name: 'Kavya',
        last_name: 'Nair',
        phone: '+91-9876543221',
        email: '<EMAIL>',
        city: 'Kochi',
        state: 'Kerala',
        purchases: 180000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000003',
        first_name: 'Rohit',
        last_name: 'Gupta',
        phone: '+91-9876543222',
        email: '<EMAIL>',
        city: 'Delhi',
        state: 'Delhi',
        purchases: 320000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000004',
        first_name: 'Anita',
        last_name: 'Desai',
        phone: '+91-9876543223',
        email: '<EMAIL>',
        city: 'Pune',
        state: 'Maharashtra',
        purchases: 150000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000005',
        first_name: 'Vikram',
        last_name: 'Singh',
        phone: '+91-9876543224',
        email: '<EMAIL>',
        city: 'Jaipur',
        state: 'Rajasthan',
        purchases: 420000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000006',
        first_name: 'Meera',
        last_name: 'Iyer',
        phone: '+91-9876543225',
        email: '<EMAIL>',
        city: 'Chennai',
        state: 'Tamil Nadu',
        purchases: 280000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000007',
        first_name: 'Rajesh',
        last_name: 'Agarwal',
        phone: '+91-9876543226',
        email: '<EMAIL>',
        city: 'Kolkata',
        state: 'West Bengal',
        purchases: 195000.00
      },
      {
        id: randomUUID(),
        code: 'CUST000008',
        first_name: 'Sunita',
        last_name: 'Reddy',
        phone: '+91-9876543227',
        email: '<EMAIL>',
        city: 'Hyderabad',
        state: 'Telangana',
        purchases: 365000.00
      }
    ]

    for (const customer of customers) {
      await connection.execute(`
        INSERT INTO customers (
          id, customer_code, customer_type, first_name, last_name, phone, email,
          city, state, country, total_purchases, loyalty_points, preferred_contact,
          is_active, last_visit, created_at, updated_at
        ) VALUES (?, ?, 'individual', ?, ?, ?, ?, ?, ?, 'India', ?, ?, 'phone', TRUE, CURDATE(), NOW(), NOW())
      `, [
        customer.id, customer.code, customer.first_name, customer.last_name,
        customer.phone, customer.email, customer.city, customer.state,
        customer.purchases, Math.floor(customer.purchases / 1000) // 1 point per ₹1000
      ])
    }
    console.log(`   ✅ Created ${customers.length} diverse customers`)

    // Step 8: Create bill sequences
    console.log('\n📄 Step 8: Setting up bill sequences...')

    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`

    const sequences = [
      { type: 'sales_invoice', prefix: 'INV', number: 0 },
      { type: 'purchase_bill', prefix: 'PUR', number: 0 },
      { type: 'exchange_purchase', prefix: 'EPB', number: 0 },
      { type: 'scheme_receipt', prefix: 'SCH', number: 0 },
      { type: 'repair_receipt', prefix: 'REP', number: 0 }
    ]

    for (const seq of sequences) {
      await connection.execute(`
        INSERT INTO bill_sequences (
          id, sequence_type, prefix, current_number, financial_year,
          reset_annually, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, TRUE, TRUE, NOW(), NOW())
      `, [randomUUID(), seq.type, seq.prefix, seq.number, financialYear])
    }
    console.log(`   ✅ Set up ${sequences.length} bill sequences`)

    // Step 9: Create comprehensive exchange transactions
    console.log('\n🔄 Step 9: Creating comprehensive exchange transactions...')

    const exchangeTransactions = [
      {
        id: randomUUID(),
        number: 'EXG-20250131-001',
        customer_id: customers[0].id, // Arjun Mehta
        amount: 720000.00,
        notes: 'Premium 24K gold bar exchange - investment grade quality'
      },
      {
        id: randomUUID(),
        number: 'EXG-20250131-002',
        customer_id: customers[1].id, // Kavya Nair
        amount: 45000.00,
        notes: 'Traditional silver jewelry collection - family heirloom pieces'
      },
      {
        id: randomUUID(),
        number: 'EXG-20250131-003',
        customer_id: customers[2].id, // Rohit Gupta
        amount: 485000.00,
        notes: 'Mixed gold jewelry collection - various occasions and purities'
      },
      {
        id: randomUUID(),
        number: 'EXG-20250131-004',
        customer_id: customers[3].id, // Anita Desai
        amount: 125000.00,
        notes: 'Contemporary silver and gold mix - modern designs'
      },
      {
        id: randomUUID(),
        number: 'EXG-20250131-005',
        customer_id: customers[4].id, // Vikram Singh
        amount: 850000.00,
        notes: 'Heritage gold collection - 3 generation family jewelry'
      },
      {
        id: randomUUID(),
        number: 'EXG-20250131-006',
        customer_id: customers[5].id, // Meera Iyer
        amount: 95000.00,
        notes: 'South Indian traditional gold jewelry'
      }
    ]

    for (const transaction of exchangeTransactions) {
      await connection.execute(`
        INSERT INTO exchange_transactions (
          id, transaction_number, customer_id, transaction_date, total_amount,
          payment_method, notes, status, created_at, updated_at
        ) VALUES (?, ?, ?, CURDATE(), ?, 'cash', ?, 'completed', NOW(), NOW())
      `, [
        transaction.id, transaction.number, transaction.customer_id,
        transaction.amount, transaction.notes
      ])
    }
    console.log(`   ✅ Created ${exchangeTransactions.length} exchange transactions`)

    // Step 10: Create detailed exchange items
    console.log('\n💎 Step 10: Creating detailed exchange items...')

    const exchangeItems = [
      // Transaction 1: Premium gold bar
      { transaction_id: exchangeTransactions[0].id, description: 'GOLD BAR 24K INVESTMENT GRADE 100g', metal: 'gold', purity: '24K', gross: 100.000, stone: 0.000, rate: 7200.00, condition: 'excellent' },

      // Transaction 2: Traditional silver collection
      { transaction_id: exchangeTransactions[1].id, description: 'SILVER BANGLES SET TRADITIONAL DESIGN', metal: 'silver', purity: '925', gross: 200.000, stone: 0.000, rate: 83.00, condition: 'good' },
      { transaction_id: exchangeTransactions[1].id, description: 'SILVER ANKLETS PAIR HEAVY TRADITIONAL', metal: 'silver', purity: '925', gross: 150.000, stone: 0.000, rate: 83.00, condition: 'good' },
      { transaction_id: exchangeTransactions[1].id, description: 'SILVER WAIST CHAIN ANTIQUE DESIGN', metal: 'silver', purity: '925', gross: 80.000, stone: 0.000, rate: 83.00, condition: 'fair' },

      // Transaction 3: Mixed gold collection
      { transaction_id: exchangeTransactions[2].id, description: 'GOLD CHAIN 22K THICK ROPE DESIGN', metal: 'gold', purity: '22K', gross: 35.000, stone: 0.000, rate: 6600.00, condition: 'good' },
      { transaction_id: exchangeTransactions[2].id, description: 'GOLD EARRINGS 18K DIAMOND STUDDED', metal: 'gold', purity: '18K', gross: 18.000, stone: 6.000, rate: 5400.00, condition: 'excellent' },
      { transaction_id: exchangeTransactions[2].id, description: 'GOLD BRACELET 14K DESIGNER MODERN', metal: 'gold', purity: '14K', gross: 15.000, stone: 4.000, rate: 4200.00, condition: 'good' },
      { transaction_id: exchangeTransactions[2].id, description: 'GOLD RING SET 22K WEDDING COLLECTION', metal: 'gold', purity: '22K', gross: 25.000, stone: 8.000, rate: 6600.00, condition: 'good' },

      // Transaction 4: Contemporary mix
      { transaction_id: exchangeTransactions[3].id, description: 'GOLD PENDANT 18K HEART DESIGN', metal: 'gold', purity: '18K', gross: 8.000, stone: 2.000, rate: 5400.00, condition: 'good' },
      { transaction_id: exchangeTransactions[3].id, description: 'SILVER NECKLACE 925 CONTEMPORARY', metal: 'silver', purity: '925', gross: 120.000, stone: 10.000, rate: 83.00, condition: 'good' },

      // Transaction 5: Heritage collection
      { transaction_id: exchangeTransactions[4].id, description: 'HERITAGE GOLD NECKLACE KUNDAN WORK', metal: 'gold', purity: '22K', gross: 75.000, stone: 25.000, rate: 6600.00, condition: 'good' },
      { transaction_id: exchangeTransactions[4].id, description: 'ANTIQUE GOLD BANGLES PAIR HEAVY', metal: 'gold', purity: '22K', gross: 90.000, stone: 15.000, rate: 6600.00, condition: 'fair' },
      { transaction_id: exchangeTransactions[4].id, description: 'GOLD ARMLET TRADITIONAL DESIGN', metal: 'gold', purity: '22K', gross: 45.000, stone: 10.000, rate: 6600.00, condition: 'good' },

      // Transaction 6: South Indian traditional
      { transaction_id: exchangeTransactions[5].id, description: 'GOLD CHAIN SOUTH INDIAN TRADITIONAL', metal: 'gold', purity: '22K', gross: 20.000, stone: 0.000, rate: 6600.00, condition: 'good' },
      { transaction_id: exchangeTransactions[5].id, description: 'GOLD EARRINGS TEMPLE JEWELRY STYLE', metal: 'gold', purity: '22K', gross: 12.000, stone: 3.000, rate: 6600.00, condition: 'good' }
    ]

    for (const item of exchangeItems) {
      await connection.execute(`
        INSERT INTO exchange_items (
          id, transaction_id, item_description, metal_type, purity, gross_weight,
          stone_weight, rate_per_gram, item_condition, notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'Professional sample data', NOW(), NOW())
      `, [
        randomUUID(), item.transaction_id, item.description, item.metal, item.purity,
        item.gross, item.stone, item.rate, item.condition
      ])
    }
    console.log(`   ✅ Created ${exchangeItems.length} detailed exchange items`)

    // Step 11: Generate purchase bills for completed transactions
    console.log('\n📄 Step 11: Generating professional purchase bills...')

    let billCounter = 1
    for (const transaction of exchangeTransactions) {
      const billId = randomUUID()
      const billNumber = `EPB/${financialYear}/${String(billCounter).padStart(4, '0')}`
      const subtotal = transaction.amount
      const cgstAmount = (subtotal * 1.5) / 100
      const sgstAmount = (subtotal * 1.5) / 100
      const totalWithTax = subtotal + cgstAmount + sgstAmount

      await connection.execute(`
        INSERT INTO exchange_purchase_bills (
          id, bill_number, exchange_transaction_id, customer_id, bill_date,
          subtotal, cgst_amount, sgst_amount, payment_method, payment_status,
          notes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, 'cash', 'paid', 'Professional sample bill', NOW(), NOW())
      `, [
        billId, billNumber, transaction.id, transaction.customer_id,
        subtotal, cgstAmount, sgstAmount
      ])

      // Update transaction with bill reference
      await connection.execute(`
        UPDATE exchange_transactions
        SET purchase_bill_generated = TRUE, purchase_bill_id = ?,
            purchase_bill_number = ?, purchase_bill_date = CURDATE()
        WHERE id = ?
      `, [billId, billNumber, transaction.id])

      billCounter++
    }

    // Update bill sequence
    await connection.execute(`
      UPDATE bill_sequences SET current_number = ? WHERE sequence_type = 'exchange_purchase'
    `, [exchangeTransactions.length])

    console.log(`   ✅ Generated ${exchangeTransactions.length} professional purchase bills`)

    // Step 12: Create comprehensive audit trail
    console.log('\n📋 Step 12: Creating comprehensive audit trail...')

    let auditCounter = 0
    for (const transaction of exchangeTransactions) {
      // Transaction created audit
      await connection.execute(`
        INSERT INTO exchange_audit_trail (
          id, exchange_transaction_id, action_type, action_description, new_values, performed_at
        ) VALUES (?, ?, 'created', ?, ?, NOW())
      `, [
        randomUUID(), transaction.id,
        `Exchange transaction ${transaction.number} created for customer`,
        JSON.stringify({ amount: transaction.amount, status: 'completed' })
      ])

      // Bill generated audit
      await connection.execute(`
        INSERT INTO exchange_audit_trail (
          id, exchange_transaction_id, action_type, action_description, new_values, performed_at
        ) VALUES (?, ?, 'billed', ?, ?, NOW())
      `, [
        randomUUID(), transaction.id,
        `Purchase bill generated for transaction ${transaction.number}`,
        JSON.stringify({ billNumber: `EPB/${financialYear}/${String(auditCounter + 1).padStart(4, '0')}` })
      ])

      auditCounter++
    }
    console.log(`   ✅ Created comprehensive audit trail entries`)

    console.log('\n🎉 Database Reset and Professional Sample Data Seeding Completed!')

  } catch (error) {
    console.error('\n❌ Database reset and seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the reset and seed operation
resetAndSeedProfessional().catch(console.error)
