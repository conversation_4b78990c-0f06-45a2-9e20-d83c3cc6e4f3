"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useStore } from "@/lib/store"
import { TrendingUp, IndianRupee, ShoppingCart, Users, Download } from "lucide-react"
import { useState } from "react"

export function SalesReport() {
  const { sales, customers } = useStore()
  const [dateRange, setDateRange] = useState("all")
  const [customerFilter, setCustomerFilter] = useState("all")

  const filteredSales = sales.filter((sale) => {
    if (customerFilter !== "all" && sale.customer.id !== customerFilter) return false
    // Add date filtering logic here if needed
    return true
  })

  const calculateTotals = () => {
    const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0)
    const totalNetWeight = filteredSales.reduce(
      (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + item.netWeight, 0),
      0,
    )
    const totalItems = filteredSales.reduce((sum, sale) => sum + sale.items.length, 0)
    const uniqueCustomers = new Set(filteredSales.map((sale) => sale.customer.id)).size

    return { totalSales, totalNetWeight, totalItems, uniqueCustomers }
  }

  const totals = calculateTotals()

  const exportReport = () => {
    // Simulate export functionality
    alert("Sales report exported successfully!")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Sales Report</h3>
          <p className="text-muted-foreground">Comprehensive sales analysis and performance metrics</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={dateRange} onValueChange={setDateRange}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select date range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Time</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>

        <Select value={customerFilter} onValueChange={setCustomerFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select customer" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Customers</SelectItem>
            {customers.map((customer) => (
              <SelectItem key={customer.id} value={customer.id}>
                {customer.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalSales.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{filteredSales.length} transactions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Weight Sold</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totals.totalNetWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Pure metal weight</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Items Sold</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.totalItems}</div>
            <p className="text-xs text-muted-foreground">Total pieces</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.uniqueCustomers}</div>
            <p className="text-xs text-muted-foreground">Unique buyers</p>
          </CardContent>
        </Card>
      </div>

      {/* Sales Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Transactions</CardTitle>
          <CardDescription>Detailed breakdown of all sales transactions</CardDescription>
        </CardHeader>
        <CardContent>
          {filteredSales.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">No sales data available</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice No.</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Net Weight</TableHead>
                  <TableHead>Subtotal</TableHead>
                  <TableHead>Tax</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSales.map((sale) => (
                  <TableRow key={sale.id}>
                    <TableCell className="font-medium">{sale.id}</TableCell>
                    <TableCell>{sale.date}</TableCell>
                    <TableCell>{sale.customer.name}</TableCell>
                    <TableCell>{sale.items.length}</TableCell>
                    <TableCell className="font-medium text-blue-600">
                      {sale.items.reduce((sum, item) => sum + item.netWeight, 0).toFixed(1)}g
                    </TableCell>
                    <TableCell>₹{sale.subtotal.toLocaleString()}</TableCell>
                    <TableCell>₹{(sale.cgst + sale.sgst).toLocaleString()}</TableCell>
                    <TableCell className="font-medium">₹{sale.total.toLocaleString()}</TableCell>
                    <TableCell>
                      <Badge variant="default">{sale.status}</Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Top Customers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Customers</CardTitle>
          <CardDescription>Customers with highest purchase value</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Transactions</TableHead>
                <TableHead>Total Purchase</TableHead>
                <TableHead>Net Weight</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {customers
                .map((customer) => {
                  const customerSales = filteredSales.filter((sale) => sale.customer.id === customer.id)
                  const totalPurchase = customerSales.reduce((sum, sale) => sum + sale.total, 0)
                  const totalWeight = customerSales.reduce(
                    (sum, sale) => sum + sale.items.reduce((itemSum, item) => itemSum + item.netWeight, 0),
                    0,
                  )
                  return {
                    ...customer,
                    transactions: customerSales.length,
                    totalPurchase,
                    totalWeight,
                  }
                })
                .filter((customer) => customer.transactions > 0)
                .sort((a, b) => b.totalPurchase - a.totalPurchase)
                .slice(0, 10)
                .map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">{customer.name}</TableCell>
                    <TableCell>{customer.transactions}</TableCell>
                    <TableCell>₹{customer.totalPurchase.toLocaleString()}</TableCell>
                    <TableCell className="text-blue-600 font-medium">{customer.totalWeight.toFixed(1)}g</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
