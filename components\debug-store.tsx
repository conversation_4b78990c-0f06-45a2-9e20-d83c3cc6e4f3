"use client"

import { useStore } from '@/lib/store'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw, Database, Bug } from 'lucide-react'
import { useState } from 'react'

export function DebugStore() {
  const { 
    sales, 
    customers, 
    inventory, 
    purchases, 
    schemes, 
    repairs,
    loadSales,
    loadCustomers,
    loadInventory,
    loadPurchases,
    loadSchemes,
    loadRepairs,
    isLoading
  } = useStore()
  
  const [debugInfo, setDebugInfo] = useState<any>(null)

  const testAPIDirectly = async () => {
    try {
      const response = await fetch('/api/sales')
      const data = await response.json()
      setDebugInfo({
        apiResponse: data,
        storeData: sales,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      setDebugInfo({
        error: error.message,
        timestamp: new Date().toISOString()
      })
    }
  }

  const forceReloadSales = () => {
    loadSales()
  }

  return (
    <div className="space-y-4 p-4 bg-gray-50 border rounded-lg">
      <div className="flex items-center gap-2">
        <Bug className="h-5 w-5 text-orange-600" />
        <h3 className="text-lg font-semibold">Store Debug Panel</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Store Data Counts</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Sales:</span>
              <span className="font-mono">{sales.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Customers:</span>
              <span className="font-mono">{customers.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Inventory:</span>
              <span className="font-mono">{inventory.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Purchases:</span>
              <span className="font-mono">{purchases.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Schemes:</span>
              <span className="font-mono">{schemes.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Repairs:</span>
              <span className="font-mono">{repairs.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Loading:</span>
              <span className="font-mono">{isLoading ? 'Yes' : 'No'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              onClick={forceReloadSales} 
              size="sm" 
              className="w-full"
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload Sales
            </Button>
            <Button 
              onClick={testAPIDirectly} 
              size="sm" 
              variant="outline"
              className="w-full"
            >
              <Database className="h-4 w-4 mr-2" />
              Test API Direct
            </Button>
            <Button 
              onClick={() => {
                loadSales()
                loadCustomers()
                loadInventory()
                loadPurchases()
              }} 
              size="sm" 
              variant="secondary"
              className="w-full"
            >
              Reload All Data
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Sample Sales Data</CardTitle>
          </CardHeader>
          <CardContent>
            {sales.length > 0 ? (
              <div className="space-y-2 text-xs">
                <div>
                  <strong>First Sale:</strong>
                  <div className="font-mono bg-gray-100 p-2 rounded mt-1">
                    ID: {sales[0].id.substring(0, 8)}...<br/>
                    Customer: {sales[0].customer?.name || 'N/A'}<br/>
                    Total: ₹{(sales[0].total || 0).toLocaleString()}<br/>
                    Items: {sales[0].items?.length || 0}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                No sales data in store
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {debugInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Store State Raw</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs space-y-2">
            <div>
              <strong>Sales Array:</strong>
              <div className="font-mono bg-gray-100 p-2 rounded mt-1">
                Length: {sales.length}<br/>
                Type: {Array.isArray(sales) ? 'Array' : typeof sales}<br/>
                First Item: {sales.length > 0 ? 'Present' : 'None'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
