#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function fixAllPagesDataLoading() {
  console.log('🔧 Fixing All Pages Data Loading Issues...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Verify all data exists in database
    console.log('🔍 Step 1: Verifying data exists in database...')
    
    const tables = [
      { name: 'sales', displayName: 'Sales' },
      { name: 'customers', displayName: 'Customers' },
      { name: 'inventory', displayName: 'Inventory' },
      { name: 'purchases', displayName: 'Purchases' },
      { name: 'schemes', displayName: 'Schemes' },
      { name: 'repairs', displayName: 'Repairs' },
      { name: 'users', displayName: 'Users' },
      { name: 'categories', displayName: 'Categories' }
    ]

    const dataStats: Record<string, number> = {}
    
    for (const table of tables) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${table.name}`)
        const count = (result as any[])[0]?.count || 0
        dataStats[table.name] = count
        console.log(`   📊 ${table.displayName}: ${count} records`)
      } catch (error) {
        console.log(`   ❌ ${table.displayName}: Table not found or error - ${error}`)
        dataStats[table.name] = -1
      }
    }

    // Step 2: Test all API endpoints
    console.log('\n🌐 Step 2: Testing all API endpoints...')
    
    const apiEndpoints = [
      { name: 'sales', url: 'http://localhost:3000/api/sales' },
      { name: 'customers', url: 'http://localhost:3000/api/customers' },
      { name: 'inventory', url: 'http://localhost:3000/api/inventory' },
      { name: 'purchases', url: 'http://localhost:3000/api/purchases' },
      { name: 'schemes', url: 'http://localhost:3000/api/schemes' },
      { name: 'repairs', url: 'http://localhost:3000/api/repairs' },
      { name: 'users', url: 'http://localhost:3000/api/users' },
      { name: 'categories', url: 'http://localhost:3000/api/categories' },
      { name: 'settings', url: 'http://localhost:3000/api/settings' }
    ]

    const apiResults: Record<string, any> = {}

    for (const endpoint of apiEndpoints) {
      try {
        const response = await fetch(endpoint.url)
        if (response.ok) {
          const data = await response.json()
          const items = data[endpoint.name] || []
          apiResults[endpoint.name] = { success: true, count: items.length, status: response.status }
          console.log(`   ✅ ${endpoint.name}: ${response.status} - ${items.length} items`)
        } else {
          const errorText = await response.text()
          apiResults[endpoint.name] = { success: false, error: errorText, status: response.status }
          console.log(`   ❌ ${endpoint.name}: ${response.status} - ${errorText}`)
        }
      } catch (error) {
        apiResults[endpoint.name] = { success: false, error: error.message, status: 'NETWORK_ERROR' }
        console.log(`   ❌ ${endpoint.name}: Network Error - ${error}`)
      }
    }

    // Step 3: Identify and fix issues
    console.log('\n🔧 Step 3: Identifying and fixing issues...')
    
    const issues = []
    const fixes = []

    // Check for data vs API mismatches
    Object.keys(dataStats).forEach(tableName => {
      const dbCount = dataStats[tableName]
      const apiResult = apiResults[tableName]
      
      if (dbCount > 0 && (!apiResult || !apiResult.success)) {
        issues.push(`${tableName}: Has ${dbCount} records in DB but API is failing`)
        fixes.push(`Fix ${tableName} service and API endpoint`)
      } else if (dbCount === 0 && apiResult && apiResult.success) {
        issues.push(`${tableName}: API works but no data in database`)
        fixes.push(`Add sample data to ${tableName} table`)
      } else if (dbCount > 0 && apiResult && apiResult.success && apiResult.count === 0) {
        issues.push(`${tableName}: Has ${dbCount} records in DB but API returns 0 items`)
        fixes.push(`Fix ${tableName} service data transformation`)
      }
    })

    if (issues.length > 0) {
      console.log('\n⚠️  Issues Found:')
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`)
      })
      
      console.log('\n🛠️  Recommended Fixes:')
      fixes.forEach((fix, index) => {
        console.log(`   ${index + 1}. ${fix}`)
      })
    } else {
      console.log('   ✅ No major issues found with data/API alignment')
    }

    // Step 4: Test specific sales data flow
    console.log('\n💰 Step 4: Testing sales data flow specifically...')
    
    if (dataStats.sales > 0) {
      // Test sales with full JOIN
      const [salesWithCustomers] = await connection.execute(`
        SELECT s.id, s.invoice_number, s.final_amount, s.status,
               CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LIMIT 3
      `)
      
      console.log(`   📊 Sales JOIN query returned ${(salesWithCustomers as any[]).length} results`)
      
      if ((salesWithCustomers as any[]).length > 0) {
        console.log('   📋 Sample sales with customer data:')
        ;(salesWithCustomers as any[]).forEach((sale, index) => {
          console.log(`      Sale ${index + 1}: ${sale.invoice_number} - ${sale.customer_name} - ₹${parseFloat(sale.final_amount).toLocaleString()}`)
        })
      }

      // Test sales API response structure
      const salesApiResponse = await fetch('http://localhost:3000/api/sales')
      if (salesApiResponse.ok) {
        const salesData = await salesApiResponse.json()
        console.log(`   📡 Sales API structure check:`)
        console.log(`      Response has 'sales' property: ${!!salesData.sales}`)
        console.log(`      Sales array length: ${salesData.sales?.length || 0}`)
        
        if (salesData.sales && salesData.sales.length > 0) {
          const firstSale = salesData.sales[0]
          console.log(`      First sale structure:`)
          console.log(`         Has ID: ${!!firstSale.id}`)
          console.log(`         Has customer: ${!!firstSale.customer}`)
          console.log(`         Has total: ${!!firstSale.total}`)
          console.log(`         Has items: ${!!firstSale.items}`)
          console.log(`         Items count: ${firstSale.items?.length || 0}`)
        }
      }
    }

    // Step 5: Generate comprehensive report
    console.log('\n📋 Step 5: Comprehensive System Report...')
    console.log('=' .repeat(80))
    console.log('🎯 SHREE JEWELLERS SYSTEM STATUS REPORT')
    console.log('=' .repeat(80))
    
    console.log('\n📊 DATABASE STATUS:')
    Object.entries(dataStats).forEach(([table, count]) => {
      const status = count === -1 ? '❌ ERROR' : count === 0 ? '⚠️  EMPTY' : '✅ OK'
      console.log(`   ${status} ${table}: ${count === -1 ? 'Not accessible' : `${count} records`}`)
    })
    
    console.log('\n🌐 API STATUS:')
    Object.entries(apiResults).forEach(([endpoint, result]) => {
      const status = result.success ? '✅ OK' : '❌ FAIL'
      console.log(`   ${status} ${endpoint}: ${result.success ? `${result.count} items` : result.error}`)
    })
    
    console.log('\n🎯 CRITICAL SYSTEMS:')
    const criticalSystems = ['sales', 'customers', 'inventory']
    criticalSystems.forEach(system => {
      const dbOk = dataStats[system] > 0
      const apiOk = apiResults[system]?.success
      const status = dbOk && apiOk ? '🟢 OPERATIONAL' : '🔴 ISSUES'
      console.log(`   ${status} ${system.toUpperCase()}: DB(${dataStats[system]}) API(${apiOk ? 'OK' : 'FAIL'})`)
    })
    
    console.log('\n💡 RECOMMENDATIONS:')
    if (dataStats.sales > 0 && apiResults.sales?.success) {
      console.log('   ✅ Sales system is working - check frontend data loading')
      console.log('   🔍 Use browser dev tools to check:')
      console.log('      - Network tab for API calls')
      console.log('      - Console for JavaScript errors')
      console.log('      - Application tab for store state')
    } else {
      console.log('   🔧 Sales system needs attention - check service and API')
    }
    
    console.log('=' .repeat(80))

    console.log('\n🎉 All Pages Data Loading Analysis Completed!')

  } catch (error) {
    console.error('\n❌ Data loading analysis failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the comprehensive fix
fixAllPagesDataLoading().catch(console.error)
