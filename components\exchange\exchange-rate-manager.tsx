"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Edit, Save, X, History, TrendingUp, TrendingDown } from "lucide-react"
import { ExchangeRate, ExchangeRateHistory } from "@/lib/types"
import { formatCurrency, formatDate } from "@/lib/utils"

export function ExchangeRateManager() {
  const [goldRates, setGoldRates] = useState<ExchangeRate[]>([])
  const [silverRates, setSilverRates] = useState<ExchangeRate[]>([])
  const [rateHistory, setRateHistory] = useState<ExchangeRateHistory[]>([])
  const [editingRate, setEditingRate] = useState<string | null>(null)
  const [editValue, setEditValue] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  // Mock data for development - replace with actual API calls
  useEffect(() => {
    const mockGoldRates: ExchangeRate[] = [
      { id: '1', metalType: 'gold', purity: '24K', ratePerGram: 6500, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '2', metalType: 'gold', purity: '22K', ratePerGram: 5950, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '3', metalType: 'gold', purity: '18K', ratePerGram: 4875, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '4', metalType: 'gold', purity: '14K', ratePerGram: 3800, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '5', metalType: 'gold', purity: '916', ratePerGram: 5950, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '6', metalType: 'gold', purity: '750', ratePerGram: 4875, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' }
    ]

    const mockSilverRates: ExchangeRate[] = [
      { id: '7', metalType: 'silver', purity: '999', ratePerGram: 85, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '8', metalType: 'silver', purity: '925', ratePerGram: 78, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' },
      { id: '9', metalType: 'silver', purity: '900', ratePerGram: 76, effectiveDate: '2024-01-31', isActive: true, createdAt: '2024-01-31', updatedAt: '2024-01-31' }
    ]

    const mockHistory: ExchangeRateHistory[] = [
      { id: '1', metalType: 'gold', purity: '22K', oldRate: 5900, newRate: 5950, changeReason: 'Market rate increase', effectiveDate: '2024-01-31', createdAt: '2024-01-31' },
      { id: '2', metalType: 'gold', purity: '24K', oldRate: 6450, newRate: 6500, changeReason: 'Market rate increase', effectiveDate: '2024-01-31', createdAt: '2024-01-31' },
      { id: '3', metalType: 'silver', purity: '999', oldRate: 82, newRate: 85, changeReason: 'Market rate increase', effectiveDate: '2024-01-30', createdAt: '2024-01-30' }
    ]

    setGoldRates(mockGoldRates)
    setSilverRates(mockSilverRates)
    setRateHistory(mockHistory)
  }, [])

  const handleEditRate = (rateId: string, currentRate: number) => {
    setEditingRate(rateId)
    setEditValue(currentRate.toString())
  }

  const handleSaveRate = async (rate: ExchangeRate) => {
    setIsLoading(true)
    try {
      const newRate = parseFloat(editValue)
      if (isNaN(newRate) || newRate <= 0) {
        alert('Please enter a valid rate')
        return
      }

      // Mock API call - replace with actual implementation
      const updatedRate = { ...rate, ratePerGram: newRate, updatedAt: new Date().toISOString() }
      
      if (rate.metalType === 'gold') {
        setGoldRates(prev => prev.map(r => r.id === rate.id ? updatedRate : r))
      } else {
        setSilverRates(prev => prev.map(r => r.id === rate.id ? updatedRate : r))
      }

      // Add to history
      const historyEntry: ExchangeRateHistory = {
        id: `hist_${Date.now()}`,
        metalType: rate.metalType,
        purity: rate.purity,
        oldRate: rate.ratePerGram,
        newRate: newRate,
        changeReason: 'Manual rate update',
        effectiveDate: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
      }
      setRateHistory(prev => [historyEntry, ...prev])

      setEditingRate(null)
      setEditValue('')
    } catch (error) {
      console.error('Error updating rate:', error)
      alert('Failed to update rate')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditingRate(null)
    setEditValue('')
  }

  const getRateChangeIcon = (oldRate?: number, newRate?: number) => {
    if (!oldRate || !newRate) return null
    if (newRate > oldRate) return <TrendingUp className="h-4 w-4 text-green-500" />
    if (newRate < oldRate) return <TrendingDown className="h-4 w-4 text-red-500" />
    return null
  }

  const RateCard = ({ rate }: { rate: ExchangeRate }) => (
    <Card key={rate.id} className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-mono">
              {rate.purity}
            </Badge>
            <span className="text-sm text-muted-foreground">
              Updated: {formatDate(rate.updatedAt)}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {editingRate === rate.id ? (
            <div className="flex items-center gap-2">
              <Input
                type="number"
                step="0.01"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="w-24"
                autoFocus
              />
              <Button
                size="sm"
                onClick={() => handleSaveRate(rate)}
                disabled={isLoading}
              >
                <Save className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelEdit}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold">
                ₹{rate.ratePerGram.toFixed(2)}/g
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleEditRate(rate.id, rate.ratePerGram)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </Card>
  )

  return (
    <div className="space-y-6">
      <Tabs defaultValue="current" className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Current Rates</TabsTrigger>
          <TabsTrigger value="history">Rate History</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-6">
          {/* Gold Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                Gold Exchange Rates
              </CardTitle>
              <CardDescription>
                Current exchange rates for different gold purities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {goldRates.map(rate => (
                  <RateCard key={rate.id} rate={rate} />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Silver Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                Silver Exchange Rates
              </CardTitle>
              <CardDescription>
                Current exchange rates for different silver purities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {silverRates.map(rate => (
                  <RateCard key={rate.id} rate={rate} />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Bulk operations and rate management tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button variant="outline">
                  Import Market Rates
                </Button>
                <Button variant="outline">
                  Export Rate Sheet
                </Button>
                <Button variant="outline">
                  Bulk Update
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Rate Change History
              </CardTitle>
              <CardDescription>
                Track all rate changes and their reasons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {rateHistory.map((entry) => (
                  <div
                    key={entry.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${
                          entry.metalType === 'gold' ? 'bg-yellow-500' : 'bg-gray-400'
                        }`}></div>
                        <Badge variant="outline">{entry.purity}</Badge>
                      </div>
                      <div>
                        <p className="font-medium capitalize">{entry.metalType}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(entry.effectiveDate)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          {entry.oldRate && (
                            <span className="text-sm text-muted-foreground line-through">
                              ₹{entry.oldRate.toFixed(2)}
                            </span>
                          )}
                          <span className="font-semibold">
                            ₹{entry.newRate.toFixed(2)}
                          </span>
                          {getRateChangeIcon(entry.oldRate, entry.newRate)}
                        </div>
                        {entry.changeReason && (
                          <p className="text-xs text-muted-foreground">
                            {entry.changeReason}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {rateHistory.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No rate changes recorded yet
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
