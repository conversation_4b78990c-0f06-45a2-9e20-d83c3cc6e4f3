"use client"

import { create } from "zustand"
import type { InventoryItem, Customer, Sale, Scheme, RepairOrder, Purchase, MetalRates, User } from "./types"
interface Settings {
  businessName: string
  address: string
  phone: string
  email: string
  gstNumber: string
  metalRates: MetalRates
  autoUpdateRates: boolean
  cgstRate: string
  sgstRate: string
  lowStockAlert: boolean
  lowStockThreshold: string
  schemeReminders: boolean
  repairReminders: boolean
  currency: string
  dateFormat: string
  backupFrequency: string
  invoiceTemplate: string
  printLogo: boolean
  printTerms: boolean
  createdAt?: string
  updatedAt?: string
}

interface Store {
  // User Management
  currentUser: User | null
  users: User[]
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  loadUsers: () => void
  addUser: (user: Omit<User, "id" | "createdAt" | "updatedAt">) => Promise<void>
  updateUser: (id: string, user: Partial<User>) => Promise<void>
  deleteUser: (id: string) => Promise<void>
  hasPermission: (module: string, action: string) => boolean

  // Inventory
  inventory: InventoryItem[]
  loadInventory: () => void
  addInventoryItem: (item: Omit<InventoryItem, "id" | "createdAt" | "updatedAt">) => Promise<void>
  updateInventoryItem: (id: string, item: Partial<InventoryItem>) => Promise<void>
  deleteInventoryItem: (id: string) => Promise<void>
  getInventoryItem: (id: string) => InventoryItem | undefined

  // Customers
  customers: Customer[]
  loadCustomers: () => void
  addCustomer: (customer: Omit<Customer, "id" | "createdAt" | "updatedAt">) => Promise<void>
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<void>
  deleteCustomer: (id: string) => Promise<void>
  getCustomer: (id: string) => Customer | undefined

  // Sales
  sales: Sale[]
  loadSales: () => void
  addSale: (sale: Omit<Sale, "id" | "createdAt" | "updatedAt">) => Promise<void>
  updateSale: (id: string, sale: Partial<Sale>) => void
  deleteSale: (id: string) => void
  getSale: (id: string) => Sale | undefined

  // Schemes
  schemes: Scheme[]
  loadSchemes: () => void
  addScheme: (scheme: Omit<Scheme, "id" | "createdAt" | "updatedAt">) => void
  updateScheme: (id: string, scheme: Partial<Scheme>) => void
  deleteScheme: (id: string) => void
  getScheme: (id: string) => Scheme | undefined

  // Repairs
  repairs: RepairOrder[]
  loadRepairs: () => void
  addRepair: (repair: Omit<RepairOrder, "id" | "createdAt" | "updatedAt">) => void
  updateRepair: (id: string, repair: Partial<RepairOrder>) => void
  deleteRepair: (id: string) => void
  getRepair: (id: string) => RepairOrder | undefined

  // Purchases
  purchases: Purchase[]
  loadPurchases: () => void
  addPurchase: (purchase: Omit<Purchase, "id" | "createdAt" | "updatedAt">) => void
  updatePurchase: (id: string, purchase: Partial<Purchase>) => void
  deletePurchase: (id: string) => void
  getPurchase: (id: string) => Purchase | undefined

  // Settings
  settings: Settings
  loadSettings: () => void
  updateSettings: (settings: Partial<Settings>) => Promise<void>
  getSettings: () => Settings
  getMetalRate: (metalType: string, purity: string) => number

  // Data Loading
  loadAllData: () => Promise<void>
  isLoading: boolean
  setLoading: (loading: boolean) => void

  // Export Functions
  exportToPDF: (data: any[], type: string) => void
  exportToExcel: (data: any[], type: string) => void
  printData: (data: any[], type: string) => void
}

// Default settings for fallback
const defaultSettings: Settings = {
  businessName: "Shree Jewellers",
  address: "123 Main Street, Mumbai",
  phone: "+91 98765 43210",
  email: "<EMAIL>",
  gstNumber: "27ABCDE1234F1Z5",
  metalRates: {
    gold: {
      "24K": "700",
      "22K": "642",
      "18K": "525",
      "14K": "408",
    },
    silver: {
      "999": "85",
      "925": "78.5",
    },
  },
  autoUpdateRates: true,
  cgstRate: "1.5",
  sgstRate: "1.5",
  lowStockAlert: true,
  lowStockThreshold: "5",
  schemeReminders: true,
  repairReminders: true,
  currency: "INR",
  dateFormat: "DD/MM/YYYY",
  backupFrequency: "daily",
  invoiceTemplate: "standard",
  printLogo: true,
  printTerms: true,
}

export const useStore = create<Store>((set, get) => ({
  // State
  currentUser: null,
  users: [],
  inventory: [],
  customers: [],
  sales: [],
  schemes: [],
  repairs: [],
  purchases: [],
  settings: defaultSettings,
  isLoading: false,

  // Loading state
  setLoading: (loading: boolean) => set({ isLoading: loading }),

  // User Management
  login: (email: string, password: string) => {
    return fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    })
    .then(response => {
      if (response.ok) {
        return response.json().then(({ user }) => {
          set({ currentUser: user })
          return true
        })
      }
      return false
    })
    .catch(error => {
      console.error('Login error:', error)
      return false
    })
  },

  logout: () => {
    set({ currentUser: null })
  },

  loadUsers: () => {
    fetch('/api/users')
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data) {
          set({ users: data.users })
        }
      })
      .catch(error => console.error('Error loading users:', error))
  },

  addUser: (userData) => {
    return fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({ users: [...state.users, data.user] }))
      }
    })
    .catch(error => {
      console.error('Error adding user:', error)
      throw error
    })
  },

  updateUser: (id, updates) => {
    return fetch(`/api/users/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({
          users: state.users.map((u) => (u.id === id ? data.user : u)),
          currentUser: state.currentUser?.id === id ? data.user : state.currentUser
        }))
      }
    })
    .catch(error => {
      console.error('Error updating user:', error)
      throw error
    })
  },

  deleteUser: (id) => {
    return fetch(`/api/users/${id}`, { method: 'DELETE' })
      .then(response => {
        if (response.ok) {
          set((state) => ({
            users: state.users.filter((user) => user.id !== id)
          }))
        }
      })
      .catch(error => {
        console.error('Error deleting user:', error)
        throw error
      })
  },

  hasPermission: (module: string, action: string) => {
    const currentUser = get().currentUser
    if (!currentUser) return false

    const permission = currentUser.permissions.find((p) => p.module === module)
    return permission ? permission.actions.includes(action as any) : false
  },

  // Inventory Management
  loadInventory: () => {
    fetch('/api/inventory')
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data) {
          set({ inventory: data.inventory })
        }
      })
      .catch(error => console.error('Error loading inventory:', error))
  },

  addInventoryItem: (itemData) => {
    return fetch('/api/inventory', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(itemData)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({ inventory: [...state.inventory, data.item] }))
      }
    })
    .catch(error => {
      console.error('Error adding inventory item:', error)
      throw error
    })
  },

  updateInventoryItem: (id, updates) => {
    return fetch(`/api/inventory/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({
          inventory: state.inventory.map((i) => (i.id === id ? data.item : i))
        }))
      }
    })
    .catch(error => {
      console.error('Error updating inventory item:', error)
      throw error
    })
  },

  deleteInventoryItem: (id) => {
    return fetch(`/api/inventory/${id}`, { method: 'DELETE' })
      .then(response => {
        if (response.ok) {
          set((state) => ({
            inventory: state.inventory.filter((item) => item.id !== id)
          }))
        }
      })
      .catch(error => {
        console.error('Error deleting inventory item:', error)
        throw error
      })
  },

  getInventoryItem: (id) => {
    return get().inventory.find((item) => item.id === id)
  },

  // Customer Management
  loadCustomers: () => {
    fetch('/api/customers')
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data) {
          set({ customers: data.customers })
        }
      })
      .catch(error => console.error('Error loading customers:', error))
  },

  addCustomer: (customerData) => {
    return fetch('/api/customers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(customerData)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({ customers: [...state.customers, data.customer] }))
      }
    })
    .catch(error => {
      console.error('Error adding customer:', error)
      throw error
    })
  },

  updateCustomer: (id, updates) => {
    return fetch(`/api/customers/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({
          customers: state.customers.map((c) => (c.id === id ? data.customer : c))
        }))
      }
    })
    .catch(error => {
      console.error('Error updating customer:', error)
      throw error
    })
  },

  deleteCustomer: (id) => {
    return fetch(`/api/customers/${id}`, { method: 'DELETE' })
      .then(response => {
        if (response.ok) {
          set((state) => ({
            customers: state.customers.filter((customer) => customer.id !== id)
          }))
        }
      })
      .catch(error => {
        console.error('Error deleting customer:', error)
        throw error
      })
  },

  getCustomer: (id) => {
    return get().customers.find((customer) => customer.id === id)
  },

  // Sales Management
  loadSales: () => {
    fetch('/api/sales')
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data) {
          set({ sales: data.sales })
        }
      })
      .catch(error => console.error('Error loading sales:', error))
  },

  addSale: (saleData) => {
    return fetch('/api/sales', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(saleData)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set((state) => ({ sales: [...state.sales, data.sale] }))
        // Reload inventory to reflect stock changes
        get().loadInventory()
      }
    })
    .catch(error => {
      console.error('Error adding sale:', error)
      throw error
    })
  },

  updateSale: async (id, updates) => {
    console.log('Update sale:', id, updates)
  },

  deleteSale: async (id) => {
    console.log('Delete sale:', id)
  },

  getSale: (id) => {
    return get().sales.find((sale) => sale.id === id)
  },

  // Schemes Management (Mock implementations)
  loadSchemes: async () => {
    console.log('Loading schemes...')
  },

  addScheme: async (schemeData) => {
    console.log('Adding scheme:', schemeData)
  },

  updateScheme: async (id, updates) => {
    console.log('Updating scheme:', id, updates)
  },

  deleteScheme: async (id) => {
    console.log('Deleting scheme:', id)
  },

  getScheme: (id) => {
    return get().schemes.find((scheme) => scheme.id === id)
  },

  // Repairs Management (Mock implementations)
  loadRepairs: async () => {
    console.log('Loading repairs...')
  },

  addRepair: async (repairData) => {
    console.log('Adding repair:', repairData)
  },

  updateRepair: async (id, updates) => {
    console.log('Updating repair:', id, updates)
  },

  deleteRepair: async (id) => {
    console.log('Deleting repair:', id)
  },

  getRepair: (id) => {
    return get().repairs.find((repair) => repair.id === id)
  },

  // Purchases Management (Mock implementations)
  loadPurchases: async () => {
    console.log('Loading purchases...')
  },

  addPurchase: async (purchaseData) => {
    console.log('Adding purchase:', purchaseData)
  },

  updatePurchase: async (id, updates) => {
    console.log('Updating purchase:', id, updates)
  },

  deletePurchase: async (id) => {
    console.log('Deleting purchase:', id)
  },

  getPurchase: (id) => {
    return get().purchases.find((purchase) => purchase.id === id)
  },

  // Settings Management
  loadSettings: () => {
    fetch('/api/settings')
      .then(response => response.ok ? response.json() : null)
      .then(data => {
        if (data) {
          set({ settings: data.settings })
        }
      })
      .catch(error => console.error('Error loading settings:', error))
  },

  updateSettings: (updates) => {
    return fetch('/api/settings', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    })
    .then(response => response.ok ? response.json() : null)
    .then(data => {
      if (data) {
        set({ settings: data.settings })
      }
    })
    .catch(error => {
      console.error('Error updating settings:', error)
      throw error
    })
  },

  getSettings: () => {
    return get().settings
  },

  getMetalRate: (metalType: string, purity: string) => {
    const settings = get().settings
    if (metalType === "gold" && settings.metalRates.gold[purity as keyof typeof settings.metalRates.gold]) {
      return parseFloat(settings.metalRates.gold[purity as keyof typeof settings.metalRates.gold])
    }
    if (metalType === "silver" && settings.metalRates.silver[purity as keyof typeof settings.metalRates.silver]) {
      return parseFloat(settings.metalRates.silver[purity as keyof typeof settings.metalRates.silver])
    }
    return 0
  },

  // Data Loading
  loadAllData: () => {
    const store = get()

    // Prevent multiple concurrent loads
    if (store.isLoading) {
      return Promise.resolve()
    }

    store.setLoading(true)

    // Load all data with error handling
    return Promise.allSettled([
      fetch('/api/users')
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data) {
            set({ users: data.users })
          }
        })
        .catch(error => console.error('Error loading users:', error)),

      fetch('/api/inventory')
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data) {
            set({ inventory: data.inventory })
          }
        })
        .catch(error => console.error('Error loading inventory:', error)),

      fetch('/api/customers')
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data) {
            set({ customers: data.customers })
          }
        })
        .catch(error => console.error('Error loading customers:', error)),

      fetch('/api/settings')
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data) {
            set({ settings: { ...defaultSettings, ...data.settings } })
          }
        })
        .catch(error => console.error('Error loading settings:', error))
    ]).finally(() => {
      store.setLoading(false)
    })
  },

  // Export Functions
  exportToPDF: (data: any[], type: string) => {
    // Create PDF content
    const content = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${type} Report - Shree Jewellers</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #d97706; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .total { font-weight: bold; background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">🔸 Shree Jewellers</div>
            <h2>${type} Report</h2>
            <p>Generated on: ${new Date().toLocaleDateString()}</p>
          </div>
          <table>
            ${
              type === "Inventory"
                ? `
              <tr>
                <th>Item Code</th>
                <th>Name</th>
                <th>Category</th>
                <th>Net Weight</th>
                <th>Purity</th>
                <th>Stock</th>
                <th>Value</th>
              </tr>
              ${data
                .map(
                  (item) => `
                <tr>
                  <td>${item.id}</td>
                  <td>${item.name}</td>
                  <td>${item.category}</td>
                  <td>${item.netWeight}g</td>
                  <td>${item.purity}</td>
                  <td>${item.stock}</td>
                  <td>₹${item.currentValue.toLocaleString()}</td>
                </tr>
              `,
                )
                .join("")}
            `
                : type === "Sales"
                  ? `
              <tr>
                <th>Invoice</th>
                <th>Date</th>
                <th>Customer</th>
                <th>Items</th>
                <th>Total</th>
                <th>Status</th>
              </tr>
              ${data
                .map(
                  (sale) => `
                <tr>
                  <td>${sale.id}</td>
                  <td>${sale.date}</td>
                  <td>${sale.customer.name}</td>
                  <td>${sale.items.length}</td>
                  <td>₹${sale.total.toLocaleString()}</td>
                  <td>${sale.status}</td>
                </tr>
              `,
                )
                .join("")}
            `
                  : `
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Details</th>
              </tr>
              ${data
                .map(
                  (item) => `
                <tr>
                  <td>${item.id}</td>
                  <td>${item.name || item.customer?.name || "N/A"}</td>
                  <td>${JSON.stringify(item).substring(0, 100)}...</td>
                </tr>
              `,
                )
                .join("")}
            `
            }
          </table>
        </body>
      </html>
    `

    // Create and download PDF
    const blob = new Blob([content], { type: "text/html" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `${type.toLowerCase()}-report-${new Date().toISOString().split("T")[0]}.html`
    link.click()
    URL.revokeObjectURL(url)
  },

  exportToExcel: (data: any[], type: string) => {
    let csvContent = ""

    if (type === "Inventory") {
      csvContent = "Item Code,Name,Category,Net Weight,Purity,Stock,Value\n"
      csvContent += data
        .map(
          (item) =>
            `${item.id},${item.name},${item.category},${item.netWeight}g,${item.purity},${item.stock},₹${item.currentValue}`,
        )
        .join("\n")
    } else if (type === "Sales") {
      csvContent = "Invoice,Date,Customer,Items,Total,Status\n"
      csvContent += data
        .map(
          (sale) => `${sale.id},${sale.date},${sale.customer.name},${sale.items.length},₹${sale.total},${sale.status}`,
        )
        .join("\n")
    } else {
      csvContent = "ID,Name,Details\n"
      csvContent += data
        .map(
          (item) => `${item.id},${item.name || item.customer?.name || "N/A"},${JSON.stringify(item).substring(0, 50)}`,
        )
        .join("\n")
    }

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `${type.toLowerCase()}-export-${new Date().toISOString().split("T")[0]}.csv`
    link.click()
    URL.revokeObjectURL(url)
  },

  printData: (data: any[], type: string) => {
    const printWindow = window.open("", "_blank")
    if (!printWindow) return

    const content = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${type} Report - Shree Jewellers</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #d97706; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">🔸 Shree Jewellers</div>
            <h2>${type} Report</h2>
            <p>Generated on: ${new Date().toLocaleDateString()}</p>
          </div>
          <table>
            ${
              type === "Inventory"
                ? `
              <tr>
                <th>Item Code</th>
                <th>Name</th>
                <th>Category</th>
                <th>Net Weight</th>
                <th>Purity</th>
                <th>Stock</th>
                <th>Value</th>
              </tr>
              ${data
                .map(
                  (item) => `
                <tr>
                  <td>${item.id}</td>
                  <td>${item.name}</td>
                  <td>${item.category}</td>
                  <td>${item.netWeight}g</td>
                  <td>${item.purity}</td>
                  <td>${item.stock}</td>
                  <td>₹${item.currentValue.toLocaleString()}</td>
                </tr>
              `,
                )
                .join("")}
            `
                : type === "Sales"
                  ? `
              <tr>
                <th>Invoice</th>
                <th>Date</th>
                <th>Customer</th>
                <th>Items</th>
                <th>Total</th>
                <th>Status</th>
              </tr>
              ${data
                .map(
                  (sale) => `
                <tr>
                  <td>${sale.id}</td>
                  <td>${sale.date}</td>
                  <td>${sale.customer.name}</td>
                  <td>${sale.items.length}</td>
                  <td>₹${sale.total.toLocaleString()}</td>
                  <td>${sale.status}</td>
                </tr>
              `,
                )
                .join("")}
            `
                  : `
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Details</th>
              </tr>
              ${data
                .map(
                  (item) => `
                <tr>
                  <td>${item.id}</td>
                  <td>${item.name || item.customer?.name || "N/A"}</td>
                  <td>${JSON.stringify(item).substring(0, 100)}...</td>
                </tr>
              `,
                )
                .join("")}
            `
            }
          </table>
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </script>
        </body>
      </html>
    `

    printWindow.document.write(content)
    printWindow.document.close()
  },
}))
