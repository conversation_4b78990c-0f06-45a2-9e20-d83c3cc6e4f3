#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function verifyTiruppurSystem() {
  console.log('🔍 Verifying JJ Jewellers Tiruppur System Functionality...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Verify database structure
    console.log('🏗️  Step 1: Verifying database structure...')
    
    const [tables] = await connection.execute(`
      SELECT table_name, table_rows
      FROM information_schema.tables 
      WHERE table_schema = ? 
      ORDER BY table_name
    `, [dbConfig.database])

    console.log('📋 Database Tables:')
    console.log('=' .repeat(50))
    ;(tables as any[]).forEach(table => {
      console.log(`   ${table.table_name} | ${table.table_rows || 0} rows`)
    })
    console.log('=' .repeat(50))
    console.log(`   Total Tables: ${(tables as any[]).length}`)

    // Step 2: Verify business configuration
    console.log('\n🏢 Step 2: Verifying business configuration...')
    
    const [businessConfig] = await connection.execute(`
      SELECT business_name, city, state, gst_number, 
             default_making_charge_percentage, default_wastage_percentage, default_margin_percentage
      FROM business_settings LIMIT 1
    `)
    
    if ((businessConfig as any[]).length > 0) {
      const config = (businessConfig as any[])[0]
      console.log(`   ✅ Business: ${config.business_name}`)
      console.log(`   ✅ Location: ${config.city}, ${config.state}`)
      console.log(`   ✅ GST Number: ${config.gst_number}`)
      console.log(`   ✅ Making Charge: ${config.default_making_charge_percentage}%`)
      console.log(`   ✅ Wastage: ${config.default_wastage_percentage}%`)
      console.log(`   ✅ Margin: ${config.default_margin_percentage}%`)
    }

    // Step 3: Verify user accounts
    console.log('\n👥 Step 3: Verifying user accounts...')
    
    const [userStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as admins,
        COUNT(CASE WHEN role = 'manager' THEN 1 END) as managers,
        COUNT(CASE WHEN role = 'sales_staff' THEN 1 END) as sales_staff,
        COUNT(CASE WHEN role = 'accountant' THEN 1 END) as accountants,
        COUNT(CASE WHEN role = 'cashier' THEN 1 END) as cashiers,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users
      FROM users
    `)
    
    const stats = (userStats as any[])[0]
    console.log(`   ✅ Total Users: ${stats.active_users}/${stats.total_users} active`)
    console.log(`   ✅ Admins: ${stats.admins}, Managers: ${stats.managers}`)
    console.log(`   ✅ Sales Staff: ${stats.sales_staff}, Accountants: ${stats.accountants}`)
    console.log(`   ✅ Cashiers: ${stats.cashiers}`)

    // Step 4: Verify metal rates
    console.log('\n💰 Step 4: Verifying metal rates...')
    
    const [metalRates] = await connection.execute(`
      SELECT metal_type, purity, rate_per_gram, rate_per_tola, change_percentage
      FROM metal_rates 
      WHERE is_active = TRUE 
      ORDER BY metal_type, purity
    `)
    
    console.log(`   ✅ Active Metal Rates: ${(metalRates as any[]).length}`)
    ;(metalRates as any[]).forEach(rate => {
      const changeIcon = rate.change_percentage > 0 ? '↗️' : rate.change_percentage < 0 ? '↘️' : '➡️'
      console.log(`      ${rate.metal_type.toUpperCase()} ${rate.purity}: ₹${rate.rate_per_gram}/g (₹${Math.round(rate.rate_per_tola)}/tola) ${changeIcon}${Math.abs(rate.change_percentage)}%`)
    })

    // Step 5: Verify categories
    console.log('\n📂 Step 5: Verifying product categories...')
    
    const [categories] = await connection.execute(`
      SELECT name, category_code, making_charge_value, wastage_percentage, requires_hallmarking
      FROM categories 
      WHERE is_active = TRUE 
      ORDER BY name
    `)
    
    console.log(`   ✅ Active Categories: ${(categories as any[]).length}`)
    ;(categories as any[]).slice(0, 8).forEach(cat => {
      console.log(`      ${cat.name} (${cat.category_code}): ${cat.making_charge_value}% making, ${cat.wastage_percentage}% wastage, Hallmark: ${cat.requires_hallmarking ? 'Yes' : 'No'}`)
    })

    // Step 6: Verify customers
    console.log('\n👥 Step 6: Verifying customer data...')
    
    const [customerStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_customers,
        SUM(total_purchases) as total_purchases,
        COUNT(CASE WHEN loyalty_tier = 'platinum' THEN 1 END) as platinum_customers,
        COUNT(CASE WHEN loyalty_tier = 'gold' THEN 1 END) as gold_customers,
        COUNT(CASE WHEN loyalty_tier = 'silver' THEN 1 END) as silver_customers,
        COUNT(CASE WHEN preferred_language = 'tamil' THEN 1 END) as tamil_customers,
        COUNT(CASE WHEN preferred_language = 'english' THEN 1 END) as english_customers
      FROM customers
    `)
    
    const custStats = (customerStats as any[])[0]
    console.log(`   ✅ Total Customers: ${custStats.total_customers}`)
    console.log(`   ✅ Total Purchases: ₹${custStats.total_purchases?.toLocaleString() || 0}`)
    console.log(`   ✅ Loyalty Tiers: ${custStats.platinum_customers} Platinum, ${custStats.gold_customers} Gold, ${custStats.silver_customers} Silver`)
    console.log(`   ✅ Language Preference: ${custStats.tamil_customers} Tamil, ${custStats.english_customers} English`)

    // Step 7: Verify inventory
    console.log('\n📦 Step 7: Verifying inventory data...')
    
    const [inventoryStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN metal_type = 'gold' THEN 1 END) as gold_items,
        COUNT(CASE WHEN metal_type = 'silver' THEN 1 END) as silver_items,
        SUM(selling_price) as total_inventory_value,
        COUNT(CASE WHEN requires_hallmarking = TRUE THEN 1 END) as hallmark_required,
        COUNT(CASE WHEN is_hallmarked = TRUE THEN 1 END) as hallmarked_items
      FROM inventory
      WHERE status = 'active'
    `)
    
    const invStats = (inventoryStats as any[])[0]
    console.log(`   ✅ Total Active Items: ${invStats.total_items}`)
    console.log(`   ✅ Gold Items: ${invStats.gold_items}, Silver Items: ${invStats.silver_items}`)
    console.log(`   ✅ Total Inventory Value: ₹${invStats.total_inventory_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Hallmarking: ${invStats.hallmarked_items}/${invStats.hallmark_required} items hallmarked`)

    // Step 8: Test professional calculations
    console.log('\n🧮 Step 8: Testing professional calculations...')
    
    const [sampleItem] = await connection.execute(`
      SELECT name, metal_type, purity, gross_weight, stone_weight, net_weight,
             wastage_percentage, wastage_weight, chargeable_weight, purchase_rate,
             metal_amount, making_charges, stone_charges, total_cost, selling_price
      FROM inventory 
      WHERE metal_type = 'gold' AND gross_weight > 20
      LIMIT 1
    `)
    
    if ((sampleItem as any[]).length > 0) {
      const item = (sampleItem as any[])[0]
      console.log(`   📋 Sample Calculation: ${item.name}`)
      console.log(`      Metal: ${item.metal_type.toUpperCase()} ${item.purity}`)
      console.log(`      Gross Weight: ${item.gross_weight}g, Stone Weight: ${item.stone_weight}g`)
      console.log(`      Net Weight: ${item.net_weight}g, Wastage: ${item.wastage_weight}g (${item.wastage_percentage}%)`)
      console.log(`      Chargeable Weight: ${item.chargeable_weight}g`)
      console.log(`      Metal Amount: ₹${item.metal_amount?.toLocaleString()} (₹${item.purchase_rate}/g)`)
      console.log(`      Making Charges: ₹${item.making_charges?.toLocaleString()}`)
      console.log(`      Stone Charges: ₹${item.stone_charges?.toLocaleString()}`)
      console.log(`      Total Cost: ₹${item.total_cost?.toLocaleString()}`)
      console.log(`      Selling Price: ₹${item.selling_price?.toLocaleString()}`)
    }

    // Step 9: Generate system summary
    console.log('\n📊 Step 9: System summary...')
    
    const systemSummary = {
      database: dbConfig.database,
      tables: (tables as any[]).length,
      users: stats.total_users,
      customers: custStats.total_customers,
      inventory_items: invStats.total_items,
      inventory_value: invStats.total_inventory_value,
      metal_rates: (metalRates as any[]).length,
      categories: (categories as any[]).length,
      status: 'fully_operational'
    }

    console.log('\n🎉 JJ Jewellers Tiruppur System Verification Completed!')

    console.log('\n📊 COMPREHENSIVE SYSTEM STATUS:')
    console.log('=' .repeat(70))
    console.log(`🗄️  Database: ${systemSummary.database}`)
    console.log(`📋 Tables: ${systemSummary.tables}`)
    console.log(`👥 Users: ${systemSummary.users}`)
    console.log(`🛍️  Customers: ${systemSummary.customers}`)
    console.log(`📦 Inventory Items: ${systemSummary.inventory_items}`)
    console.log(`💰 Inventory Value: ₹${systemSummary.inventory_value?.toLocaleString() || 0}`)
    console.log(`💎 Metal Rates: ${systemSummary.metal_rates}`)
    console.log(`📂 Categories: ${systemSummary.categories}`)
    console.log(`🟢 Status: ${systemSummary.status.toUpperCase()}`)
    console.log('=' .repeat(70))

    console.log('\n🚀 SYSTEM READY FOR TESTING:')
    console.log('✅ Complete professional database schema')
    console.log('✅ Tiruppur-specific business configuration')
    console.log('✅ Multi-role user management system')
    console.log('✅ Comprehensive customer database')
    console.log('✅ Real-time metal rate management')
    console.log('✅ Professional jewelry inventory')
    console.log('✅ Advanced pricing calculations')
    console.log('✅ Hallmarking and compliance tracking')
    console.log('✅ Tamil and English language support')

    console.log('\n🔐 DEFAULT LOGIN CREDENTIALS:')
    console.log('Username: admin')
    console.log('Password: admin123')
    console.log('⚠️  IMPORTANT: Change password on first login!')

  } catch (error) {
    console.error('\n❌ Tiruppur system verification failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the Tiruppur system verification
verifyTiruppurSystem().catch(console.error)
