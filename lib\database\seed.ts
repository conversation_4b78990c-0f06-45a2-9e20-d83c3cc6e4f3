import {
  userService,
  customerService,
  inventoryService,
  categoryService,
  settingsService
} from './services'
import { MigrationManager } from './migrations'
import { testConnection } from './config'

export class DatabaseSeeder {
  private migrationManager = new MigrationManager()

  async initializeDatabase(): Promise<void> {
    console.log('Initializing database...')
    
    // Test connection
    const isConnected = await testConnection()
    if (!isConnected) {
      throw new Error('Database connection failed')
    }

    // Run migrations
    await this.migrationManager.runMigrations()
    
    // Seed initial data
    await this.seedInitialData()
    
    console.log('Database initialization completed successfully')
  }

  async seedInitialData(): Promise<void> {
    console.log('Seeding initial data...')
    
    try {
      // Check if data already exists
      const existingUsers = await userService.findAll()
      if (existingUsers.length > 0) {
        console.log('Data already exists, skipping seed')
        return
      }

      // Seed users
      await this.seedUsers()

      // Seed categories
      await this.seedCategories()

      // Seed customers
      await this.seedCustomers()

      // Seed inventory
      await this.seedInventory()

      // Seed settings
      await this.seedSettings()
      
      console.log('Initial data seeded successfully')
    } catch (error) {
      console.error('Error seeding data:', error)
      throw error
    }
  }

  private async seedUsers(): Promise<void> {
    const users = [
      {
        name: "Admin User",
        email: "<EMAIL>",
        password: "admin123",
        role: "admin" as const,
        permissions: [
          { module: "inventory", actions: ["create", "read", "update", "delete", "export"] },
          { module: "customers", actions: ["create", "read", "update", "delete", "export"] },
          { module: "sales", actions: ["create", "read", "update", "delete", "export"] },
          { module: "purchases", actions: ["create", "read", "update", "delete", "export"] },
          { module: "schemes", actions: ["create", "read", "update", "delete", "export"] },
          { module: "repairs", actions: ["create", "read", "update", "delete", "export"] },
          { module: "categories", actions: ["create", "read", "update", "delete", "export"] },
          { module: "reports", actions: ["read", "export"] },
          { module: "settings", actions: ["read", "update"] },
        ]
      },
      {
        name: "Manager User",
        email: "<EMAIL>",
        password: "manager123",
        role: "manager" as const,
        permissions: [
          { module: "inventory", actions: ["create", "read", "update", "export"] },
          { module: "customers", actions: ["create", "read", "update", "export"] },
          { module: "sales", actions: ["create", "read", "update", "export"] },
          { module: "schemes", actions: ["create", "read", "update"] },
          { module: "repairs", actions: ["create", "read", "update"] },
          { module: "categories", actions: ["create", "read", "update", "export"] },
          { module: "reports", actions: ["read", "export"] },
        ]
      },
      {
        name: "Staff User",
        email: "<EMAIL>",
        password: "staff123",
        role: "staff" as const,
        permissions: [
          { module: "inventory", actions: ["read"] },
          { module: "customers", actions: ["create", "read", "update"] },
          { module: "sales", actions: ["create", "read"] },
          { module: "repairs", actions: ["create", "read", "update"] },
        ]
      }
    ]

    for (const userData of users) {
      await userService.create(userData)
      console.log(`Created user: ${userData.email}`)
    }
  }

  private async seedCategories(): Promise<void> {
    const categories = [
      {
        name: "Rings",
        description: "Wedding rings, engagement rings, and fashion rings",
        isActive: true,
        sortOrder: 1,
        tags: ["wedding", "engagement", "fashion"],
        makingChargePercentage: 15,
        wastagePercentage: 2,
      },
      {
        name: "Necklaces",
        description: "Gold and silver necklaces, chains, and pendants",
        isActive: true,
        sortOrder: 2,
        tags: ["chains", "pendants", "traditional"],
        makingChargePercentage: 12,
        wastagePercentage: 1.5,
      },
      {
        name: "Earrings",
        description: "Studs, hoops, and traditional earrings",
        isActive: true,
        sortOrder: 3,
        tags: ["studs", "hoops", "traditional", "modern"],
        makingChargePercentage: 18,
        wastagePercentage: 2.5,
      },
      {
        name: "Bracelets",
        description: "Gold and silver bracelets and bangles",
        isActive: true,
        sortOrder: 4,
        tags: ["bangles", "charm", "tennis"],
        makingChargePercentage: 14,
        wastagePercentage: 2,
      },
      {
        name: "Pendants",
        description: "Religious and fashion pendants",
        isActive: true,
        sortOrder: 5,
        tags: ["religious", "fashion", "custom"],
        makingChargePercentage: 16,
        wastagePercentage: 1.8,
      },
      {
        name: "Chains",
        description: "Gold and silver chains of various designs",
        isActive: true,
        sortOrder: 6,
        tags: ["rope", "box", "figaro", "curb"],
        makingChargePercentage: 10,
        wastagePercentage: 1,
      },
      {
        name: "Anklets",
        description: "Traditional and modern anklets",
        isActive: true,
        sortOrder: 7,
        tags: ["traditional", "modern", "charm"],
        makingChargePercentage: 13,
        wastagePercentage: 1.5,
      },
      {
        name: "Sets",
        description: "Complete jewelry sets including necklace, earrings, and more",
        isActive: true,
        sortOrder: 8,
        tags: ["bridal", "party", "traditional", "complete"],
        makingChargePercentage: 20,
        wastagePercentage: 3,
      },
    ]

    for (const categoryData of categories) {
      await categoryService.create(categoryData)
      console.log(`Created category: ${categoryData.name}`)
    }
  }

  private async seedCustomers(): Promise<void> {
    const customers = [
      {
        name: "Priya Sharma",
        phone: "+91 98765 43210",
        email: "<EMAIL>",
        address: "123 Main Street, Mumbai",
        totalPurchases: 245000,
        lastVisit: "2024-01-15"
      },
      {
        name: "Rajesh Kumar",
        phone: "+91 87654 32109",
        email: "<EMAIL>",
        address: "456 Park Avenue, Delhi",
        totalPurchases: 185000,
        lastVisit: "2024-01-15"
      },
      {
        name: "Anita Patel",
        phone: "+91 76543 21098",
        email: "<EMAIL>",
        address: "789 Garden Road, Pune",
        totalPurchases: 125000,
        lastVisit: "2024-01-10"
      }
    ]

    for (const customerData of customers) {
      await customerService.create(customerData)
      console.log(`Created customer: ${customerData.name}`)
    }
  }

  private async seedInventory(): Promise<void> {
    const inventory = [
      {
        name: "Gold Necklace 22K",
        category: "Necklace",
        metalType: "gold",
        grossWeight: 25.5,
        stoneWeight: 2.5,
        netWeight: 23.0,
        stoneAmount: 15000,
        purity: "22K",
        makingCharges: 8500,
        currentValue: 189750,
        stock: 3,
        stoneDetails: "Diamond",
        description: "Beautiful gold necklace with diamond stones"
      },
      {
        name: "Diamond Earrings",
        category: "Earrings",
        metalType: "gold",
        grossWeight: 8.2,
        stoneWeight: 1.8,
        netWeight: 6.4,
        stoneAmount: 45000,
        purity: "18K",
        makingCharges: 15000,
        currentValue: 125000,
        stock: 5,
        stoneDetails: "Diamond",
        description: "Elegant diamond earrings"
      },
      {
        name: "Silver Bracelet",
        category: "Bracelet",
        metalType: "silver",
        grossWeight: 15.0,
        stoneWeight: 0,
        netWeight: 15.0,
        stoneAmount: 0,
        purity: "925",
        makingCharges: 2500,
        currentValue: 8500,
        stock: 8,
        stoneDetails: "",
        description: "Stylish silver bracelet"
      },
      {
        name: "Gold Ring 18K",
        category: "Ring",
        metalType: "gold",
        grossWeight: 4.5,
        stoneWeight: 0.5,
        netWeight: 4.0,
        stoneAmount: 8000,
        purity: "18K",
        makingCharges: 3500,
        currentValue: 35000,
        stock: 12,
        stoneDetails: "Ruby",
        description: "Elegant gold ring with ruby stone"
      }
    ]

    for (const itemData of inventory) {
      await inventoryService.create(itemData)
      console.log(`Created inventory item: ${itemData.name}`)
    }
  }

  private async seedSettings(): Promise<void> {
    // Settings will be created with default values automatically
    const settings = await settingsService.getSettings()
    console.log('Settings initialized:', settings.businessName)
  }

  async resetDatabase(): Promise<void> {
    console.log('Resetting database...')
    await this.migrationManager.resetDatabase()
    await this.initializeDatabase()
    console.log('Database reset completed')
  }
}

export const databaseSeeder = new DatabaseSeeder()
