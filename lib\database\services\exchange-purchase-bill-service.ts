import { BaseService } from '../base-service'
import { ExchangePurchaseBill, ExchangeTransaction, BillSequence } from '../../types'
import { RowDataPacket } from 'mysql2'

export class ExchangePurchaseBillService extends BaseService<ExchangePurchaseBill> {
  protected tableName = 'exchange_purchase_bills'

  // Generate bill number
  private async generateBillNumber(): Promise<string> {
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
    
    // Get or create sequence
    let sequenceSql = `
      SELECT current_number FROM bill_sequences 
      WHERE sequence_type = 'exchange_purchase' AND financial_year = ?
    `
    let sequenceResult = await this.executeQuery<RowDataPacket[]>(sequenceSql, [financialYear])
    
    let currentNumber = 1
    if (sequenceResult.length > 0) {
      currentNumber = sequenceResult[0].current_number + 1
      // Update sequence
      const updateSql = `
        UPDATE bill_sequences 
        SET current_number = ?, updated_at = ? 
        WHERE sequence_type = 'exchange_purchase' AND financial_year = ?
      `
      await this.executeUpdate(updateSql, [currentNumber, new Date().toISOString(), financialYear])
    } else {
      // Create new sequence
      const insertSql = `
        INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) 
        VALUES (?, 'exchange_purchase', 'EPB', ?, ?)
      `
      await this.executeUpdate(insertSql, [this.generateId(), currentNumber, financialYear])
    }
    
    return `EPB/${financialYear}/${String(currentNumber).padStart(4, '0')}`
  }

  // Create purchase bill from exchange transaction
  async createFromExchangeTransaction(
    exchangeTransaction: ExchangeTransaction,
    cgstRate: number = 1.5,
    sgstRate: number = 1.5
  ): Promise<ExchangePurchaseBill> {
    const connection = await this.pool.getConnection()
    
    try {
      await connection.beginTransaction()
      
      const billNumber = await this.generateBillNumber()
      const billId = this.generateId()
      const now = new Date().toISOString()
      const billDate = new Date().toISOString().split('T')[0]
      
      // Calculate tax amounts
      const cgstAmount = (exchangeTransaction.totalAmount * cgstRate) / 100
      const sgstAmount = (exchangeTransaction.totalAmount * sgstRate) / 100
      const totalWithTax = exchangeTransaction.totalAmount + cgstAmount + sgstAmount
      
      // Create purchase bill
      const billData = {
        id: billId,
        billNumber,
        exchangeTransactionId: exchangeTransaction.id,
        customerId: exchangeTransaction.customerId,
        billDate,
        totalAmount: exchangeTransaction.totalAmount,
        cgstAmount,
        sgstAmount,
        totalWithTax,
        paymentMethod: exchangeTransaction.paymentMethod,
        paymentStatus: 'pending' as const,
        notes: `Purchase bill for exchange transaction ${exchangeTransaction.transactionNumber}`,
        createdAt: now,
        updatedAt: now
      }
      
      const transformedData = this.transformKeysToSnake(billData)
      const keys = Object.keys(transformedData)
      const values = Object.values(transformedData)
      const placeholders = keys.map(() => '?').join(', ')
      
      const billSql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
      await connection.execute(billSql, values)
      
      // Update exchange transaction
      const updateExchangeSql = `
        UPDATE exchange_transactions 
        SET purchase_bill_id = ?, purchase_bill_number = ?, purchase_bill_generated = true, 
            purchase_bill_date = ?, updated_at = ?
        WHERE id = ?
      `
      await connection.execute(updateExchangeSql, [
        billId, billNumber, billDate, now, exchangeTransaction.id
      ])
      
      // Add audit trail
      const auditSql = `
        INSERT INTO exchange_audit_trail 
        (id, exchange_transaction_id, action_type, action_description, new_values, related_bill_id, performed_at)
        VALUES (?, ?, 'billed', ?, ?, ?, ?)
      `
      await connection.execute(auditSql, [
        this.generateId(),
        exchangeTransaction.id,
        `Purchase bill ${billNumber} generated for exchange transaction`,
        JSON.stringify({ billNumber, totalWithTax }),
        billId,
        now
      ])
      
      await connection.commit()
      
      // Return the created bill with exchange transaction data
      return this.findById(billId) as Promise<ExchangePurchaseBill>
      
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  // Override findById to include exchange transaction data
  async findById(id: string): Promise<ExchangePurchaseBill | null> {
    const sql = `
      SELECT 
        epb.*,
        et.transaction_number,
        et.transaction_date,
        et.total_amount as exchange_total,
        c.name as customer_name,
        c.phone as customer_phone,
        c.email as customer_email,
        c.address as customer_address
      FROM ${this.tableName} epb
      JOIN exchange_transactions et ON epb.exchange_transaction_id = et.id
      LEFT JOIN customers c ON epb.customer_id = c.id
      WHERE epb.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    const bill = this.transformKeys(rows[0]) as ExchangePurchaseBill
    
    // Get exchange transaction details
    const exchangeSql = `
      SELECT et.*, 
             GROUP_CONCAT(
               CONCAT(ei.item_description, '|', ei.metal_type, '|', ei.purity, '|', 
                      ei.gross_weight, '|', ei.net_weight, '|', ei.rate_per_gram, '|', ei.amount)
               SEPARATOR ';;'
             ) as items_data
      FROM exchange_transactions et
      LEFT JOIN exchange_items ei ON et.id = ei.transaction_id
      WHERE et.id = ?
      GROUP BY et.id
    `
    const exchangeRows = await this.executeQuery<RowDataPacket[]>(exchangeSql, [bill.exchangeTransactionId])
    
    if (exchangeRows.length > 0) {
      const exchangeData = exchangeRows[0]
      bill.exchangeTransaction = {
        ...this.transformKeys(exchangeData),
        items: exchangeData.items_data ? exchangeData.items_data.split(';;').map((itemStr: string) => {
          const [description, metalType, purity, grossWeight, netWeight, rate, amount] = itemStr.split('|')
          return {
            itemDescription: description,
            metalType,
            purity,
            grossWeight: parseFloat(grossWeight),
            netWeight: parseFloat(netWeight),
            ratePerGram: parseFloat(rate),
            amount: parseFloat(amount)
          }
        }) : []
      }
    }
    
    return bill
  }

  // Get bills by date range
  async findByDateRange(startDate: string, endDate: string): Promise<ExchangePurchaseBill[]> {
    const sql = `
      SELECT epb.*, et.transaction_number, c.name as customer_name
      FROM ${this.tableName} epb
      JOIN exchange_transactions et ON epb.exchange_transaction_id = et.id
      LEFT JOIN customers c ON epb.customer_id = c.id
      WHERE epb.bill_date BETWEEN ? AND ?
      ORDER BY epb.bill_date DESC, epb.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [startDate, endDate])
    return rows.map(row => this.transformKeys(row) as ExchangePurchaseBill)
  }

  // Get bills by customer
  async findByCustomer(customerId: string): Promise<ExchangePurchaseBill[]> {
    const sql = `
      SELECT epb.*, et.transaction_number
      FROM ${this.tableName} epb
      JOIN exchange_transactions et ON epb.exchange_transaction_id = et.id
      WHERE epb.customer_id = ?
      ORDER BY epb.bill_date DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [customerId])
    return rows.map(row => this.transformKeys(row) as ExchangePurchaseBill)
  }

  // Update payment status
  async updatePaymentStatus(id: string, status: 'pending' | 'paid' | 'partial'): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET payment_status = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [status, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  // Get bill statistics
  async getBillStats(startDate?: string, endDate?: string): Promise<{
    totalBills: number
    totalAmount: number
    totalWithTax: number
    paidBills: number
    pendingBills: number
    averageBillValue: number
  }> {
    let whereClause = ''
    const params: any[] = []
    
    if (startDate && endDate) {
      whereClause = 'WHERE bill_date BETWEEN ? AND ?'
      params.push(startDate, endDate)
    }
    
    const sql = `
      SELECT 
        COUNT(*) as total_bills,
        SUM(total_amount) as total_amount,
        SUM(total_with_tax) as total_with_tax,
        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_bills,
        SUM(CASE WHEN payment_status = 'pending' THEN 1 ELSE 0 END) as pending_bills,
        AVG(total_with_tax) as average_bill_value
      FROM ${this.tableName}
      ${whereClause}
    `
    
    const result = await this.executeQuery<RowDataPacket[]>(sql, params)
    const stats = result[0]
    
    return {
      totalBills: stats?.total_bills || 0,
      totalAmount: stats?.total_amount || 0,
      totalWithTax: stats?.total_with_tax || 0,
      paidBills: stats?.paid_bills || 0,
      pendingBills: stats?.pending_bills || 0,
      averageBillValue: stats?.average_bill_value || 0
    }
  }

  // Generate bill data for PDF/print
  async getBillForPrint(id: string): Promise<any> {
    const bill = await this.findById(id)
    if (!bill) return null
    
    return {
      bill,
      items: bill.exchangeTransaction?.items || [],
      customer: bill.customer,
      totals: {
        subtotal: bill.totalAmount,
        cgst: bill.cgstAmount,
        sgst: bill.sgstAmount,
        total: bill.totalWithTax
      }
    }
  }
}
