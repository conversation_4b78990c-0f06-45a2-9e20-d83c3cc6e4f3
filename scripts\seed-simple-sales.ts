#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedSimpleSales() {
  console.log('🛍️  Seeding Simple Sales Data for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Get admin user ID
    const [adminUser] = await connection.execute('SELECT id FROM users WHERE username = "admin" LIMIT 1')
    const adminId = (adminUser as any[])[0]?.id

    // Get first customer ID
    const [firstCustomer] = await connection.execute('SELECT id, first_name, last_name, phone FROM customers LIMIT 1')
    const customer = (firstCustomer as any[])[0]

    // Get current financial year
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`

    console.log('🛒 Creating simple sales transactions...')
    
    // Create simple sales transactions
    const sales = [
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/001`,
        subtotal: 42000.00,
        discount_amount: 2100.00,
        packing_charges: 200.00
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/002`,
        subtotal: 85000.00,
        discount_amount: 2550.00,
        packing_charges: 300.00
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/003`,
        subtotal: 420000.00,
        discount_amount: 8400.00,
        packing_charges: 500.00
      }
    ]

    // Insert sales transactions
    for (const sale of sales) {
      await connection.execute(`
        INSERT INTO sales (
          id, invoice_number, invoice_date, customer_id, customer_name, customer_phone,
          sale_type, sale_category, payment_mode, subtotal, discount_type, discount_percentage,
          discount_amount, packing_charges, payment_status, paid_amount, status,
          sales_person_id, cashier_id, notes, loyalty_points_earned, created_by, created_at, updated_at
        ) VALUES (?, ?, CURDATE(), ?, ?, ?, 'cash', 'retail', 'cash', ?, 'percentage', 5.00, ?, ?, 'paid', ?, 'confirmed', ?, ?, 'Sample sale transaction', ?, ?, NOW(), NOW())
      `, [
        sale.id, sale.invoice_number, customer?.id || null, customer?.first_name + ' ' + customer?.last_name || 'Walk-in Customer',
        customer?.phone || '+91-9999999999', sale.subtotal, sale.discount_amount, sale.packing_charges,
        sale.subtotal - sale.discount_amount + sale.packing_charges, adminId, adminId,
        Math.floor((sale.subtotal - sale.discount_amount) / 1000), adminId
      ])
    }
    console.log(`   ✅ Created ${sales.length} sales transactions`)

    // Create simple sale items
    console.log('\n📦 Creating sale items...')
    
    const saleItems = [
      {
        sale_id: sales[0].id,
        item_name: 'Gold Earrings Stud Simple',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 4.500,
        rate_per_gram: 6740.00,
        making_charges: 8000.00
      },
      {
        sale_id: sales[1].id,
        item_name: 'Gold Diamond Ring Designer',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 6.200,
        rate_per_gram: 5510.00,
        making_charges: 15000.00
      },
      {
        sale_id: sales[2].id,
        item_name: 'Gold Chain Necklace Heavy',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 45.000,
        rate_per_gram: 6740.00,
        making_charges: 60000.00
      }
    ]

    // Insert sale items
    for (let i = 0; i < saleItems.length; i++) {
      const item = saleItems[i]
      await connection.execute(`
        INSERT INTO sale_items (
          id, sale_id, line_number, item_code, item_name, description,
          metal_type, purity, gross_weight, stone_weight, wastage_percentage,
          rate_per_gram, making_charges, stone_charges, other_charges,
          discount_percentage, discount_amount, quantity, hsn_code, status,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0.000, 8.00, ?, ?, 0.00, 0.00, 0.00, 0.00, 1, '71131900', 'active', NOW(), NOW())
      `, [
        randomUUID(), item.sale_id, i + 1, `ITEM${String(i + 1).padStart(3, '0')}`,
        item.item_name, item.item_name, item.metal_type, item.purity,
        item.gross_weight, item.rate_per_gram, item.making_charges
      ])
    }
    console.log(`   ✅ Created ${saleItems.length} sale items`)

    console.log('\n🎉 Simple Sales Data Seeded Successfully!')

    // Show summary
    const [salesSummary] = await connection.execute(`
      SELECT 
        COUNT(*) as total_sales,
        SUM(subtotal) as total_subtotal,
        SUM(discount_amount) as total_discount,
        SUM(final_amount) as total_final_amount
      FROM sales
    `)
    
    const summary = (salesSummary as any[])[0]
    console.log('\n📊 Sales Summary:')
    console.log(`   Total Sales: ${summary.total_sales}`)
    console.log(`   Total Subtotal: ₹${summary.total_subtotal?.toLocaleString() || 0}`)
    console.log(`   Total Discount: ₹${summary.total_discount?.toLocaleString() || 0}`)
    console.log(`   Total Final Amount: ₹${summary.total_final_amount?.toLocaleString() || 0}`)

  } catch (error) {
    console.error('\n❌ Simple sales data seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the simple sales data seeding
seedSimpleSales().catch(console.error)
