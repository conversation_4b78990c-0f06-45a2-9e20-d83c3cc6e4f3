"use client"

import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Settings, User, Bell, Database, Printer, Shield, Check, TrendingUp } from "lucide-react"
import { useState, useEffect } from "react"
import { useStore } from "@/lib/store"
import { toast } from "sonner"

export function SettingsPanel() {
  const { settings, updateSettings } = useStore()
  const [localSettings, setLocalSettings] = useState(settings)
  const [hasChanges, setHasChanges] = useState(false)

  // Update local settings when store settings change
  useEffect(() => {
    setLocalSettings(settings)
    setHasChanges(false)
  }, [settings])

  // Track changes
  useEffect(() => {
    const hasChanged = JSON.stringify(localSettings) !== JSON.stringify(settings)
    setHasChanges(hasChanged)
  }, [localSettings, settings])

  const handleSave = () => {
    updateSettings(localSettings)
    toast.success("Settings saved successfully!")
    setHasChanges(false)
  }

  const handleReset = () => {
    const defaultSettings = {
      businessName: "Shree Jewellers",
      address: "123 Main Street, Mumbai",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      gstNumber: "27ABCDE1234F1Z5",
      metalRates: {
        gold: {
          "22K": "642",
          "18K": "525",
        },
        silver: {
          "925": "78.5",
        },
      },
      autoUpdateRates: true,
      cgstRate: "1.5",
      sgstRate: "1.5",
      lowStockAlert: true,
      lowStockThreshold: "5",
      schemeReminders: true,
      repairReminders: true,
      currency: "INR",
      dateFormat: "DD/MM/YYYY",
      backupFrequency: "daily",
      invoiceTemplate: "standard",
      printLogo: true,
      printTerms: true,
    }
    setLocalSettings(defaultSettings)
    updateSettings(defaultSettings)
    toast.success("Settings reset to defaults!")
  }

  const handleBackup = () => {
    // Create backup data
    const backupData = {
      settings,
      timestamp: new Date().toISOString(),
      version: "1.0",
    }

    // Create and download backup file
    const dataStr = JSON.stringify(backupData, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `jewellers-backup-${new Date().toISOString().split("T")[0]}.json`
    link.click()
    URL.revokeObjectURL(url)

    toast.success("Data backup downloaded!")
  }

  const handleRestore = () => {
    const input = document.createElement("input")
    input.type = "file"
    input.accept = ".json"
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const backupData = JSON.parse(e.target?.result as string)
            if (backupData.settings) {
              updateSettings(backupData.settings)
              toast.success("Data restored successfully!")
            } else {
              toast.error("Invalid backup file format!")
            }
          } catch (error) {
            toast.error("Failed to restore data!")
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  const handleNumericChange = (field: string, value: string) => {
    setLocalSettings({ ...localSettings, [field]: value })
  }

  const handleChange = (field: string, value: any) => {
    setLocalSettings({ ...localSettings, [field]: value })
  }

  const handleMetalRateChange = (metalType: "gold" | "silver", purity: string, value: string) => {
    const updatedRates = {
      ...localSettings.metalRates,
      [metalType]: {
        ...localSettings.metalRates[metalType],
        [purity]: value,
      },
    }

    // Auto-calculate 18K gold rate when 22K is updated
    if (metalType === "gold" && purity === "22K" && value) {
      const gold22KRate = parseFloat(value)
      if (!isNaN(gold22KRate)) {
        // 18K gold is 75% pure vs 22K which is 91.6% pure
        // So 18K rate = 22K rate × (75/91.6) ≈ 22K rate × 0.8188
        const gold18KRate = Math.round(gold22KRate * 0.8188)
        updatedRates.gold["18K"] = gold18KRate.toString()
      }
    }

    setLocalSettings({
      ...localSettings,
      metalRates: updatedRates,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
          <p className="text-muted-foreground">Manage your application settings and preferences</p>
        </div>
        {hasChanges && (
          <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-md">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
            Unsaved changes
          </div>
        )}
      </div>

      <Tabs defaultValue="business" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="rates">Rates</TabsTrigger>
          <TabsTrigger value="notifications">Alerts</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="print">Print</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
        </TabsList>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Business Information
              </CardTitle>
              <CardDescription>Update your business details and contact information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input
                    id="businessName"
                    value={localSettings.businessName}
                    onChange={(e) => handleChange("businessName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number</Label>
                  <Input
                    id="gstNumber"
                    value={localSettings.gstNumber}
                    onChange={(e) => handleChange("gstNumber", e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={localSettings.address}
                  onChange={(e) => handleChange("address", e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={localSettings.phone}
                    onChange={(e) => handleChange("phone", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={localSettings.email}
                    onChange={(e) => handleChange("email", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Metal Rates
              </CardTitle>
              <CardDescription>Configure current metal rates for different purities</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Gold Rates */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <h4 className="font-semibold text-lg">Gold Rates (per gram)</h4>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="gold22k">22K Gold (91.6%)</Label>
                    <Input
                      id="gold22k"
                      type="number"
                      value={localSettings.metalRates.gold["22K"]}
                      onChange={(e) => handleMetalRateChange("gold", "22K", e.target.value)}
                      placeholder="642"
                    />
                    <p className="text-xs text-muted-foreground">Primary rate - 18K will auto-calculate</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="gold18k">18K Gold (75%)</Label>
                    <Input
                      id="gold18k"
                      type="number"
                      value={localSettings.metalRates.gold["18K"]}
                      onChange={(e) => handleMetalRateChange("gold", "18K", e.target.value)}
                      placeholder="525"
                    />
                    <p className="text-xs text-muted-foreground">Auto-calculated from 22K rate</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Silver Rates */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                  <h4 className="font-semibold text-lg">Silver Rates (per gram)</h4>
                </div>
                <div className="grid grid-cols-1 gap-4 max-w-md">
                  <div className="space-y-2">
                    <Label htmlFor="silver925">925 Silver (92.5% - Sterling Silver)</Label>
                    <Input
                      id="silver925"
                      type="number"
                      value={localSettings.metalRates.silver["925"]}
                      onChange={(e) => handleMetalRateChange("silver", "925", e.target.value)}
                      placeholder="78.5"
                    />
                    <p className="text-xs text-muted-foreground">Standard jewelry silver purity</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="flex items-center space-x-2">
                <Switch
                  id="autoUpdate"
                  checked={localSettings.autoUpdateRates}
                  onCheckedChange={(checked) => handleChange("autoUpdateRates", checked)}
                />
                <Label htmlFor="autoUpdate">Auto-update rates from market</Label>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cgst">CGST Rate (%)</Label>
                  <Input
                    id="cgst"
                    type="number"
                    step="0.1"
                    value={localSettings.cgstRate}
                    onChange={(e) => handleNumericChange("cgstRate", e.target.value)}
                    placeholder="1.5"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sgst">SGST Rate (%)</Label>
                  <Input
                    id="sgst"
                    type="number"
                    step="0.1"
                    value={localSettings.sgstRate}
                    onChange={(e) => handleNumericChange("sgstRate", e.target.value)}
                    placeholder="1.5"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>Configure alerts and reminders for your business</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Low Stock Alerts</Label>
                  <p className="text-sm text-muted-foreground">Get notified when items are running low</p>
                </div>
                <Switch
                  checked={localSettings.lowStockAlert}
                  onCheckedChange={(checked) => handleChange("lowStockAlert", checked)}
                />
              </div>

              {localSettings.lowStockAlert && (
                <div className="space-y-2">
                  <Label htmlFor="threshold">Low Stock Threshold</Label>
                  <Input
                    id="threshold"
                    type="number"
                    value={localSettings.lowStockThreshold}
                    onChange={(e) => handleNumericChange("lowStockThreshold", e.target.value)}
                    className="w-32"
                    placeholder="5"
                  />
                </div>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Scheme Payment Reminders</Label>
                  <p className="text-sm text-muted-foreground">Remind customers about upcoming payments</p>
                </div>
                <Switch
                  checked={localSettings.schemeReminders}
                  onCheckedChange={(checked) => handleChange("schemeReminders", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Repair Order Reminders</Label>
                  <p className="text-sm text-muted-foreground">Notify about repair completion and delivery</p>
                </div>
                <Switch
                  checked={localSettings.repairReminders}
                  onCheckedChange={(checked) => handleChange("repairReminders", checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Preferences
              </CardTitle>
              <CardDescription>Configure system-wide settings and preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={localSettings.currency} onValueChange={(value) => handleChange("currency", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INR">Indian Rupee (₹)</SelectItem>
                      <SelectItem value="USD">US Dollar ($)</SelectItem>
                      <SelectItem value="EUR">Euro (€)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dateFormat">Date Format</Label>
                  <Select value={localSettings.dateFormat} onValueChange={(value) => handleChange("dateFormat", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="backup">Auto Backup Frequency</Label>
                <Select
                  value={localSettings.backupFrequency}
                  onValueChange={(value) => handleChange("backupFrequency", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="manual">Manual Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="print" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Printer className="h-5 w-5" />
                Print Settings
              </CardTitle>
              <CardDescription>Configure invoice and receipt printing options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template">Invoice Template</Label>
                <Select
                  value={localSettings.invoiceTemplate}
                  onValueChange={(value) => handleChange("invoiceTemplate", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                    <SelectItem value="minimal">Minimal</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Print Business Logo</Label>
                  <p className="text-sm text-muted-foreground">Include logo on invoices and receipts</p>
                </div>
                <Switch
                  checked={localSettings.printLogo}
                  onCheckedChange={(checked) => handleChange("printLogo", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Print Terms & Conditions</Label>
                  <p className="text-sm text-muted-foreground">Include T&C at bottom of invoices</p>
                </div>
                <Switch
                  checked={localSettings.printTerms}
                  onCheckedChange={(checked) => handleChange("printTerms", checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Management
              </CardTitle>
              <CardDescription>Backup, restore, and manage your business data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button onClick={handleBackup} className="h-20 flex-col">
                  <Database className="h-6 w-6 mb-2" />
                  Backup Data
                </Button>
                <Button onClick={handleRestore} variant="outline" className="h-20 flex-col bg-transparent">
                  <Shield className="h-6 w-6 mb-2" />
                  Restore Data
                </Button>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>Data Export</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm">
                    Export Inventory
                  </Button>
                  <Button variant="outline" size="sm">
                    Export Customers
                  </Button>
                  <Button variant="outline" size="sm">
                    Export Sales
                  </Button>
                  <Button variant="outline" size="sm">
                    Export Reports
                  </Button>
                </div>
              </div>

              <Separator />

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">Danger Zone</h4>
                <p className="text-sm text-red-600 mb-3">These actions cannot be undone. Please be careful.</p>
                <Button variant="destructive" size="sm">
                  Reset All Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={handleReset}>
          Reset to Defaults
        </Button>
        <Button onClick={handleSave} disabled={!hasChanges}>
          <Check className="h-4 w-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  )
}
