/**
 * <PERSON><PERSON><PERSON> to convert existing UUID-based inventory item IDs to jewelry business format
 * This will update all existing inventory items with proper jewelry IDs
 */

import mysql from 'mysql2/promise'
import { generateJewelryItemId, getHSNCode } from '../lib/utils/jewelry-id-generator'

// Category mapping for existing data
const CATEGORY_MAPPING: Record<string, string> = {
  'Ring': 'Rings',
  'Necklace': 'Necklaces', 
  'Earrings': 'Earrings',
  'Bracelet': 'Bracelets',
  'Pendant': 'Pendants',
  'Chain': 'Chains',
  'Bangles': 'Bangles',
  'Anklet': 'Anklets'
}

interface InventoryRow {
  id: string
  name: string
  category: string
  metal_type: string
  purity: string
  hsn_code?: string
  created_at: string
  [key: string]: any
}

interface ConvertedItem {
  oldId: string
  newId: string
  name: string
  category: string
  hsnCode: string
}

async function convertItemIds() {
  let connection: mysql.Connection | undefined

  try {
    // Database connection configuration
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jjjewellers_db'
    }

    console.log('🔄 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Get all existing inventory items
    console.log('🔄 Fetching existing inventory items...')
    const [rows] = await connection.execute('SELECT * FROM inventory ORDER BY created_at ASC')
    const items = rows as InventoryRow[]
    console.log(`📦 Found ${items.length} inventory items to convert`)

    if (items.length === 0) {
      console.log('ℹ️  No items to convert')
      return
    }

    // Create a mapping of old IDs to new IDs for foreign key updates
    const idMapping = new Map<string, string>()
    const convertedItems: ConvertedItem[] = []
    let sequenceCounter = 1

    // Generate new IDs for all items
    for (const item of items) {
      // Normalize category name
      const normalizedCategory = CATEGORY_MAPPING[item.category] || item.category

      // Generate new jewelry ID
      const newId = generateJewelryItemId(
        normalizedCategory,
        item.metal_type,
        item.purity,
        sequenceCounter++
      )

      // Update HSN code if not already set
      const hsnCode = item.hsn_code || getHSNCode(normalizedCategory, item.metal_type)

      idMapping.set(item.id, newId)
      convertedItems.push({
        oldId: item.id,
        newId: newId,
        name: item.name,
        category: normalizedCategory,
        hsnCode: hsnCode
      })

      console.log(`   ${item.name}: ${item.id} → ${newId} (HSN: ${hsnCode})`)
    }

    console.log('\n🔄 Starting ID conversion process...')

    // Start transaction
    await connection.beginTransaction()

    try {
      // Step 1: Temporarily disable foreign key checks
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0')

      // Step 2: Create temporary table with new structure
      console.log('   Creating temporary table...')
      await connection.execute(`
        CREATE TEMPORARY TABLE inventory_temp AS 
        SELECT * FROM inventory WHERE 1=0
      `)

      // Step 3: Insert items with new IDs into temporary table
      console.log('   Inserting items with new IDs...')
      for (const item of items) {
        const convertedItem = convertedItems.find(c => c.oldId === item.id)
        if (!convertedItem) continue
        
        await connection.execute(`
          INSERT INTO inventory_temp 
          SELECT ?, name, ?, metal_type, gross_weight, stone_weight, net_weight, 
                 stone_amount, purity, making_charges, current_value, stock, 
                 stone_details, description, ?, created_at, updated_at
          FROM inventory WHERE id = ?
        `, [
          convertedItem.newId,
          convertedItem.category,
          convertedItem.hsnCode,
          item.id
        ])
      }

      // Step 4: Update foreign key references in related tables
      console.log('   Updating foreign key references...')
      
      // Update sale_items table
      for (const [oldId, newId] of idMapping) {
        await connection.execute(
          'UPDATE sale_items SET inventory_id = ? WHERE inventory_id = ?',
          [newId, oldId]
        )
      }

      // Step 5: Replace original table with updated data
      console.log('   Replacing original table...')
      await connection.execute('DELETE FROM inventory')
      await connection.execute(`
        INSERT INTO inventory 
        SELECT * FROM inventory_temp
      `)

      // Step 6: Re-enable foreign key checks
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

      // Commit transaction
      await connection.commit()
      console.log('✅ Transaction committed successfully')

    } catch (error) {
      // Rollback on error
      await connection.rollback()
      throw error
    }

    // Verify the conversion
    console.log('\n🔄 Verifying conversion...')
    const [updatedRows] = await connection.execute('SELECT id, name, category, hsn_code FROM inventory ORDER BY created_at ASC')
    const updatedItems = updatedRows as InventoryRow[]
    
    console.log('\n📋 Converted Items:')
    console.table(updatedItems.map(item => ({
      'Item ID': item.id,
      'Name': item.name,
      'Category': item.category,
      'HSN Code': item.hsn_code
    })))

    console.log(`\n✅ Successfully converted ${updatedItems.length} inventory items!`)
    console.log('🎉 All items now have jewelry business format IDs')

  } catch (error) {
    console.error('❌ Conversion failed:', (error as Error).message)
    console.error((error as Error).stack)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

// Check if this script is being run directly
if (require.main === module) {
  console.log('🚀 Starting Item ID Conversion')
  console.log('===============================')
  convertItemIds()
    .then(() => {
      console.log('\n🎊 Conversion completed successfully!')
      console.log('All inventory items now have proper jewelry business IDs!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Conversion failed:', error)
      process.exit(1)
    })
}

export { convertItemIds }
