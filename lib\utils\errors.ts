// Professional Error Handling System
// Comprehensive error types and handling for jewelry management system

export class BaseError extends Error {
  public readonly name: string
  public readonly statusCode: number
  public readonly isOperational: boolean
  public readonly timestamp: Date
  public readonly details?: any

  constructor(
    name: string,
    statusCode: number,
    description: string,
    isOperational = true,
    details?: any
  ) {
    super(description)
    
    Object.setPrototypeOf(this, new.target.prototype)
    
    this.name = name
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.timestamp = new Date()
    this.details = details

    Error.captureStackTrace(this)
  }
}

export class ValidationError extends BaseError {
  constructor(message: string, details?: any) {
    super('ValidationError', 400, message, true, details)
  }
}

export class BusinessLogicError extends BaseError {
  constructor(message: string, details?: any) {
    super('BusinessLogicError', 422, message, true, details)
  }
}

export class NotFoundError extends BaseError {
  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`
    super('NotFoundError', 404, message, true)
  }
}

export class UnauthorizedError extends BaseError {
  constructor(message = 'Unauthorized access') {
    super('UnauthorizedError', 401, message, true)
  }
}

export class ForbiddenError extends BaseError {
  constructor(message = 'Forbidden access') {
    super('ForbiddenError', 403, message, true)
  }
}

export class ConflictError extends BaseError {
  constructor(message: string, details?: any) {
    super('ConflictError', 409, message, true, details)
  }
}

export class DatabaseError extends BaseError {
  constructor(message: string, details?: any) {
    super('DatabaseError', 500, message, false, details)
  }
}

export class ExternalServiceError extends BaseError {
  constructor(service: string, message: string, details?: any) {
    super('ExternalServiceError', 502, `${service}: ${message}`, false, details)
  }
}

export class RateLimitError extends BaseError {
  constructor(message = 'Rate limit exceeded') {
    super('RateLimitError', 429, message, true)
  }
}

// Error response interface
export interface ErrorResponse {
  success: false
  error: {
    name: string
    message: string
    statusCode: number
    timestamp: string
    details?: any
    stack?: string
  }
  requestId?: string
}

// Success response interface
export interface SuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  meta?: {
    total?: number
    page?: number
    limit?: number
    hasNext?: boolean
    hasPrev?: boolean
  }
  requestId?: string
}

// API Response type
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse

// Error handler function
export const handleError = (error: Error, includeStack = false): ErrorResponse => {
  if (error instanceof BaseError) {
    return {
      success: false,
      error: {
        name: error.name,
        message: error.message,
        statusCode: error.statusCode,
        timestamp: error.timestamp.toISOString(),
        details: error.details,
        ...(includeStack && { stack: error.stack })
      }
    }
  }

  // Handle unknown errors
  return {
    success: false,
    error: {
      name: 'InternalServerError',
      message: 'An unexpected error occurred',
      statusCode: 500,
      timestamp: new Date().toISOString(),
      ...(includeStack && { stack: error.stack })
    }
  }
}

// Success response helper
export const createSuccessResponse = <T>(
  data: T,
  message?: string,
  meta?: any
): SuccessResponse<T> => ({
  success: true,
  data,
  message,
  meta
})

// Validation helpers
export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName} is required`)
  }
}

export const validateEmail = (email: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format')
  }
}

export const validatePhone = (phone: string): void => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    throw new ValidationError('Invalid phone number format')
  }
}

export const validatePositiveNumber = (value: number, fieldName: string): void => {
  if (typeof value !== 'number' || value < 0) {
    throw new ValidationError(`${fieldName} must be a positive number`)
  }
}

export const validateEnum = <T>(value: T, allowedValues: T[], fieldName: string): void => {
  if (!allowedValues.includes(value)) {
    throw new ValidationError(
      `${fieldName} must be one of: ${allowedValues.join(', ')}`
    )
  }
}

export const validateDateRange = (startDate: Date, endDate: Date): void => {
  if (startDate > endDate) {
    throw new ValidationError('Start date must be before end date')
  }
}

export const validateStringLength = (
  value: string,
  minLength: number,
  maxLength: number,
  fieldName: string
): void => {
  if (value.length < minLength || value.length > maxLength) {
    throw new ValidationError(
      `${fieldName} must be between ${minLength} and ${maxLength} characters`
    )
  }
}

// Business logic validators
export const validateMetalPurity = (metalType: string, purity: string): void => {
  const validPurities: Record<string, string[]> = {
    gold: ['24K', '22K', '18K', '14K', '10K'],
    silver: ['999', '925', '900', '800'],
    platinum: ['950', '900', '850']
  }

  if (!validPurities[metalType]?.includes(purity)) {
    throw new ValidationError(
      `Invalid purity '${purity}' for ${metalType}. Valid purities: ${validPurities[metalType]?.join(', ') || 'none'}`
    )
  }
}

export const validateWeight = (grossWeight: number, stoneWeight: number): void => {
  validatePositiveNumber(grossWeight, 'Gross weight')
  validatePositiveNumber(stoneWeight, 'Stone weight')
  
  if (stoneWeight > grossWeight) {
    throw new ValidationError('Stone weight cannot be greater than gross weight')
  }
}

export const validateRate = (metalType: string, rate: number): void => {
  const rateRanges: Record<string, { min: number; max: number }> = {
    gold: { min: 1000, max: 50000 },
    silver: { min: 10, max: 2000 },
    platinum: { min: 1000, max: 10000 }
  }

  const range = rateRanges[metalType]
  if (!range) {
    throw new ValidationError(`Unknown metal type: ${metalType}`)
  }

  if (rate < range.min || rate > range.max) {
    throw new ValidationError(
      `Rate for ${metalType} must be between ₹${range.min} and ₹${range.max} per gram`
    )
  }
}

export const validateGSTNumber = (gstNumber: string): void => {
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
  if (!gstRegex.test(gstNumber)) {
    throw new ValidationError('Invalid GST number format')
  }
}

export const validatePANNumber = (panNumber: string): void => {
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/
  if (!panRegex.test(panNumber)) {
    throw new ValidationError('Invalid PAN number format')
  }
}

export const validateAadharNumber = (aadharNumber: string): void => {
  const aadharRegex = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/
  if (!aadharRegex.test(aadharNumber.replace(/\s/g, ''))) {
    throw new ValidationError('Invalid Aadhar number format')
  }
}

// Async validation wrapper
export const asyncValidate = async <T>(
  validationFn: () => Promise<T> | T
): Promise<T> => {
  try {
    return await validationFn()
  } catch (error) {
    if (error instanceof BaseError) {
      throw error
    }
    throw new ValidationError('Validation failed', { originalError: error })
  }
}

// Error logging utility
export const logError = (error: Error, context?: any): void => {
  const errorInfo = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context
  }

  if (error instanceof BaseError) {
    errorInfo['statusCode'] = error.statusCode
    errorInfo['isOperational'] = error.isOperational
    errorInfo['details'] = error.details
  }

  // In production, this would integrate with logging service
  console.error('Error logged:', JSON.stringify(errorInfo, null, 2))
}

// Error recovery utilities
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      if (error instanceof BaseError && error.isOperational) {
        throw error // Don't retry operational errors
      }

      if (attempt === maxRetries) {
        break
      }

      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }

  throw lastError!
}

// Circuit breaker pattern for external services
export class CircuitBreaker {
  private failures = 0
  private lastFailureTime?: Date
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'

  constructor(
    private readonly threshold = 5,
    private readonly timeout = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (this.shouldAttemptReset()) {
        this.state = 'HALF_OPEN'
      } else {
        throw new ExternalServiceError('Circuit breaker', 'Service temporarily unavailable')
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess(): void {
    this.failures = 0
    this.state = 'CLOSED'
  }

  private onFailure(): void {
    this.failures++
    this.lastFailureTime = new Date()

    if (this.failures >= this.threshold) {
      this.state = 'OPEN'
    }
  }

  private shouldAttemptReset(): boolean {
    return this.lastFailureTime && 
           (Date.now() - this.lastFailureTime.getTime()) >= this.timeout
  }
}

export default {
  BaseError,
  ValidationError,
  BusinessLogicError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  DatabaseError,
  ExternalServiceError,
  RateLimitError,
  handleError,
  createSuccessResponse,
  validateRequired,
  validateEmail,
  validatePhone,
  validatePositiveNumber,
  validateEnum,
  validateDateRange,
  validateStringLength,
  validateMetalPurity,
  validateWeight,
  validateRate,
  validateGSTNumber,
  validatePANNumber,
  validateAadharNumber,
  asyncValidate,
  logError,
  withRetry,
  CircuitBreaker
}
