@echo off
echo ========================================
echo Exchange System Database Setup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if MySQL is accessible
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: MySQL command line client not found in PATH
    echo You can still run the setup, but manual SQL execution may be needed
    echo.
)

echo Node.js version:
node --version
echo.

REM Set default database configuration
if "%DB_HOST%"=="" set DB_HOST=localhost
if "%DB_USER%"=="" set DB_USER=root
if "%DB_NAME%"=="" set DB_NAME=jewellers_db

echo Database Configuration:
echo Host: %DB_HOST%
echo User: %DB_USER%
echo Database: %DB_NAME%
echo.

REM Check if password is set
if "%DB_PASSWORD%"=="" (
    echo WARNING: DB_PASSWORD environment variable not set
    echo Using empty password - this may fail if MySQL requires authentication
    echo.
    echo To set password, run:
    echo set DB_PASSWORD=your_password
    echo.
)

echo Starting database setup...
echo.

REM Run the Node.js setup script
node "%~dp0setup-database.js"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Database Setup Completed Successfully!
    echo ========================================
    echo.
    echo You can now:
    echo 1. Start the application: npm run dev
    echo 2. Navigate to Exchange tab
    echo 3. Try the Demo feature
    echo 4. Create test transactions
    echo.
) else (
    echo.
    echo ========================================
    echo Database Setup Failed!
    echo ========================================
    echo.
    echo Manual Setup Option:
    echo 1. Open MySQL command line or phpMyAdmin
    echo 2. Run scripts/migrations/001_create_exchange_tables.sql
    echo 3. Run scripts/migrations/002_insert_sample_data.sql
    echo.
    echo Common Issues:
    echo - MySQL server not running
    echo - Incorrect credentials
    echo - Insufficient permissions
    echo - Database already exists with conflicts
    echo.
)

pause
