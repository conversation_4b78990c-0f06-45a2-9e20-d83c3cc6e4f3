#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function fixUserPermissions() {
  console.log('🔧 Fixing User Permissions for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Check current user permissions
    console.log('🔍 Step 1: Checking current user permissions...')
    
    const [users] = await connection.execute(`
      SELECT id, username, email, role, permissions 
      FROM users 
      ORDER BY role, username
    `)

    console.log(`   Found ${(users as any[]).length} users:`)
    ;(users as any[]).forEach(user => {
      const permissionsStatus = user.permissions ? 
        (typeof user.permissions === 'string' ? 'JSON String' : 'Object') : 
        'NULL'
      console.log(`      ${user.username} (${user.role}): ${permissionsStatus}`)
    })

    // Step 2: Define role-based permissions
    console.log('\n🛡️  Step 2: Defining role-based permissions...')
    
    const rolePermissions = {
      super_admin: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'customers', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'sales', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'purchases', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'schemes', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'repairs', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'categories', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'exchange', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'reports', actions: ['read', 'export'] },
        { module: 'settings', actions: ['read', 'update'] },
        { module: 'users', actions: ['create', 'read', 'update', 'delete'] }
      ],
      manager: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'customers', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'sales', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'purchases', actions: ['create', 'read', 'update', 'export'] },
        { module: 'schemes', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'repairs', actions: ['create', 'read', 'update', 'delete', 'export'] },
        { module: 'categories', actions: ['read', 'update'] },
        { module: 'exchange', actions: ['create', 'read', 'update', 'export'] },
        { module: 'reports', actions: ['read', 'export'] },
        { module: 'settings', actions: ['read'] }
      ],
      sales_manager: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['read', 'update', 'export'] },
        { module: 'customers', actions: ['create', 'read', 'update', 'export'] },
        { module: 'sales', actions: ['create', 'read', 'update', 'export'] },
        { module: 'schemes', actions: ['create', 'read', 'update', 'export'] },
        { module: 'repairs', actions: ['create', 'read', 'update', 'export'] },
        { module: 'exchange', actions: ['create', 'read', 'update'] },
        { module: 'reports', actions: ['read'] }
      ],
      sales_staff: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['read'] },
        { module: 'customers', actions: ['create', 'read', 'update'] },
        { module: 'sales', actions: ['create', 'read', 'update'] },
        { module: 'schemes', actions: ['create', 'read', 'update'] },
        { module: 'repairs', actions: ['create', 'read', 'update'] },
        { module: 'exchange', actions: ['create', 'read'] }
      ],
      accountant: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['read', 'export'] },
        { module: 'customers', actions: ['read', 'export'] },
        { module: 'sales', actions: ['read', 'export'] },
        { module: 'purchases', actions: ['create', 'read', 'update', 'export'] },
        { module: 'schemes', actions: ['read', 'export'] },
        { module: 'repairs', actions: ['read', 'export'] },
        { module: 'exchange', actions: ['read', 'export'] },
        { module: 'reports', actions: ['read', 'export'] }
      ],
      cashier: [
        { module: 'dashboard', actions: ['read'] },
        { module: 'inventory', actions: ['read'] },
        { module: 'customers', actions: ['read'] },
        { module: 'sales', actions: ['create', 'read'] },
        { module: 'schemes', actions: ['read'] },
        { module: 'repairs', actions: ['read'] },
        { module: 'exchange', actions: ['read'] }
      ]
    }

    console.log('   Defined permissions for roles:')
    Object.keys(rolePermissions).forEach(role => {
      const permissions = rolePermissions[role as keyof typeof rolePermissions]
      console.log(`      ${role}: ${permissions.length} modules`)
    })

    // Step 3: Update user permissions
    console.log('\n🔄 Step 3: Updating user permissions...')
    
    let updatedCount = 0
    for (const user of (users as any[])) {
      const userRole = user.role as keyof typeof rolePermissions
      const permissions = rolePermissions[userRole]
      
      if (permissions) {
        const permissionsJson = JSON.stringify(permissions)
        
        try {
          await connection.execute(`
            UPDATE users 
            SET permissions = ?, updated_at = NOW() 
            WHERE id = ?
          `, [permissionsJson, user.id])
          
          console.log(`   ✅ Updated ${user.username} (${user.role}) with ${permissions.length} modules`)
          updatedCount++
        } catch (error) {
          console.log(`   ❌ Failed to update ${user.username}: ${error}`)
        }
      } else {
        console.log(`   ⚠️  No permissions defined for role: ${user.role}`)
      }
    }

    console.log(`\n   📊 Updated ${updatedCount}/${(users as any[]).length} users`)

    // Step 4: Verify updated permissions
    console.log('\n✅ Step 4: Verifying updated permissions...')
    
    const [updatedUsers] = await connection.execute(`
      SELECT id, username, email, role, permissions 
      FROM users 
      ORDER BY role, username
    `)

    console.log(`   Verification results:`)
    for (const user of (updatedUsers as any[])) {
      let permissionsInfo = 'NULL'
      let moduleCount = 0
      
      if (user.permissions) {
        try {
          const parsed = typeof user.permissions === 'string' ? 
            JSON.parse(user.permissions) : user.permissions
          moduleCount = Array.isArray(parsed) ? parsed.length : 0
          permissionsInfo = `${moduleCount} modules`
        } catch (error) {
          permissionsInfo = 'Invalid JSON'
        }
      }
      
      console.log(`      ${user.username} (${user.role}): ${permissionsInfo}`)
    }

    // Step 5: Test permission checking
    console.log('\n🧪 Step 5: Testing permission checking...')
    
    const testUser = (updatedUsers as any[]).find(u => u.username === 'admin')
    if (testUser && testUser.permissions) {
      try {
        const permissions = typeof testUser.permissions === 'string' ? 
          JSON.parse(testUser.permissions) : testUser.permissions
        
        console.log(`   Testing admin permissions:`)
        console.log(`      Total modules: ${permissions.length}`)
        
        // Test specific permissions
        const inventoryPermission = permissions.find((p: any) => p.module === 'inventory')
        const salesPermission = permissions.find((p: any) => p.module === 'sales')
        
        console.log(`      Inventory actions: ${inventoryPermission?.actions?.join(', ') || 'None'}`)
        console.log(`      Sales actions: ${salesPermission?.actions?.join(', ') || 'None'}`)
        
      } catch (error) {
        console.log(`   ❌ Failed to test permissions: ${error}`)
      }
    }

    console.log('\n🎉 User Permissions Fix Completed Successfully!')

    console.log('\n📋 PERMISSION SUMMARY:')
    console.log('=' .repeat(60))
    console.log('✅ All users now have proper role-based permissions')
    console.log('✅ Permissions stored as JSON in database')
    console.log('✅ UserService updated to parse JSON permissions')
    console.log('✅ hasPermission function updated with null checks')
    console.log('✅ Admin users have full system access')
    console.log('✅ Role-based access control implemented')
    console.log('=' .repeat(60))

  } catch (error) {
    console.error('\n❌ User permissions fix failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the user permissions fix
fixUserPermissions().catch(console.error)
