-- Exchange Billing Integration Database Schema Extensions

-- Add billing integration fields to exchange_transactions table
ALTER TABLE exchange_transactions ADD COLUMN IF NOT EXISTS purchase_bill_id VARCHAR(36);
ALTER TABLE exchange_transactions ADD COLUMN IF NOT EXISTS purchase_bill_number VARCHAR(50);
ALTER TABLE exchange_transactions ADD COLUMN IF NOT EXISTS purchase_bill_generated BOOLEAN DEFAULT FALSE;
ALTER TABLE exchange_transactions ADD COLUMN IF NOT EXISTS purchase_bill_date DATE;

-- Add foreign key constraint (if purchase bills table exists)
-- ALTER TABLE exchange_transactions ADD CONSTRAINT fk_exchange_purchase_bill 
-- FOREIGN KEY (purchase_bill_id) REFERENCES purchase_bills(id) ON DELETE SET NULL;

-- Exchange Purchase Bills Table - Dedicated table for exchange purchase bills
CREATE TABLE IF NOT EXISTS exchange_purchase_bills (
  id VARCHAR(36) PRIMARY KEY,
  bill_number VARCHAR(50) UNIQUE NOT NULL,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  customer_id VARCHAR(36),
  bill_date DATE NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
  cgst_amount DECIMAL(10,2) DEFAULT 0,
  sgst_amount DECIMAL(10,2) DEFAULT 0,
  total_with_tax DECIMAL(12,2) NOT NULL DEFAULT 0,
  payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
  payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
  notes TEXT,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_bill_number (bill_number),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_bill_date (bill_date)
);

-- Sales Exchange Integration Table - Links sales with exchange items
CREATE TABLE IF NOT EXISTS sales_exchange_items (
  id VARCHAR(36) PRIMARY KEY,
  sale_id VARCHAR(36) NOT NULL,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  exchange_item_id VARCHAR(36) NOT NULL,
  deduction_amount DECIMAL(12,2) NOT NULL,
  applied_rate DECIMAL(10,2) NOT NULL, -- Rate at which exchange was applied
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) ON DELETE CASCADE,
  INDEX idx_sale (sale_id),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_exchange_item (exchange_item_id)
);

-- Exchange Vouchers Table - For compliance and record keeping
CREATE TABLE IF NOT EXISTS exchange_vouchers (
  id VARCHAR(36) PRIMARY KEY,
  voucher_number VARCHAR(50) UNIQUE NOT NULL,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  voucher_type ENUM('purchase', 'exchange', 'adjustment') NOT NULL,
  voucher_date DATE NOT NULL,
  customer_id VARCHAR(36),
  total_amount DECIMAL(12,2) NOT NULL,
  description TEXT,
  terms_conditions TEXT,
  validity_date DATE,
  status ENUM('active', 'used', 'expired', 'cancelled') DEFAULT 'active',
  used_in_sale_id VARCHAR(36),
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
  FOREIGN KEY (used_in_sale_id) REFERENCES sales(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_voucher_number (voucher_number),
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_status (status),
  INDEX idx_validity_date (validity_date)
);

-- Bill Sequences Table - For generating sequential bill numbers
CREATE TABLE IF NOT EXISTS bill_sequences (
  id VARCHAR(36) PRIMARY KEY,
  sequence_type ENUM('exchange_purchase', 'exchange_voucher', 'sales', 'purchase') NOT NULL,
  prefix VARCHAR(10) NOT NULL,
  current_number INT NOT NULL DEFAULT 0,
  financial_year VARCHAR(10) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_sequence (sequence_type, financial_year),
  INDEX idx_sequence_type (sequence_type),
  INDEX idx_financial_year (financial_year)
);

-- Insert default sequences for current financial year
INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) VALUES
(UUID(), 'exchange_purchase', 'EPB', 0, '2024-25'),
(UUID(), 'exchange_voucher', 'EV', 0, '2024-25')
ON DUPLICATE KEY UPDATE 
  updated_at = CURRENT_TIMESTAMP;

-- Exchange Audit Trail Table - Complete audit trail for compliance
CREATE TABLE IF NOT EXISTS exchange_audit_trail (
  id VARCHAR(36) PRIMARY KEY,
  exchange_transaction_id VARCHAR(36) NOT NULL,
  action_type ENUM('created', 'updated', 'billed', 'voucher_generated', 'used_in_sale', 'cancelled') NOT NULL,
  action_description TEXT NOT NULL,
  old_values JSON,
  new_values JSON,
  related_bill_id VARCHAR(36),
  related_sale_id VARCHAR(36),
  performed_by VARCHAR(36),
  performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
  FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_exchange_transaction (exchange_transaction_id),
  INDEX idx_action_type (action_type),
  INDEX idx_performed_at (performed_at)
);

-- Views for reporting and analytics

-- Exchange Purchase Bills Summary View
CREATE OR REPLACE VIEW exchange_purchase_bills_summary AS
SELECT 
  epb.id,
  epb.bill_number,
  epb.bill_date,
  epb.total_amount,
  epb.total_with_tax,
  epb.payment_status,
  et.transaction_number,
  et.transaction_date,
  c.name as customer_name,
  c.phone as customer_phone,
  COUNT(ei.id) as items_count,
  SUM(ei.net_weight) as total_net_weight
FROM exchange_purchase_bills epb
JOIN exchange_transactions et ON epb.exchange_transaction_id = et.id
LEFT JOIN customers c ON epb.customer_id = c.id
LEFT JOIN exchange_items ei ON et.id = ei.transaction_id
GROUP BY epb.id, epb.bill_number, epb.bill_date, epb.total_amount, epb.total_with_tax, 
         epb.payment_status, et.transaction_number, et.transaction_date, c.name, c.phone;

-- Sales with Exchange Integration View
CREATE OR REPLACE VIEW sales_with_exchange_summary AS
SELECT 
  s.id as sale_id,
  s.invoice_number,
  s.sale_date,
  s.total_amount as sale_total,
  COALESCE(SUM(sei.deduction_amount), 0) as exchange_deduction,
  (s.total_amount - COALESCE(SUM(sei.deduction_amount), 0)) as final_amount,
  COUNT(sei.id) as exchange_items_count,
  c.name as customer_name
FROM sales s
LEFT JOIN sales_exchange_items sei ON s.id = sei.sale_id
LEFT JOIN customers c ON s.customer_id = c.id
GROUP BY s.id, s.invoice_number, s.sale_date, s.total_amount, c.name;

-- Exchange Utilization Report View
CREATE OR REPLACE VIEW exchange_utilization_report AS
SELECT 
  et.id,
  et.transaction_number,
  et.transaction_date,
  et.total_amount as exchange_value,
  epb.bill_number as purchase_bill,
  CASE WHEN sei.sale_id IS NOT NULL THEN 'Used in Sale' ELSE 'Available' END as status,
  COALESCE(SUM(sei.deduction_amount), 0) as amount_used,
  (et.total_amount - COALESCE(SUM(sei.deduction_amount), 0)) as remaining_value
FROM exchange_transactions et
LEFT JOIN exchange_purchase_bills epb ON et.id = epb.exchange_transaction_id
LEFT JOIN sales_exchange_items sei ON et.id = sei.exchange_transaction_id
GROUP BY et.id, et.transaction_number, et.transaction_date, et.total_amount, epb.bill_number;
