#!/bin/bash

echo "========================================"
echo "Exchange System Database Setup"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}ERROR: Node.js is not installed or not in PATH${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if MySQL is accessible
if ! command -v mysql &> /dev/null; then
    echo -e "${YELLOW}WARNING: MySQL command line client not found in PATH${NC}"
    echo "You can still run the setup, but manual SQL execution may be needed"
    echo
fi

echo -e "${BLUE}Node.js version:${NC}"
node --version
echo

# Set default database configuration
DB_HOST=${DB_HOST:-localhost}
DB_USER=${DB_USER:-root}
DB_NAME=${DB_NAME:-jewellers_db}

echo -e "${BLUE}Database Configuration:${NC}"
echo "Host: $DB_HOST"
echo "User: $DB_USER"
echo "Database: $DB_NAME"
echo

# Check if password is set
if [ -z "$DB_PASSWORD" ]; then
    echo -e "${YELLOW}WARNING: DB_PASSWORD environment variable not set${NC}"
    echo "Using empty password - this may fail if MySQL requires authentication"
    echo
    echo "To set password, run:"
    echo "export DB_PASSWORD=your_password"
    echo
fi

echo -e "${BLUE}Starting database setup...${NC}"
echo

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Run the Node.js setup script
node "$SCRIPT_DIR/setup-database.js"

if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}Database Setup Completed Successfully!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo
    echo -e "${BLUE}You can now:${NC}"
    echo "1. Start the application: npm run dev"
    echo "2. Navigate to Exchange tab"
    echo "3. Try the Demo feature"
    echo "4. Create test transactions"
    echo
else
    echo
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}Database Setup Failed!${NC}"
    echo -e "${RED}========================================${NC}"
    echo
    echo -e "${YELLOW}Manual Setup Option:${NC}"
    echo "1. Open MySQL command line or phpMyAdmin"
    echo "2. Run scripts/migrations/001_create_exchange_tables.sql"
    echo "3. Run scripts/migrations/002_insert_sample_data.sql"
    echo
    echo -e "${YELLOW}Common Issues:${NC}"
    echo "- MySQL server not running"
    echo "- Incorrect credentials"
    echo "- Insufficient permissions"
    echo "- Database already exists with conflicts"
    echo
    
    # Offer to try manual MySQL setup
    if command -v mysql &> /dev/null; then
        echo
        read -p "Would you like to try manual MySQL setup? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}Attempting manual MySQL setup...${NC}"
            
            # Try to run SQL files directly with MySQL
            echo "Please enter MySQL password when prompted:"
            
            mysql -h "$DB_HOST" -u "$DB_USER" -p -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}Database created successfully${NC}"
                
                echo "Running migration scripts..."
                mysql -h "$DB_HOST" -u "$DB_USER" -p "$DB_NAME" < "$SCRIPT_DIR/migrations/001_create_exchange_tables.sql"
                mysql -h "$DB_HOST" -u "$DB_USER" -p "$DB_NAME" < "$SCRIPT_DIR/migrations/002_insert_sample_data.sql"
                
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}Manual setup completed successfully!${NC}"
                else
                    echo -e "${RED}Manual setup failed. Please check the SQL files manually.${NC}"
                fi
            else
                echo -e "${RED}Could not create database. Please check credentials and permissions.${NC}"
            fi
        fi
    fi
fi

echo
echo -e "${BLUE}Setup script completed.${NC}"
