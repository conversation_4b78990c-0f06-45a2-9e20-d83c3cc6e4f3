// Test script to verify the infinite loop fix
// This can be run in the browser console to check for excessive re-renders

console.log('🧪 Testing for infinite loop issues...');

// Monitor fetch calls to detect excessive API requests
let fetchCallCount = 0;
const originalFetch = window.fetch;

window.fetch = function(...args) {
  fetchCallCount++;
  console.log(`📡 Fetch call #${fetchCallCount}:`, args[0]);
  return originalFetch.apply(this, args);
};

// Monitor React re-renders (if React DevTools is available)
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('✅ React DevTools detected - monitoring re-renders');
} else {
  console.log('⚠️ React DevTools not available - limited monitoring');
}

// Set up a timer to check for excessive calls
setTimeout(() => {
  console.log(`📊 Total fetch calls in 10 seconds: ${fetchCallCount}`);
  if (fetchCallCount > 20) {
    console.error('🚨 Potential infinite loop detected - too many fetch calls!');
  } else if (fetchCallCount <= 4) {
    console.log('✅ Normal behavior - expected initial data loading calls');
  } else {
    console.warn('⚠️ Moderate number of calls - may need investigation');
  }
}, 10000);

console.log('✅ Test setup complete. Wait 10 seconds for results...');
