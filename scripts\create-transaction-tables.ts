#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function createTransactionTables() {
  console.log('🏗️  Creating All Transaction & Supporting Tables for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')

    // 1. Sales Table
    console.log('   Creating sales table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sales (
        id VARCHAR(36) PRIMARY KEY,
        invoice_number VARCHAR(100) UNIQUE NOT NULL,
        invoice_date DATE NOT NULL,
        invoice_time TIME DEFAULT '00:00:00',
        
        -- Customer Information
        customer_id VARCHAR(36),
        customer_name VARCHAR(255),
        customer_phone VARCHAR(20),
        customer_address TEXT,
        
        -- Sale Type and Classification
        sale_type ENUM('cash', 'credit', 'exchange', 'scheme', 'advance', 'return') DEFAULT 'cash',
        sale_category ENUM('retail', 'wholesale', 'corporate', 'online', 'exhibition') DEFAULT 'retail',
        payment_mode ENUM('cash', 'card', 'upi', 'bank_transfer', 'cheque', 'dd', 'mixed', 'credit') DEFAULT 'cash',
        
        -- Financial Calculations
        subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
        
        -- Discount Information
        discount_type ENUM('percentage', 'fixed', 'scheme', 'loyalty', 'promotional') DEFAULT 'percentage',
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        discount_amount DECIMAL(15,2) DEFAULT 0.00,
        discount_reason VARCHAR(255),
        discount_approved_by VARCHAR(36),
        
        -- Taxable Amount Calculation
        taxable_amount DECIMAL(15,2) GENERATED ALWAYS AS (subtotal - discount_amount) STORED,
        
        -- Tax Information
        cgst_rate DECIMAL(5,2) DEFAULT 1.50,
        sgst_rate DECIMAL(5,2) DEFAULT 1.50,
        igst_rate DECIMAL(5,2) DEFAULT 0.00,
        cess_rate DECIMAL(5,2) DEFAULT 0.00,
        cgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cgst_rate / 100) STORED,
        sgst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * sgst_rate / 100) STORED,
        igst_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * igst_rate / 100) STORED,
        cess_amount DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount * cess_rate / 100) STORED,
        total_tax DECIMAL(15,2) GENERATED ALWAYS AS (cgst_amount + sgst_amount + igst_amount + cess_amount) STORED,
        
        -- Additional Charges
        packing_charges DECIMAL(10,2) DEFAULT 0.00,
        shipping_charges DECIMAL(10,2) DEFAULT 0.00,
        insurance_charges DECIMAL(10,2) DEFAULT 0.00,
        handling_charges DECIMAL(10,2) DEFAULT 0.00,
        other_charges DECIMAL(10,2) DEFAULT 0.00,
        total_additional_charges DECIMAL(15,2) GENERATED ALWAYS AS (packing_charges + shipping_charges + insurance_charges + handling_charges + other_charges) STORED,
        
        -- Round Off
        round_off DECIMAL(5,2) DEFAULT 0.00,
        
        -- Grand Total Calculation
        grand_total DECIMAL(15,2) GENERATED ALWAYS AS (taxable_amount + total_tax + total_additional_charges + round_off) STORED,
        
        -- Exchange Information
        exchange_deduction DECIMAL(15,2) DEFAULT 0.00,
        exchange_reference VARCHAR(255),
        
        -- Final Amount
        final_amount DECIMAL(15,2) GENERATED ALWAYS AS (grand_total - exchange_deduction) STORED,
        
        -- Payment Information
        payment_status ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'paid',
        paid_amount DECIMAL(15,2) DEFAULT 0.00,
        balance_amount DECIMAL(15,2) GENERATED ALWAYS AS (final_amount - paid_amount) STORED,
        due_date DATE,
        
        -- Status Information
        status ENUM('draft', 'confirmed', 'packed', 'shipped', 'delivered', 'cancelled', 'returned') DEFAULT 'confirmed',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- Staff Information
        sales_person_id VARCHAR(36),
        cashier_id VARCHAR(36),
        manager_approval_id VARCHAR(36),
        
        -- Additional Information
        notes TEXT,
        internal_notes TEXT,
        terms_conditions TEXT,
        special_instructions TEXT,
        
        -- Loyalty and Rewards
        loyalty_points_earned INT DEFAULT 0,
        loyalty_points_redeemed INT DEFAULT 0,
        
        -- Audit Information
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_invoice_number (invoice_number),
        INDEX idx_invoice_date (invoice_date),
        INDEX idx_customer (customer_id),
        INDEX idx_sale_type (sale_type),
        INDEX idx_payment_status (payment_status),
        INDEX idx_status (status),
        INDEX idx_sales_person (sales_person_id),
        INDEX idx_final_amount (final_amount),
        INDEX idx_created_at (created_at)
      )
    `)

    // 2. Sale Items Table
    console.log('   Creating sale_items table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sale_items (
        id VARCHAR(36) PRIMARY KEY,
        sale_id VARCHAR(36) NOT NULL,
        line_number INT NOT NULL,
        
        -- Item Information
        inventory_id VARCHAR(36),
        item_code VARCHAR(100),
        item_name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(100),
        
        -- Metal Information
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20),
        hallmark_number VARCHAR(100),
        
        -- Weight Information
        gross_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
        diamond_weight DECIMAL(8,3) DEFAULT 0.000,
        
        -- Wastage Calculation
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        wastage_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight * wastage_percentage / 100) STORED,
        chargeable_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight + wastage_weight) STORED,
        
        -- Pricing Information
        rate_per_gram DECIMAL(12,2) NOT NULL,
        metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (chargeable_weight * rate_per_gram) STORED,
        
        -- Charges
        making_charges DECIMAL(15,2) DEFAULT 0.00,
        making_charge_percentage DECIMAL(5,2) DEFAULT 0.00,
        stone_charges DECIMAL(15,2) DEFAULT 0.00,
        other_charges DECIMAL(15,2) DEFAULT 0.00,
        certification_charges DECIMAL(10,2) DEFAULT 0.00,
        
        -- Total Amount Calculation
        total_amount DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges + certification_charges) STORED,
        
        -- Discount Information
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        discount_amount DECIMAL(15,2) DEFAULT 0.00,
        
        -- Final Amount
        final_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - discount_amount) STORED,
        
        -- Quantity Information
        quantity INT DEFAULT 1,
        unit_price DECIMAL(15,2) GENERATED ALWAYS AS (final_amount / quantity) STORED,
        
        -- Additional Information
        hsn_code VARCHAR(20),
        size VARCHAR(50),
        color VARCHAR(50),
        design_number VARCHAR(100),
        
        -- Status
        status ENUM('active', 'cancelled', 'returned', 'exchanged') DEFAULT 'active',
        
        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_sale_line (sale_id, line_number),
        INDEX idx_sale (sale_id),
        INDEX idx_inventory (inventory_id),
        INDEX idx_item_code (item_code),
        INDEX idx_metal_type (metal_type),
        INDEX idx_final_amount (final_amount)
      )
    `)

    // 3. Exchange Transactions Table
    console.log('   Creating exchange_transactions table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_transactions (
        id VARCHAR(36) PRIMARY KEY,
        transaction_number VARCHAR(100) UNIQUE NOT NULL,
        transaction_date DATE NOT NULL,
        transaction_time TIME DEFAULT '00:00:00',

        -- Customer Information
        customer_id VARCHAR(36),
        customer_name VARCHAR(255),
        customer_phone VARCHAR(20),

        -- Transaction Details
        total_items INT DEFAULT 0,
        total_gross_weight DECIMAL(10,3) DEFAULT 0.000,
        total_net_weight DECIMAL(10,3) DEFAULT 0.000,
        total_stone_weight DECIMAL(10,3) DEFAULT 0.000,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,

        -- Payment Information
        payment_method ENUM('cash', 'bank_transfer', 'adjustment', 'account_credit', 'cheque') DEFAULT 'cash',
        payment_reference VARCHAR(100),

        -- Status Information
        status ENUM('pending', 'completed', 'cancelled', 'on_hold', 'partial') DEFAULT 'pending',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',

        -- Purchase Bill Information
        purchase_bill_generated BOOLEAN DEFAULT FALSE,
        purchase_bill_id VARCHAR(36),
        purchase_bill_number VARCHAR(100),
        purchase_bill_date DATE,
        purchase_bill_amount DECIMAL(15,2) DEFAULT 0.00,

        -- Additional Information
        notes TEXT,
        internal_notes TEXT,
        special_instructions TEXT,

        -- Staff Information
        received_by VARCHAR(36),
        evaluated_by VARCHAR(36),
        processed_by VARCHAR(36),

        -- Audit Information
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_transaction_number (transaction_number),
        INDEX idx_transaction_date (transaction_date),
        INDEX idx_customer (customer_id),
        INDEX idx_status (status),
        INDEX idx_total_amount (total_amount),
        INDEX idx_created_at (created_at)
      )
    `)

    // 4. Exchange Items Table
    console.log('   Creating exchange_items table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_items (
        id VARCHAR(36) PRIMARY KEY,
        transaction_id VARCHAR(36) NOT NULL,
        item_number INT NOT NULL,

        -- Item Description
        item_description VARCHAR(255) NOT NULL,
        item_type VARCHAR(100),
        category VARCHAR(100),

        -- Metal Information
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20) NOT NULL,
        hallmark_number VARCHAR(100),

        -- Weight Information
        gross_weight DECIMAL(10,3) NOT NULL,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
        diamond_weight DECIMAL(8,3) DEFAULT 0.000,
        other_deductions DECIMAL(8,3) DEFAULT 0.000,

        -- Final Weight Calculation
        final_weight DECIMAL(10,3) GENERATED ALWAYS AS (net_weight - other_deductions) STORED,

        -- Pricing Information
        rate_per_gram DECIMAL(12,2) NOT NULL,
        amount DECIMAL(15,2) GENERATED ALWAYS AS (final_weight * rate_per_gram) STORED,

        -- Condition Assessment
        item_condition ENUM('excellent', 'very_good', 'good', 'fair', 'poor') DEFAULT 'good',
        condition_notes TEXT,

        -- Quality Assessment
        hallmark_available BOOLEAN DEFAULT FALSE,
        hallmark_verified BOOLEAN DEFAULT FALSE,
        purity_verified BOOLEAN DEFAULT FALSE,
        purity_test_method ENUM('touchstone', 'electronic', 'xrf', 'fire_assay') DEFAULT 'touchstone',
        actual_purity VARCHAR(20),

        -- Additional Information
        notes TEXT,

        -- Status
        status ENUM('pending', 'evaluated', 'approved', 'rejected', 'on_hold') DEFAULT 'pending',
        rejection_reason TEXT,

        -- Audit Information
        evaluated_by VARCHAR(36),
        evaluated_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_transaction_item (transaction_id, item_number),
        INDEX idx_transaction (transaction_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity (purity),
        INDEX idx_item_condition (item_condition),
        INDEX idx_amount (amount),
        INDEX idx_status (status)
      )
    `)

    // 5. Suppliers Table
    console.log('   Creating suppliers table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id VARCHAR(36) PRIMARY KEY,
        supplier_code VARCHAR(50) UNIQUE NOT NULL,
        supplier_type ENUM('manufacturer', 'wholesaler', 'artisan', 'refiner', 'importer', 'other') DEFAULT 'wholesaler',

        -- Business Information
        company_name VARCHAR(255) NOT NULL,
        trade_name VARCHAR(255),
        contact_person VARCHAR(255),
        contact_designation VARCHAR(100),
        contact_phone VARCHAR(20) NOT NULL,
        contact_email VARCHAR(255),

        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',

        -- Legal Information
        gst_number VARCHAR(50),
        pan_number VARCHAR(20),

        -- Banking Information
        bank_name VARCHAR(255),
        bank_account_number VARCHAR(50),
        bank_ifsc VARCHAR(20),

        -- Business Terms
        credit_limit DECIMAL(15,2) DEFAULT 0.00,
        credit_days INT DEFAULT 0,
        payment_terms VARCHAR(255),
        discount_offered DECIMAL(5,2) DEFAULT 0.00,

        -- Financial Tracking
        total_purchases DECIMAL(15,2) DEFAULT 0.00,
        total_outstanding DECIMAL(15,2) DEFAULT 0.00,
        total_payments DECIMAL(15,2) DEFAULT 0.00,
        last_purchase_date DATE,
        last_payment_date DATE,

        -- Performance Metrics
        rating DECIMAL(3,2) DEFAULT 0.00,

        -- Additional Information
        notes TEXT,

        -- Status
        is_active BOOLEAN DEFAULT TRUE,
        is_preferred BOOLEAN DEFAULT FALSE,

        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_supplier_code (supplier_code),
        INDEX idx_supplier_type (supplier_type),
        INDEX idx_company_name (company_name),
        INDEX idx_contact_phone (contact_phone),
        INDEX idx_city (city),
        INDEX idx_gst_number (gst_number),
        INDEX idx_is_active (is_active),
        INDEX idx_is_preferred (is_preferred),
        INDEX idx_rating (rating)
      )
    `)

    // 6. Bill Sequences Table
    console.log('   Creating bill_sequences table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS bill_sequences (
        id VARCHAR(36) PRIMARY KEY,
        sequence_type VARCHAR(100) NOT NULL,
        sequence_name VARCHAR(255) NOT NULL,
        prefix VARCHAR(20) NOT NULL,
        suffix VARCHAR(20) DEFAULT '',
        current_number INT NOT NULL DEFAULT 0,
        start_number INT DEFAULT 1,
        increment_by INT DEFAULT 1,
        pad_length INT DEFAULT 4,
        financial_year VARCHAR(20) NOT NULL,
        format_pattern VARCHAR(100) DEFAULT '{prefix}/{year}/{number:04d}',

        -- Configuration
        reset_annually BOOLEAN DEFAULT TRUE,
        reset_monthly BOOLEAN DEFAULT FALSE,
        include_month BOOLEAN DEFAULT FALSE,
        include_day BOOLEAN DEFAULT FALSE,

        -- Status
        is_active BOOLEAN DEFAULT TRUE,
        last_used_date DATE,

        -- Audit
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_sequence (sequence_type, financial_year),
        INDEX idx_sequence_type (sequence_type),
        INDEX idx_financial_year (financial_year),
        INDEX idx_is_active (is_active)
      )
    `)

    console.log('   ✅ All transaction and supporting tables created successfully')

    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    console.log('\n🎉 All Transaction & Supporting Tables Created Successfully!')

  } catch (error) {
    console.error('\n❌ Transaction table creation failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the transaction table creation
createTransactionTables().catch(console.error)
