"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ShoppingCart, Users, Package, FileText, Calculator, QrCode, CreditCard, Wrench } from "lucide-react"

interface QuickActionsProps {
  onActionClick: (action: string) => void
}

export function QuickActions({ onActionClick }: QuickActionsProps) {
  const actions = [
    { id: "new-sale", label: "New Sale", icon: ShoppingCart, color: "bg-green-500" },
    { id: "add-customer", label: "Add Customer", icon: Users, color: "bg-blue-500" },
    { id: "add-item", label: "Add Item", icon: Package, color: "bg-purple-500" },
    { id: "generate-report", label: "Generate Report", icon: FileText, color: "bg-orange-500" },
    { id: "rate-calculator", label: "Rate Calculator", icon: Calculator, color: "bg-teal-500" },
    { id: "scan-barcode", label: "Scan Barcode", icon: QrCode, color: "bg-indigo-500" },
    { id: "new-scheme", label: "New Scheme", icon: CreditCard, color: "bg-pink-500" },
    { id: "repair-order", label: "Repair Order", icon: Wrench, color: "bg-yellow-500" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant="outline"
              className="h-20 flex-col gap-2 hover:shadow-md transition-shadow bg-transparent"
              onClick={() => onActionClick(action.id)}
            >
              <div className={`p-2 rounded-full ${action.color} text-white`}>
                <action.icon className="h-4 w-4" />
              </div>
              <span className="text-xs font-medium">{action.label}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
