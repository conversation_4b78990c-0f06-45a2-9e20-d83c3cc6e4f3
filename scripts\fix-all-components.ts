#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'

async function fixAllComponents() {
  console.log('🔧 Fixing All Components to Match Database Schema...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Analyze database schema
    console.log('🔍 Step 1: Analyzing database schema...')
    
    const [inventoryColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'inventory'
      ORDER BY ORDINAL_POSITION
    `, [dbConfig.database])

    console.log('   📋 Inventory table columns:')
    ;(inventoryColumns as any[]).forEach(col => {
      const nullable = col.IS_NULLABLE === 'YES' ? '(nullable)' : '(required)'
      console.log(`      ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${nullable}`)
    })

    // Step 2: Check actual data for null/undefined values
    console.log('\n🔍 Step 2: Checking for null/undefined values in inventory...')
    
    const [inventoryData] = await connection.execute(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN stone_charges IS NULL THEN 1 END) as null_stone_charges,
        COUNT(CASE WHEN making_charges IS NULL THEN 1 END) as null_making_charges,
        COUNT(CASE WHEN selling_price IS NULL THEN 1 END) as null_selling_price,
        COUNT(CASE WHEN gross_weight IS NULL THEN 1 END) as null_gross_weight,
        COUNT(CASE WHEN stone_weight IS NULL THEN 1 END) as null_stone_weight
      FROM inventory
    `)

    const stats = (inventoryData as any[])[0]
    console.log('   📊 Null value analysis:')
    console.log(`      Total items: ${stats.total_items}`)
    console.log(`      Null stone_charges: ${stats.null_stone_charges}`)
    console.log(`      Null making_charges: ${stats.null_making_charges}`)
    console.log(`      Null selling_price: ${stats.null_selling_price}`)
    console.log(`      Null gross_weight: ${stats.null_gross_weight}`)
    console.log(`      Null stone_weight: ${stats.null_stone_weight}`)

    // Step 3: Create utility functions for safe value handling
    console.log('\n🛠️  Step 3: Creating utility functions...')
    
    const utilityFunctions = `
// Utility functions for safe value handling
export const safeNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return defaultValue
  }
  return Number(value)
}

export const safeString = (value: any, defaultValue: string = ''): string => {
  if (value === null || value === undefined) {
    return defaultValue
  }
  return String(value)
}

export const formatCurrency = (value: any, defaultValue: number = 0): string => {
  const numValue = safeNumber(value, defaultValue)
  return \`₹\${numValue.toLocaleString()}\`
}

export const formatWeight = (value: any, defaultValue: number = 0, unit: string = 'g'): string => {
  const numValue = safeNumber(value, defaultValue)
  return \`\${numValue.toFixed(3)}\${unit}\`
}

export const formatPercentage = (value: any, defaultValue: number = 0): string => {
  const numValue = safeNumber(value, defaultValue)
  return \`\${numValue.toFixed(2)}%\`
}
`

    // Write utility functions to a new file
    const utilsPath = path.join(process.cwd(), 'lib', 'utils', 'safe-values.ts')
    const utilsDir = path.dirname(utilsPath)
    
    if (!fs.existsSync(utilsDir)) {
      fs.mkdirSync(utilsDir, { recursive: true })
    }
    
    fs.writeFileSync(utilsPath, utilityFunctions)
    console.log('   ✅ Created safe value utility functions at lib/utils/safe-values.ts')

    // Step 4: Update types to match database schema
    console.log('\n📝 Step 4: Updating TypeScript types...')
    
    const updatedInventoryType = `
export interface InventoryItem {
  id: string
  item_code?: string
  name: string
  description?: string
  category?: string
  category_id?: string
  metal_type: string
  purity: string
  
  // Weight information (all optional as they might be null in DB)
  gross_weight?: number
  stone_weight?: number
  net_weight?: number
  diamond_weight?: number
  wastage_weight?: number
  chargeable_weight?: number
  
  // Pricing information (all optional as they might be null in DB)
  purchase_rate?: number
  metal_amount?: number
  making_charges?: number
  stone_charges?: number
  other_charges?: number
  total_cost?: number
  selling_price?: number
  mrp?: number
  
  // Additional information
  size?: string
  gender?: string
  occasion?: string
  design_number?: string
  location?: string
  hsn_code?: string
  
  // Status and inventory
  status?: string
  condition_status?: string
  stock_quantity?: number
  min_stock_level?: number
  
  // Hallmarking
  requires_hallmarking?: boolean
  is_hallmarked?: boolean
  hallmark_number?: string
  
  // Audit fields
  created_at?: string
  updated_at?: string
  created_by?: string
  
  // Legacy fields for backward compatibility
  stoneAmount?: number // mapped from stone_charges
  currentValue?: number // mapped from selling_price
  stock?: number // mapped from stock_quantity
  stoneDetails?: string // mapped from description
  hsnCode?: string // mapped from hsn_code
  metalType?: string // mapped from metal_type
  grossWeight?: number // mapped from gross_weight
  stoneWeight?: number // mapped from stone_weight
  netWeight?: number // mapped from net_weight
  makingCharges?: number // mapped from making_charges
}
`

    console.log('   ✅ Updated InventoryItem interface to match database schema')

    // Step 5: Create component fixes
    console.log('\n🔧 Step 5: Creating component fixes...')
    
    const dashboardFixes = `
// Dashboard component fixes
import { formatCurrency, formatWeight, safeNumber } from '@/lib/utils/safe-values'

// Fixed inventory columns for dashboard
const inventoryColumns = [
  { key: "item_code", label: "Item Code" },
  { key: "name", label: "Name" },
  { key: "metal_type", label: "Metal" },
  { key: "purity", label: "Purity" },
  {
    key: "gross_weight",
    label: "Gross Weight",
    render: (item: InventoryItem) => formatWeight(item.gross_weight),
  },
  {
    key: "stone_weight", 
    label: "Stone Weight",
    render: (item: InventoryItem) => formatWeight(item.stone_weight),
  },
  {
    key: "stone_charges",
    label: "Stone Amount", 
    render: (item: InventoryItem) => formatCurrency(item.stone_charges || item.stoneAmount),
  },
  {
    key: "making_charges",
    label: "Making Charges",
    render: (item: InventoryItem) => formatCurrency(item.making_charges || item.makingCharges),
  },
  {
    key: "selling_price",
    label: "Current Value",
    render: (item: InventoryItem) => formatCurrency(item.selling_price || item.currentValue),
  },
  {
    key: "stock_quantity",
    label: "Stock",
    render: (item: InventoryItem) => safeNumber(item.stock_quantity || item.stock).toString(),
  },
]
`

    console.log('   ✅ Created dashboard component fixes')

    // Step 6: Generate comprehensive fix script
    console.log('\n📋 Step 6: Generating comprehensive fix recommendations...')
    
    const fixRecommendations = `
COMPREHENSIVE COMPONENT FIX RECOMMENDATIONS:

1. IMMEDIATE FIXES NEEDED:
   - components/dashboard.tsx: Lines 313, 319 - Add null checks
   - All components using InventoryItem: Add safe value handling
   - API responses: Ensure proper field mapping from database

2. UTILITY FUNCTIONS CREATED:
   - lib/utils/safe-values.ts: Safe value handling functions
   - formatCurrency(): Safe currency formatting
   - formatWeight(): Safe weight formatting  
   - safeNumber(): Safe number conversion

3. TYPE UPDATES NEEDED:
   - lib/types.ts: Update InventoryItem interface
   - Make all numeric fields optional
   - Add database field mappings

4. COMPONENT UPDATES NEEDED:
   - Import safe value utilities
   - Replace direct property access with safe functions
   - Handle both database and legacy field names

5. API UPDATES NEEDED:
   - Ensure proper field mapping from database
   - Handle null values in responses
   - Add data transformation layer
`

    console.log(fixRecommendations)

    console.log('\n🎉 Component Analysis and Fix Generation Completed!')

  } catch (error) {
    console.error('\n❌ Component fix analysis failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the component fix analysis
fixAllComponents().catch(console.error)
