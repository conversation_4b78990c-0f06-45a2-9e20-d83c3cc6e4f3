#!/usr/bin/env tsx

// Load environment variables FIRST before any other imports
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

// Environment variables loaded

// Now import database modules
import { testConnection, resetPool } from '../lib/database/config'
import { 
  userService, 
  customerService, 
  inventoryService, 
  salesService,
  settingsService 
} from '../lib/database/services'

async function testDatabaseOperations() {
  console.log('🧪 Testing database operations...\n')

  try {
    // Reset pool to ensure fresh configuration
    resetPool()
    // Test 1: Database Connection
    console.log('1. Testing database connection...')
    const isConnected = await testConnection()
    if (!isConnected) {
      throw new Error('Database connection failed')
    }
    console.log('✅ Database connection successful\n')

    // Test 2: User Operations
    console.log('2. Testing user operations...')
    const users = await userService.findAll()
    console.log(`✅ Found ${users.length} users`)
    
    if (users.length > 0) {
      const firstUser = users[0]
      console.log(`✅ First user: ${firstUser.name} (${firstUser.email})`)
      
      // Test login
      const loginResult = await userService.validatePassword(firstUser.email, 'admin123')
      if (loginResult) {
        console.log('✅ User login validation successful')
      } else {
        console.log('⚠️  User login validation failed (expected if password was changed)')
      }
    }
    console.log()

    // Test 3: Customer Operations
    console.log('3. Testing customer operations...')
    const customers = await customerService.findAll()
    console.log(`✅ Found ${customers.length} customers`)
    
    if (customers.length > 0) {
      const firstCustomer = customers[0]
      console.log(`✅ First customer: ${firstCustomer.name} (${firstCustomer.phone})`)
    }
    console.log()

    // Test 4: Inventory Operations
    console.log('4. Testing inventory operations...')
    const inventory = await inventoryService.findAll()
    console.log(`✅ Found ${inventory.length} inventory items`)
    
    if (inventory.length > 0) {
      const firstItem = inventory[0]
      console.log(`✅ First item: ${firstItem.name} (Stock: ${firstItem.stock})`)
      
      // Test low stock
      const lowStockItems = await inventoryService.findLowStock(10)
      console.log(`✅ Found ${lowStockItems.length} low stock items`)
    }
    console.log()

    // Test 5: Sales Operations
    console.log('5. Testing sales operations...')
    const sales = await salesService.findAll()
    console.log(`✅ Found ${sales.length} sales records`)
    
    // Test sales stats
    const salesStats = await salesService.getSalesStats()
    console.log(`✅ Sales stats: ${salesStats.totalSales} total sales, ₹${salesStats.totalRevenue} revenue`)
    console.log()

    // Test 6: Settings Operations
    console.log('6. Testing settings operations...')
    const settings = await settingsService.getSettings()
    console.log(`✅ Settings loaded: ${settings.businessName}`)
    
    const goldRate = await settingsService.getMetalRate('gold', '22K')
    console.log(`✅ Gold 22K rate: ₹${goldRate}/gram`)
    console.log()

    // Test 7: Create and Delete Test Record
    console.log('7. Testing CRUD operations...')
    
    // Create test customer
    const testCustomer = await customerService.create({
      name: 'Test Customer',
      phone: '+91 99999 99999',
      email: '<EMAIL>',
      address: 'Test Address',
      totalPurchases: 0,
      lastVisit: new Date().toISOString()
    })
    console.log(`✅ Created test customer: ${testCustomer.name}`)
    
    // Update test customer
    const updatedCustomer = await customerService.update(testCustomer.id, {
      name: 'Updated Test Customer'
    })
    if (updatedCustomer) {
      console.log(`✅ Updated customer name: ${updatedCustomer.name}`)
    }
    
    // Delete test customer
    const deleted = await customerService.delete(testCustomer.id)
    if (deleted) {
      console.log('✅ Deleted test customer')
    }
    console.log()

    console.log('🎉 All database tests passed successfully!')
    return true

  } catch (error) {
    console.error('❌ Database test failed:', error)
    return false
  }
}

async function main() {
  const success = await testDatabaseOperations()
  process.exit(success ? 0 : 1)
}

main()
