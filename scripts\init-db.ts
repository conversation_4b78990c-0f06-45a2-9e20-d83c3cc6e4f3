#!/usr/bin/env tsx

// Load environment variables FIRST before any other imports
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

// Debug environment variables
console.log('Environment variables loaded:')
console.log('DB_USER:', process.env.DB_USER)
console.log('DB_NAME:', process.env.DB_NAME)
console.log('DB_PASSWORD:', process.env.DB_PASSWORD === '' ? '[EMPTY]' : process.env.DB_PASSWORD ? '[SET]' : '[NOT SET]')
console.log()

// Now import database modules
import { databaseSeeder } from '../lib/database/seed'

async function main() {
  try {
    console.log('Starting database initialization...')
    await databaseSeeder.initializeDatabase()
    console.log('Database initialization completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('Database initialization failed:', error)
    process.exit(1)
  }
}

main()
