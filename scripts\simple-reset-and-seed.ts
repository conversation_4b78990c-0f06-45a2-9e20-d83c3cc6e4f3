import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function resetAndSeedDatabase() {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔄 Connecting to database...')
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jjjewellers_db',
      port: parseInt(process.env.DB_PORT || '3306')
    })

    console.log('✅ Connected to database')

    // Clear existing data (except users and settings)
    console.log('🧹 Clearing existing data...')
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    
    const tables = ['sale_items', 'sales', 'repairs', 'schemes', 'purchases', 'inventory', 'customers', 'categories']
    for (const table of tables) {
      try {
        await connection.execute(`DELETE FROM ${table}`)
        console.log(`✅ Cleared ${table}`)
      } catch (error) {
        console.log(`⚠️  Table ${table} might not exist, skipping...`)
      }
    }
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
    console.log('✅ Existing data cleared')

    // 1. Seed Categories
    console.log('📂 Seeding categories...')
    const categories = [
      {
        id: randomUUID(),
        name: 'Rings',
        description: 'Wedding rings, engagement rings, and fashion rings',
        is_active: true,
        sort_order: 1,
        tags: JSON.stringify(['wedding', 'engagement', 'fashion']),
        making_charge_percentage: 15.00,
        wastage_percentage: 2.00
      },
      {
        id: randomUUID(),
        name: 'Necklaces',
        description: 'Gold and silver necklaces, chains, and pendants',
        is_active: true,
        sort_order: 2,
        tags: JSON.stringify(['chains', 'pendants', 'traditional']),
        making_charge_percentage: 12.00,
        wastage_percentage: 1.50
      },
      {
        id: randomUUID(),
        name: 'Earrings',
        description: 'Studs, hoops, and traditional earrings',
        is_active: true,
        sort_order: 3,
        tags: JSON.stringify(['studs', 'hoops', 'traditional', 'modern']),
        making_charge_percentage: 18.00,
        wastage_percentage: 2.50
      },
      {
        id: randomUUID(),
        name: 'Bracelets',
        description: 'Gold and silver bracelets and bangles',
        is_active: true,
        sort_order: 4,
        tags: JSON.stringify(['bangles', 'charm', 'tennis']),
        making_charge_percentage: 14.00,
        wastage_percentage: 2.00
      },
      {
        id: randomUUID(),
        name: 'Pendants',
        description: 'Religious and fashion pendants',
        is_active: true,
        sort_order: 5,
        tags: JSON.stringify(['religious', 'fashion', 'custom']),
        making_charge_percentage: 16.00,
        wastage_percentage: 1.80
      },
      {
        id: randomUUID(),
        name: 'Chains',
        description: 'Gold and silver chains of various designs',
        is_active: true,
        sort_order: 6,
        tags: JSON.stringify(['rope', 'box', 'figaro', 'curb']),
        making_charge_percentage: 10.00,
        wastage_percentage: 1.00
      }
    ]

    for (const category of categories) {
      await connection.execute(
        `INSERT INTO categories (id, name, description, is_active, sort_order, tags, making_charge_percentage, wastage_percentage, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [category.id, category.name, category.description, category.is_active, category.sort_order, category.tags, category.making_charge_percentage, category.wastage_percentage]
      )
    }
    console.log(`✅ Created ${categories.length} categories`)

    // 2. Seed Customers
    console.log('👥 Seeding customers...')
    const customers = [
      {
        id: randomUUID(),
        name: 'Rajesh Kumar',
        phone: '+91 98765 43210',
        email: '<EMAIL>',
        address: '123 MG Road, Mumbai, Maharashtra 400001',
        gst_number: '27ABCDE1234F1Z5',
        total_purchases: 125000,
        last_visit: new Date().toISOString().split('T')[0]
      },
      {
        id: randomUUID(),
        name: 'Priya Sharma',
        phone: '+91 87654 32109',
        email: '<EMAIL>',
        address: '456 Park Street, Delhi, Delhi 110001',
        gst_number: null,
        total_purchases: 85000,
        last_visit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      {
        id: randomUUID(),
        name: 'Amit Patel',
        phone: '+91 76543 21098',
        email: '<EMAIL>',
        address: '789 Commercial Street, Bangalore, Karnataka 560001',
        gst_number: '29FGHIJ5678K2L6',
        total_purchases: 195000,
        last_visit: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      {
        id: randomUUID(),
        name: 'Sunita Reddy',
        phone: '+91 65432 10987',
        email: '<EMAIL>',
        address: '321 Tank Bund Road, Hyderabad, Telangana 500001',
        gst_number: null,
        total_purchases: 67000,
        last_visit: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      },
      {
        id: randomUUID(),
        name: 'Vikram Singh',
        phone: '+91 54321 09876',
        email: '<EMAIL>',
        address: '654 Civil Lines, Jaipur, Rajasthan 302001',
        gst_number: '08KLMNO9012P3Q4',
        total_purchases: 310000,
        last_visit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }
    ]

    for (const customer of customers) {
      await connection.execute(
        `INSERT INTO customers (id, name, phone, email, address, gst_number, total_purchases, last_visit, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [customer.id, customer.name, customer.phone, customer.email, customer.address, customer.gst_number, customer.total_purchases, customer.last_visit]
      )
    }
    console.log(`✅ Created ${customers.length} customers`)

    // 3. Seed Inventory Items (linked to categories)
    console.log('💍 Seeding inventory items...')
    const inventoryItems = [
      // Rings
      {
        id: randomUUID(),
        name: 'Diamond Engagement Ring 18K',
        category: 'Rings',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 4.5,
        stone_weight: 0.8,
        net_weight: 3.7,
        making_charges: 2500,
        stone_amount: 25000,
        current_value: 45000,
        stock: 8,
        stone_details: '0.5ct Diamond',
        description: 'Beautiful 18K gold engagement ring with solitaire diamond'
      },
      {
        id: randomUUID(),
        name: 'Gold Wedding Band 22K',
        category: 'Rings',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 6.2,
        stone_weight: 0.0,
        net_weight: 6.2,
        making_charges: 1200,
        stone_amount: 0,
        current_value: 28000,
        stock: 15,
        stone_details: '',
        description: 'Classic 22K gold wedding band'
      },
      // Necklaces
      {
        id: randomUUID(),
        name: 'Traditional Gold Necklace 22K',
        category: 'Necklaces',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 25.5,
        stone_weight: 2.5,
        net_weight: 23.0,
        making_charges: 8500,
        stone_amount: 15000,
        current_value: 189750,
        stock: 3,
        stone_details: 'Ruby and Emerald',
        description: 'Traditional South Indian gold necklace with precious stones'
      },
      {
        id: randomUUID(),
        name: 'Silver Chain Necklace',
        category: 'Necklaces',
        metal_type: 'silver',
        purity: '925',
        gross_weight: 18.0,
        stone_weight: 0.0,
        net_weight: 18.0,
        making_charges: 1500,
        stone_amount: 0,
        current_value: 4500,
        stock: 12,
        stone_details: '',
        description: '925 sterling silver chain necklace'
      },
      // Earrings
      {
        id: randomUUID(),
        name: 'Diamond Stud Earrings',
        category: 'Earrings',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 3.2,
        stone_weight: 0.6,
        net_weight: 2.6,
        making_charges: 3500,
        stone_amount: 18000,
        current_value: 35000,
        stock: 6,
        stone_details: '0.3ct each Diamond',
        description: 'Elegant 18K gold diamond stud earrings'
      },
      {
        id: randomUUID(),
        name: 'Gold Hoop Earrings 22K',
        category: 'Earrings',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 8.3,
        stone_weight: 0.0,
        net_weight: 8.3,
        making_charges: 2200,
        stone_amount: 0,
        current_value: 38500,
        stock: 10,
        stone_details: '',
        description: 'Classic 22K gold hoop earrings'
      },
      // Bracelets
      {
        id: randomUUID(),
        name: 'Gold Tennis Bracelet',
        category: 'Bracelets',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 12.0,
        stone_weight: 1.5,
        net_weight: 10.5,
        making_charges: 4500,
        stone_amount: 22000,
        current_value: 75000,
        stock: 4,
        stone_details: 'Multiple small diamonds',
        description: '18K gold tennis bracelet with diamonds'
      },
      {
        id: randomUUID(),
        name: 'Silver Charm Bracelet',
        category: 'Bracelets',
        metal_type: 'silver',
        purity: '925',
        gross_weight: 15.0,
        stone_weight: 0.0,
        net_weight: 15.0,
        making_charges: 800,
        stone_amount: 0,
        current_value: 3200,
        stock: 8,
        stone_details: '',
        description: '925 sterling silver charm bracelet'
      }
    ]

    for (const item of inventoryItems) {
      await connection.execute(
        `INSERT INTO inventory (id, name, category, metal_type, purity, gross_weight, stone_weight, net_weight, making_charges, stone_amount, current_value, stock, stone_details, description, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [item.id, item.name, item.category, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.making_charges, item.stone_amount, item.current_value, item.stock, item.stone_details, item.description]
      )
    }
    console.log(`✅ Created ${inventoryItems.length} inventory items`)

    console.log('🎉 Database reset and seeding completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`- ${categories.length} Categories`)
    console.log(`- ${customers.length} Customers`)
    console.log(`- ${inventoryItems.length} Inventory Items`)
    console.log('\n🔑 Login with: <EMAIL> / admin123')

  } catch (error) {
    console.error('❌ Error:', error)
    return false
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }

  return true
}

async function main() {
  const success = await resetAndSeedDatabase()
  process.exit(success ? 0 : 1)
}

main()
