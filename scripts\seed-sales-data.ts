#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedSalesData() {
  console.log('🛍️  Seeding Comprehensive Sales Data for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Get reference data
    const [users] = await connection.execute('SELECT id, username, role FROM users')
    const [customers] = await connection.execute('SELECT id, customer_code, first_name, last_name, phone FROM customers')
    const [inventory] = await connection.execute(`
      SELECT id, item_code, name, selling_price, metal_type, purity, gross_weight, stone_weight, 
             wastage_percentage, making_charges, stone_charges, hsn_code
      FROM inventory WHERE status = "active"
    `)

    const userMap = new Map()
    const customerMap = new Map()
    const inventoryMap = new Map()

    ;(users as any[]).forEach(user => userMap.set(user.username, user))
    ;(customers as any[]).forEach(customer => customerMap.set(customer.customer_code, customer))
    ;(inventory as any[]).forEach(item => inventoryMap.set(item.item_code, item))

    // Get current financial year
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`

    console.log('🛒 Creating comprehensive sales transactions...')
    
    // Create realistic sales transactions
    const salesTransactions = [
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/001`,
        customer_id: customerMap.get('TRP000002')?.id,
        customer_name: 'Priya Selvam',
        customer_phone: '+91-9876543302',
        sale_type: 'cash',
        payment_mode: 'upi',
        subtotal: 42000.00,
        discount_percentage: 5.00,
        discount_amount: 2100.00,
        packing_charges: 200.00,
        sales_person_id: userMap.get('sales1')?.id,
        cashier_id: userMap.get('cashier1')?.id,
        notes: 'Gold earrings for festival occasion'
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/002`,
        customer_id: customerMap.get('TRP000004')?.id,
        customer_name: 'Kavitha Devi',
        customer_phone: '+91-9876543304',
        sale_type: 'cash',
        payment_mode: 'cash',
        subtotal: 85000.00,
        discount_percentage: 3.00,
        discount_amount: 2550.00,
        packing_charges: 300.00,
        sales_person_id: userMap.get('sales2')?.id,
        cashier_id: userMap.get('cashier1')?.id,
        notes: 'Gold ring with diamond for engagement'
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/003`,
        customer_id: customerMap.get('TRP000001')?.id,
        customer_name: 'Rajesh Murugan',
        customer_phone: '+91-9876543301',
        sale_type: 'exchange',
        payment_mode: 'mixed',
        subtotal: 420000.00,
        discount_percentage: 2.00,
        discount_amount: 8400.00,
        exchange_deduction: 285000.00,
        packing_charges: 500.00,
        sales_person_id: userMap.get('sales_manager')?.id,
        cashier_id: userMap.get('cashier2')?.id,
        manager_approval_id: userMap.get('manager')?.id,
        notes: 'Gold necklace with exchange of old jewelry'
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/004`,
        customer_id: customerMap.get('TRP000006')?.id,
        customer_name: 'Lakshmi Narayanan',
        customer_phone: '+91-9876543306',
        sale_type: 'cash',
        payment_mode: 'card',
        subtotal: 520000.00,
        discount_percentage: 4.00,
        discount_amount: 20800.00,
        packing_charges: 800.00,
        sales_person_id: userMap.get('sales1')?.id,
        cashier_id: userMap.get('cashier1')?.id,
        manager_approval_id: userMap.get('manager')?.id,
        notes: 'Gold bangles pair for wedding ceremony'
      },
      {
        id: randomUUID(),
        invoice_number: `INV/${financialYear}/005`,
        customer_id: customerMap.get('TRP000007')?.id,
        customer_name: 'Arjun Prasad',
        customer_phone: '+91-**********',
        sale_type: 'cash',
        payment_mode: 'bank_transfer',
        subtotal: 210000.00,
        discount_percentage: 2.50,
        discount_amount: 5250.00,
        packing_charges: 400.00,
        sales_person_id: userMap.get('sales2')?.id,
        cashier_id: userMap.get('cashier2')?.id,
        notes: 'Gold chain for personal use'
      }
    ]

    // Insert sales transactions
    for (const sale of salesTransactions) {
      await connection.execute(`
        INSERT INTO sales (
          id, invoice_number, invoice_date, customer_id, customer_name, customer_phone,
          sale_type, sale_category, payment_mode, subtotal, discount_type, discount_percentage,
          discount_amount, packing_charges, exchange_deduction, payment_status, paid_amount,
          status, sales_person_id, cashier_id, manager_approval_id, notes,
          loyalty_points_earned, created_by, created_at, updated_at
        ) VALUES (?, ?, CURDATE(), ?, ?, ?, ?, 'retail', ?, ?, 'percentage', ?, ?, ?, ?, 'paid', ?, 'confirmed', ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        sale.id, sale.invoice_number, sale.customer_id, sale.customer_name, sale.customer_phone,
        sale.sale_type, sale.payment_mode, sale.subtotal, sale.discount_percentage,
        sale.discount_amount, sale.packing_charges || 0.00, sale.exchange_deduction || 0.00,
        sale.subtotal - (sale.discount_amount || 0.00) + (sale.packing_charges || 0.00) - (sale.exchange_deduction || 0.00),
        sale.sales_person_id, sale.cashier_id, sale.manager_approval_id, sale.notes,
        Math.floor((sale.subtotal - (sale.discount_amount || 0.00)) / 1000), sale.sales_person_id
      ])
    }
    console.log(`   ✅ Created ${salesTransactions.length} sales transactions`)

    // Create sale items for each transaction
    console.log('\n📦 Creating sale items...')
    
    const saleItems = [
      // Sale 1 - Gold Earrings
      {
        sale_id: salesTransactions[0].id,
        line_number: 1,
        inventory_id: inventoryMap.get('GE001')?.id,
        item_code: 'GE001',
        item_name: 'Gold Earrings Stud Simple',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 4.500,
        stone_weight: 0.000,
        wastage_percentage: 8.00,
        rate_per_gram: 6740.00,
        making_charges: 8000.00,
        final_amount: 42000.00,
        hsn_code: '71131200'
      },
      
      // Sale 2 - Gold Diamond Ring
      {
        sale_id: salesTransactions[1].id,
        line_number: 1,
        inventory_id: inventoryMap.get('GR002')?.id,
        item_code: 'GR002',
        item_name: 'Gold Diamond Ring Designer',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 6.200,
        stone_weight: 0.500,
        diamond_weight: 0.250,
        wastage_percentage: 8.00,
        rate_per_gram: 5510.00,
        making_charges: 15000.00,
        stone_charges: 35000.00,
        final_amount: 85000.00,
        hsn_code: '71131900'
      },
      
      // Sale 3 - Gold Necklace Heavy
      {
        sale_id: salesTransactions[2].id,
        line_number: 1,
        inventory_id: inventoryMap.get('GN001')?.id,
        item_code: 'GN001',
        item_name: 'Gold Chain Necklace Heavy',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 45.000,
        stone_weight: 0.000,
        wastage_percentage: 12.00,
        rate_per_gram: 6740.00,
        making_charges: 60000.00,
        final_amount: 420000.00,
        hsn_code: '71131100'
      },
      
      // Sale 4 - Gold Bangles
      {
        sale_id: salesTransactions[3].id,
        line_number: 1,
        inventory_id: inventoryMap.get('GB001')?.id,
        item_code: 'GB001',
        item_name: 'Gold Bangles Pair Traditional',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 65.000,
        stone_weight: 0.000,
        wastage_percentage: 6.00,
        rate_per_gram: 6740.00,
        making_charges: 55000.00,
        final_amount: 520000.00,
        hsn_code: '71131300'
      },
      
      // Sale 5 - Gold Chain Mens
      {
        sale_id: salesTransactions[4].id,
        line_number: 1,
        inventory_id: inventoryMap.get('GC001')?.id,
        item_code: 'GC001',
        item_name: 'Gold Chain Mens Heavy',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 28.000,
        stone_weight: 0.000,
        wastage_percentage: 4.00,
        rate_per_gram: 6740.00,
        making_charges: 15000.00,
        final_amount: 210000.00,
        hsn_code: '71131400'
      }
    ]

    // Insert sale items
    for (const item of saleItems) {
      await connection.execute(`
        INSERT INTO sale_items (
          id, sale_id, line_number, inventory_id, item_code, item_name, description,
          metal_type, purity, gross_weight, stone_weight, diamond_weight,
          wastage_percentage, rate_per_gram, making_charges, stone_charges,
          discount_percentage, discount_amount, quantity, hsn_code, status,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0.00, 0.00, 1, ?, 'active', NOW(), NOW())
      `, [
        randomUUID(), item.sale_id, item.line_number, item.inventory_id, item.item_code,
        item.item_name, item.item_name, item.metal_type, item.purity, item.gross_weight,
        item.stone_weight, item.diamond_weight || 0.000, item.wastage_percentage,
        item.rate_per_gram, item.making_charges, item.stone_charges || 0.00, item.hsn_code
      ])
    }
    console.log(`   ✅ Created ${saleItems.length} sale items`)

    // Update inventory status for sold items
    console.log('\n📦 Updating inventory status...')
    const soldItemCodes = ['GE001', 'GR002', 'GN001', 'GB001', 'GC001']
    for (const itemCode of soldItemCodes) {
      await connection.execute(`
        UPDATE inventory SET status = 'sold', stock_quantity = 0, updated_at = NOW() 
        WHERE item_code = ?
      `, [itemCode])
    }
    console.log(`   ✅ Updated ${soldItemCodes.length} inventory items to sold status`)

    console.log('\n🎉 Comprehensive Sales Data Seeded Successfully!')

  } catch (error) {
    console.error('\n❌ Sales data seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the sales data seeding
seedSalesData().catch(console.error)
