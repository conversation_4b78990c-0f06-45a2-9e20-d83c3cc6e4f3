"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import {
  Gem,
  Users,
  ShoppingCart,
  TrendingUp,
  Package,
  IndianRupee,
  Plus,
  FileText,
  Settings,
  BarChart3,
  CreditCard,
  Wrench,
  QrCode,
  Calculator,
  Clock,
  CheckCircle,
  AlertCircle,
  Scale,
  LogOut,
} from "lucide-react"
import { useState, useEffect } from "react"
import { useStore } from "@/lib/store"
import { RateCalculator } from "@/components/rate-calculator"
import { QuickActions } from "@/components/quick-actions"
import { Notifications } from "@/components/notifications"
import { DataTable } from "@/components/ui/data-table"
import { ConfirmDialog } from "@/components/ui/confirm-dialog"
import { InventoryForm } from "@/components/forms/inventory-form"
import { CustomerForm } from "@/components/forms/customer-form"
import { RepairForm } from "@/components/forms/repair-form"
import { SchemeForm } from "@/components/forms/scheme-form"
import type { InventoryItem, Customer, RepairOrder, Scheme, Sale, Purchase } from "@/lib/types"
import { WeightCalculator } from "@/components/weight-calculator"
import { SaleForm } from "@/components/forms/sale-form"
import { WeightReport } from "@/components/reports/weight-report"
import { BarcodeScanner } from "@/components/barcode-scanner"
import { PurchaseForm } from "@/components/forms/purchase-form"
import { SalesReport } from "@/components/reports/sales-report"
import { InventoryReport } from "@/components/reports/inventory-report"
import { CustomerReport } from "@/components/reports/customer-report"
import { SettingsPanel } from "@/components/settings/settings-panel"
import { InvoiceActions } from "@/components/invoice/invoice-actions"
import { CategoriesPage } from "@/components/categories/categories-page"

interface DashboardProps {
  onLogout: () => void
}

export function Dashboard({ onLogout }: DashboardProps) {
  const {
    inventory,
    customers,
    repairs,
    schemes,
    purchases,
    deleteInventoryItem,
    deleteCustomer,
    deleteRepair,
    deleteScheme,
    deleteSale,
    deletePurchase,
    sales,
    settings,
    currentUser,
    hasPermission,
    logout,
  } = useStore()

  const [activeTab, setActiveTab] = useState("dashboard")
  const [isAddItemOpen, setIsAddItemOpen] = useState(false)
  const [isAddCustomerOpen, setIsAddCustomerOpen] = useState(false)
  const [isRepairDialogOpen, setIsRepairDialogOpen] = useState(false)
  const [isSchemeDialogOpen, setIsSchemeDialogOpen] = useState(false)
  const [isSaleDialogOpen, setIsSaleDialogOpen] = useState(false)
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false)

  const [editingItem, setEditingItem] = useState<InventoryItem | undefined>()
  const [editingCustomer, setEditingCustomer] = useState<Customer | undefined>()
  const [editingRepair, setEditingRepair] = useState<RepairOrder | undefined>()
  const [editingScheme, setEditingScheme] = useState<Scheme | undefined>()
  const [editingSale, setEditingSale] = useState<Sale | undefined>()
  const [editingPurchase, setEditingPurchase] = useState<Purchase | undefined>()

  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => void
  }>({
    open: false,
    title: "",
    description: "",
    onConfirm: () => {},
  })

  const [activeReport, setActiveReport] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)

  // Data loading is handled by DatabaseProvider, no need to load here

  const handleLogout = () => {
    logout()
    onLogout()
  }

  const handleQuickAction = (action: string) => {
    switch (action) {
      case "add-customer":
        if (hasPermission("customers", "create")) {
          setIsAddCustomerOpen(true)
        } else {
          alert("You don't have permission to add customers")
        }
        break
      case "add-item":
        if (hasPermission("inventory", "create")) {
          setIsAddItemOpen(true)
        } else {
          alert("You don't have permission to add inventory items")
        }
        break
      case "new-sale":
        if (hasPermission("sales", "create")) {
          setIsSaleDialogOpen(true)
        } else {
          alert("You don't have permission to create sales")
        }
        break
      case "new-scheme":
        if (hasPermission("schemes", "create")) {
          setIsSchemeDialogOpen(true)
        } else {
          alert("You don't have permission to create schemes")
        }
        break
      case "repair-order":
        if (hasPermission("repairs", "create")) {
          setIsRepairDialogOpen(true)
        } else {
          alert("You don't have permission to create repair orders")
        }
        break
      case "scan-barcode":
        setActiveTab("barcode")
        break
      case "generate-report":
        if (hasPermission("reports", "read")) {
          setActiveTab("reports")
        } else {
          alert("You don't have permission to view reports")
        }
        break
      case "rate-calculator":
        setActiveTab("rate-calculator")
        break
      default:
        console.log("Unknown action:", action)
    }
  }

  const handleDeleteItem = (item: InventoryItem) => {
    if (!hasPermission("inventory", "delete")) {
      alert("You don't have permission to delete inventory items")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Item",
      description: `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deleteInventoryItem(item.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  const handleDeleteCustomer = (customer: Customer) => {
    if (!hasPermission("customers", "delete")) {
      alert("You don't have permission to delete customers")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Customer",
      description: `Are you sure you want to delete "${customer.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deleteCustomer(customer.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  const handleDeleteRepair = (repair: RepairOrder) => {
    if (!hasPermission("repairs", "delete")) {
      alert("You don't have permission to delete repair orders")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Repair Order",
      description: `Are you sure you want to delete repair order "${repair.id}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deleteRepair(repair.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  const handleDeleteScheme = (scheme: Scheme) => {
    if (!hasPermission("schemes", "delete")) {
      alert("You don't have permission to delete schemes")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Scheme",
      description: `Are you sure you want to delete scheme "${scheme.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deleteScheme(scheme.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  const handleDeleteSale = (sale: Sale) => {
    if (!hasPermission("sales", "delete")) {
      alert("You don't have permission to delete sales")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Sale",
      description: `Are you sure you want to delete sale "${sale.id}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deleteSale(sale.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  const handleDeletePurchase = (purchase: Purchase) => {
    if (!hasPermission("purchases", "delete")) {
      alert("You don't have permission to delete purchases")
      return
    }

    setConfirmDialog({
      open: true,
      title: "Delete Purchase",
      description: `Are you sure you want to delete purchase "${purchase.id}"? This action cannot be undone.`,
      onConfirm: async () => {
        await deletePurchase(purchase.id)
        setConfirmDialog({ ...confirmDialog, open: false })
      },
    })
  }

  // Column definitions for tables
  const inventoryColumns = [
    {
      key: "id",
      label: "Item Code",
      render: (item: InventoryItem) => (
        <div className="font-mono text-sm">
          <div className="font-medium">{item.id}</div>
          {item.hsnCode && (
            <div className="text-xs text-muted-foreground">HSN: {item.hsnCode}</div>
          )}
        </div>
      )
    },
    { key: "name", label: "Name" },
    {
      key: "category",
      label: "Category",
      render: (item: InventoryItem) => <Badge variant="outline">{item.category}</Badge>,
    },
    { key: "grossWeight", label: "Gross Weight", render: (item: InventoryItem) => `${item.grossWeight}g` },
    { key: "stoneWeight", label: "Stone Weight", render: (item: InventoryItem) => `${item.stoneWeight}g` },
    {
      key: "netWeight",
      label: "Net Weight",
      render: (item: InventoryItem) => <span className="font-medium text-blue-600">{item.netWeight}g</span>,
    },
    {
      key: "stoneAmount",
      label: "Stone Amount",
      render: (item: InventoryItem) => `₹${item.stoneAmount.toLocaleString()}`,
    },
    { key: "purity", label: "Purity" },
    {
      key: "makingCharges",
      label: "Making Charges",
      render: (item: InventoryItem) => `₹${item.makingCharges.toLocaleString()}`,
    },
    {
      key: "currentValue",
      label: "Current Value",
      render: (item: InventoryItem) => `₹${item.currentValue.toLocaleString()}`,
    },
    {
      key: "stock",
      label: "Stock",
      render: (item: InventoryItem) => <Badge variant={item.stock < 5 ? "destructive" : "default"}>{item.stock}</Badge>,
    },
  ]

  const customerColumns = [
    { key: "id", label: "Customer ID" },
    { key: "name", label: "Name" },
    { key: "phone", label: "Phone" },
    { key: "email", label: "Email" },
    {
      key: "totalPurchases",
      label: "Total Purchases",
      render: (customer: Customer) => `₹${customer.totalPurchases.toLocaleString()}`,
    },
    { key: "lastVisit", label: "Last Visit" },
  ]

  const repairColumns = [
    { key: "id", label: "Order ID" },
    { key: "customer.name", label: "Customer" },
    { key: "item", label: "Item" },
    {
      key: "description",
      label: "Description",
      render: (repair: RepairOrder) => <div className="max-w-xs truncate">{repair.description}</div>,
    },
    { key: "receivedDate", label: "Received" },
    { key: "promisedDate", label: "Promised" },
    { key: "charges", label: "Charges", render: (repair: RepairOrder) => `₹${repair.charges}` },
    {
      key: "status",
      label: "Status",
      render: (repair: RepairOrder) => (
        <Badge variant={repair.status === "completed" ? "default" : "secondary"}>{repair.status}</Badge>
      ),
    },
  ]

  const schemeColumns = [
    { key: "id", label: "Scheme ID" },
    { key: "customer.name", label: "Customer" },
    { key: "name", label: "Scheme Name" },
    {
      key: "totalAmount",
      label: "Total Amount",
      render: (scheme: Scheme) => `₹${scheme.totalAmount.toLocaleString()}`,
    },
    { key: "paidAmount", label: "Paid Amount", render: (scheme: Scheme) => `₹${scheme.paidAmount.toLocaleString()}` },
    {
      key: "progress",
      label: "Progress",
      render: (scheme: Scheme) => (
        <div className="space-y-1">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-600 h-2 rounded-full"
              style={{ width: `${(scheme.paidAmount / scheme.totalAmount) * 100}%` }}
            ></div>
          </div>
          <span className="text-xs text-muted-foreground">
            {Math.round((scheme.paidAmount / scheme.totalAmount) * 100)}%
          </span>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (scheme: Scheme) => <Badge variant="default">{scheme.status}</Badge>,
    },
  ]

  const salesColumns = [
    { key: "id", label: "Invoice No." },
    { key: "date", label: "Date" },
    { key: "customer.name", label: "Customer" },
    {
      key: "netWeight",
      label: "Net Weight",
      render: (sale: Sale) => (
        <span className="font-medium text-blue-600">
          {sale.items.reduce((sum, item) => sum + item.netWeight, 0).toFixed(1)}g
        </span>
      ),
    },
    { key: "items.length", label: "Items", render: (sale: Sale) => `${sale.items.length} items` },
    { key: "total", label: "Amount", render: (sale: Sale) => `₹${sale.total.toLocaleString()}` },
    {
      key: "status",
      label: "Status",
      render: (sale: Sale) => <Badge variant="default">{sale.status}</Badge>,
    },
    {
      key: "actions",
      label: "Actions",
      render: (sale: Sale) => {
        const customer = sale.customer
        if (!customer) return null

        const saleItems = sale.items.map(item => ({
          item: item.item,
          quantity: 1, // Default quantity since it's not in the type
          weight: item.netWeight,
          rate: item.rate,
          amount: item.amount
        }))

        return (
          <InvoiceActions
            sale={sale}
            customer={customer}
            items={saleItems}
          />
        )
      },
    },
  ]

  const purchaseColumns = [
    { key: "id", label: "Purchase ID" },
    { key: "date", label: "Date" },
    { key: "supplier", label: "Supplier" },
    {
      key: "items",
      label: "Items",
      render: (purchase: Purchase) => <div className="max-w-xs truncate">{purchase.items}</div>,
    },
    { key: "amount", label: "Amount", render: (purchase: Purchase) => `₹${purchase.amount.toLocaleString()}` },
    {
      key: "status",
      label: "Status",
      render: (purchase: Purchase) => <Badge variant="default">{purchase.status}</Badge>,
    },
  ]

  return (
    <div className="min-h-screen bg-background">


      {/* Header */}
      <header className="border-b bg-white">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-2">
            <Gem className="h-8 w-8 text-yellow-600" />
            <h1 className="text-2xl font-bold text-gray-900">Shree Jewellers</h1>
          </div>
          <div className="ml-auto flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-yellow-600 font-medium">Gold 22K: ₹{settings.metalRates.gold["22K"]}/g</span>
              <span className="text-gray-400">|</span>
              <span className="text-gray-600 font-medium">Silver 925: ₹{settings.metalRates.silver["925"]}/g</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Welcome, {currentUser?.name}</span>
              <Badge variant="outline">{currentUser?.role}</Badge>
            </div>
            {hasPermission("settings", "read") && (
              <Button variant="outline" size="sm" onClick={() => setShowSettings(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 border-r bg-white h-[calc(100vh-4rem)]">
          <nav className="p-4 space-y-2">
            <Button
              variant={activeTab === "dashboard" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("dashboard")}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Dashboard
            </Button>
            {hasPermission("inventory", "read") && (
              <Button
                variant={activeTab === "inventory" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("inventory")}
              >
                <Package className="h-4 w-4 mr-2" />
                Inventory
              </Button>
            )}
            {hasPermission("customers", "read") && (
              <Button
                variant={activeTab === "customers" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("customers")}
              >
                <Users className="h-4 w-4 mr-2" />
                Customers
              </Button>
            )}
            {hasPermission("sales", "read") && (
              <Button
                variant={activeTab === "sales" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("sales")}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Sales
              </Button>
            )}
            {hasPermission("purchases", "read") && (
              <Button
                variant={activeTab === "purchases" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("purchases")}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Purchases
              </Button>
            )}
            {hasPermission("reports", "read") && (
              <Button
                variant={activeTab === "reports" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("reports")}
              >
                <FileText className="h-4 w-4 mr-2" />
                Reports
              </Button>
            )}
            {hasPermission("schemes", "read") && (
              <Button
                variant={activeTab === "schemes" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("schemes")}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Schemes
              </Button>
            )}
            {hasPermission("repairs", "read") && (
              <Button
                variant={activeTab === "repairs" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("repairs")}
              >
                <Wrench className="h-4 w-4 mr-2" />
                Repairs
              </Button>
            )}
            {(hasPermission("categories", "read") || currentUser?.role === "admin") && (
              <Button
                variant={activeTab === "categories" ? "default" : "ghost"}
                className="w-full justify-start"
                onClick={() => setActiveTab("categories")}
              >
                <Package className="h-4 w-4 mr-2" />
                Categories
              </Button>
            )}
            <Button
              variant={activeTab === "barcode" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("barcode")}
            >
              <QrCode className="h-4 w-4 mr-2" />
              Barcode
            </Button>
            <Button
              variant={activeTab === "rate-calculator" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("rate-calculator")}
            >
              <Calculator className="h-4 w-4 mr-2" />
              Rate Calculator
            </Button>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === "dashboard" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                <p className="text-muted-foreground">Welcome back! Here's what's happening at your store today.</p>
              </div>

              {/* Stats Cards */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Today's Sales</CardTitle>
                    <IndianRupee className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">₹3,50,000</div>
                    <p className="text-xs text-muted-foreground">+12% from yesterday</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{customers.length}</div>
                    <p className="text-xs text-muted-foreground">+5 new today</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Inventory Items</CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{inventory.length}</div>
                    <p className="text-xs text-muted-foreground">Total items in stock</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Schemes</CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{schemes.filter((s) => s.status === "active").length}</div>
                    <p className="text-xs text-muted-foreground">Running schemes</p>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions, Rate Calculator, and Weight Calculator */}
              <div className="grid gap-6 md:grid-cols-3">
                <div>
                  <QuickActions onActionClick={handleQuickAction} />
                </div>
                <div>
                  <RateCalculator />
                </div>
                <div>
                  <WeightCalculator />
                </div>
              </div>

              {/* Notifications */}
              <div className="grid gap-6 md:grid-cols-1">
                <Notifications />
              </div>
            </div>
          )}

          {activeTab === "inventory" && hasPermission("inventory", "read") && (
            <div className="space-y-6">

              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Inventory Management</h2>
                  <p className="text-muted-foreground">Manage your jewelry inventory and stock levels</p>
                </div>
                {hasPermission("inventory", "create") && (
                  <div>
                    <Button onClick={() => setIsAddItemOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>

                    <Dialog
                      open={isAddItemOpen || !!editingItem}
                      onOpenChange={(open) => {
                        if (!open) {
                          setIsAddItemOpen(false)
                          setEditingItem(undefined)
                        }
                      }}
                    >
                    <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>{editingItem ? "Edit Item" : "Add New Jewelry Item"}</DialogTitle>
                        <DialogDescription>
                          {editingItem
                            ? "Update the jewelry item details"
                            : "Enter the details of the new jewelry item"}
                        </DialogDescription>
                      </DialogHeader>
                      <InventoryForm
                        item={editingItem}
                        onSubmit={() => {
                          setIsAddItemOpen(false)
                          setEditingItem(undefined)
                        }}
                        onCancel={() => {
                          setIsAddItemOpen(false)
                          setEditingItem(undefined)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                  </div>
                )}
              </div>

              <DataTable
                data={inventory}
                columns={inventoryColumns}
                onEdit={hasPermission("inventory", "update") ? (item) => {
                  console.log('🎯 Edit button clicked for item:', {
                    id: item.id,
                    name: item.name,
                    category: item.category,
                    metalType: item.metalType,
                    grossWeight: item.grossWeight,
                    stoneWeight: item.stoneWeight,
                    purity: item.purity
                  })
                  setEditingItem(item)
                } : undefined}
                onDelete={hasPermission("inventory", "delete") ? handleDeleteItem : undefined}
                searchPlaceholder="Search inventory..."
                searchKey="name"
                exportData={hasPermission("inventory", "export") ? inventory : undefined}
                exportType="Inventory"
              />
            </div>
          )}

          {activeTab === "customers" && hasPermission("customers", "read") && (
            <div className="space-y-6">

              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Customer Management</h2>
                  <p className="text-muted-foreground">Manage your customer database and relationships</p>
                </div>
                {hasPermission("customers", "create") && (
                  <div>
                    <Button onClick={() => setIsAddCustomerOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Customer
                    </Button>

                    <Dialog
                      open={isAddCustomerOpen || !!editingCustomer}
                      onOpenChange={(open) => {
                        if (!open) {
                          setIsAddCustomerOpen(false)
                          setEditingCustomer(undefined)
                        }
                      }}
                    >
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>{editingCustomer ? "Edit Customer" : "Add New Customer"}</DialogTitle>
                        <DialogDescription>
                          {editingCustomer ? "Update customer details" : "Enter customer details"}
                        </DialogDescription>
                      </DialogHeader>
                      <CustomerForm
                        customer={editingCustomer}
                        onSubmit={() => {
                          setIsAddCustomerOpen(false)
                          setEditingCustomer(undefined)
                        }}
                        onCancel={() => {
                          setIsAddCustomerOpen(false)
                          setEditingCustomer(undefined)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                  </div>
                )}
              </div>

              <DataTable
                data={customers}
                columns={customerColumns}
                onEdit={hasPermission("customers", "update") ? (customer) => setEditingCustomer(customer) : undefined}
                onDelete={hasPermission("customers", "delete") ? handleDeleteCustomer : undefined}
                searchPlaceholder="Search customers..."
                searchKey="name"
                exportData={hasPermission("customers", "export") ? customers : undefined}
                exportType="Customers"
              />
            </div>
          )}

          {activeTab === "sales" && hasPermission("sales", "read") && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Sales Management</h2>
                  <p className="text-muted-foreground">Create new sales and manage transactions</p>
                </div>
                {hasPermission("sales", "create") && (
                  <div>
                    <Button onClick={() => setIsSaleDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      New Sale
                    </Button>

                    <Dialog open={isSaleDialogOpen} onOpenChange={setIsSaleDialogOpen}>
                    <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Create New Sale</DialogTitle>
                        <DialogDescription>
                          Generate a new sales invoice with detailed weight calculations
                        </DialogDescription>
                      </DialogHeader>
                      <SaleForm
                        onSubmit={() => setIsSaleDialogOpen(false)}
                        onCancel={() => setIsSaleDialogOpen(false)}
                      />
                    </DialogContent>
                  </Dialog>
                  </div>
                )}
              </div>

              <Dialog
                open={!!editingSale}
                onOpenChange={(open) => {
                  if (!open) {
                    setEditingSale(undefined)
                  }
                }}
              >
                <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Edit Sale</DialogTitle>
                    <DialogDescription>Update sale details and items</DialogDescription>
                  </DialogHeader>
                  <SaleForm
                    sale={editingSale}
                    onSubmit={() => setEditingSale(undefined)}
                    onCancel={() => setEditingSale(undefined)}
                  />
                </DialogContent>
              </Dialog>

              {sales.length > 0 ? (
                <DataTable
                  data={sales}
                  columns={salesColumns}
                  onEdit={hasPermission("sales", "update") ? (sale) => setEditingSale(sale) : undefined}
                  onDelete={hasPermission("sales", "delete") ? handleDeleteSale : undefined}
                  searchPlaceholder="Search sales..."
                  searchKey="id"
                  exportData={hasPermission("sales", "export") ? sales : undefined}
                  exportType="Sales"
                />
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No sales yet</h3>
                    <p className="text-muted-foreground mb-4">Create your first sale to get started</p>
                    {hasPermission("sales", "create") && (
                      <Button onClick={() => setIsSaleDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Sale
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeTab === "purchases" && hasPermission("purchases", "read") && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Purchase Management</h2>
                  <p className="text-muted-foreground">Manage inventory purchases and supplier transactions</p>
                </div>
                {hasPermission("purchases", "create") && (
                  <Dialog
                    open={isPurchaseDialogOpen || !!editingPurchase}
                    onOpenChange={(open) => {
                      if (!open) {
                        setIsPurchaseDialogOpen(false)
                        setEditingPurchase(undefined)
                      }
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button onClick={() => setIsPurchaseDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Purchase
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>{editingPurchase ? "Edit Purchase" : "Add New Purchase"}</DialogTitle>
                        <DialogDescription>
                          {editingPurchase ? "Update purchase details" : "Record a new purchase transaction"}
                        </DialogDescription>
                      </DialogHeader>
                      <PurchaseForm
                        purchase={editingPurchase}
                        onSubmit={() => {
                          setIsPurchaseDialogOpen(false)
                          setEditingPurchase(undefined)
                        }}
                        onCancel={() => {
                          setIsPurchaseDialogOpen(false)
                          setEditingPurchase(undefined)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">This Month Purchases</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ₹{purchases.reduce((sum, p) => sum + p.amount, 0).toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">{purchases.length} transactions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ₹
                      {purchases
                        .filter((p) => p.status === "pending")
                        .reduce((sum, p) => sum + p.amount, 0)
                        .toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {purchases.filter((p) => p.status === "pending").length} pending
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{new Set(purchases.map((p) => p.supplier)).size}</div>
                    <p className="text-xs text-muted-foreground">Unique suppliers</p>
                  </CardContent>
                </Card>
              </div>

              <DataTable
                data={purchases}
                columns={purchaseColumns}
                onEdit={hasPermission("purchases", "update") ? (purchase) => setEditingPurchase(purchase) : undefined}
                onDelete={hasPermission("purchases", "delete") ? handleDeletePurchase : undefined}
                searchPlaceholder="Search purchases..."
                searchKey="supplier"
                exportData={hasPermission("purchases", "export") ? purchases : undefined}
                exportType="Purchases"
              />
            </div>
          )}

          {activeTab === "reports" && hasPermission("reports", "read") && (
            <div className="space-y-6">
              {activeReport === "weight" ? (
                <div>
                  <Button variant="outline" onClick={() => setActiveReport(null)} className="mb-4">
                    ← Back to Reports
                  </Button>
                  <WeightReport />
                </div>
              ) : activeReport === "sales" ? (
                <div>
                  <Button variant="outline" onClick={() => setActiveReport(null)} className="mb-4">
                    ← Back to Reports
                  </Button>
                  <SalesReport />
                </div>
              ) : activeReport === "inventory" ? (
                <div>
                  <Button variant="outline" onClick={() => setActiveReport(null)} className="mb-4">
                    ← Back to Reports
                  </Button>
                  <InventoryReport />
                </div>
              ) : activeReport === "customer" ? (
                <div>
                  <Button variant="outline" onClick={() => setActiveReport(null)} className="mb-4">
                    ← Back to Reports
                  </Button>
                  <CustomerReport />
                </div>
              ) : (
                <>
                  <div>
                    <h2 className="text-3xl font-bold tracking-tight">Reports & Analytics</h2>
                    <p className="text-muted-foreground">Generate detailed reports and analyze business performance</p>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveReport("weight")}
                    >
                      <CardHeader>
                        <CardTitle className="text-sm">Weight Analysis</CardTitle>
                        <Scale className="h-8 w-8 text-blue-600" />
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground">Gross, net, and stone weight analysis</p>
                      </CardContent>
                    </Card>

                    <Card
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveReport("sales")}
                    >
                      <CardHeader>
                        <CardTitle className="text-sm">Sales Report</CardTitle>
                        <FileText className="h-8 w-8 text-green-600" />
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground">Daily, monthly, yearly sales analysis</p>
                      </CardContent>
                    </Card>

                    <Card
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveReport("inventory")}
                    >
                      <CardHeader>
                        <CardTitle className="text-sm">Inventory Report</CardTitle>
                        <Package className="h-8 w-8 text-purple-600" />
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground">Stock levels and inventory valuation</p>
                      </CardContent>
                    </Card>

                    <Card
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveReport("customer")}
                    >
                      <CardHeader>
                        <CardTitle className="text-sm">Customer Report</CardTitle>
                        <Users className="h-8 w-8 text-orange-600" />
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground">Customer purchase history and analysis</p>
                      </CardContent>
                    </Card>
                  </div>
                </>
              )}
            </div>
          )}

          {activeTab === "schemes" && hasPermission("schemes", "read") && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Scheme Management</h2>
                  <p className="text-muted-foreground">Manage jewelry schemes and installment plans</p>
                </div>
                {hasPermission("schemes", "create") && (
                  <Dialog
                    open={isSchemeDialogOpen || !!editingScheme}
                    onOpenChange={(open) => {
                      if (!open) {
                        setIsSchemeDialogOpen(false)
                        setEditingScheme(undefined)
                      }
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button onClick={() => setIsSchemeDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Scheme
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle>{editingScheme ? "Edit Scheme" : "Create New Scheme"}</DialogTitle>
                        <DialogDescription>
                          {editingScheme ? "Update scheme details" : "Set up a new jewelry savings scheme for customer"}
                        </DialogDescription>
                      </DialogHeader>
                      <SchemeForm
                        scheme={editingScheme}
                        onSubmit={() => {
                          setIsSchemeDialogOpen(false)
                          setEditingScheme(undefined)
                        }}
                        onCancel={() => {
                          setIsSchemeDialogOpen(false)
                          setEditingScheme(undefined)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Active Schemes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{schemes.filter((s) => s.status === "active").length}</div>
                    <p className="text-xs text-muted-foreground">Running schemes</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ₹{schemes.reduce((sum, s) => sum + s.totalAmount, 0).toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">All schemes combined</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Collected Amount</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ₹{schemes.reduce((sum, s) => sum + s.paidAmount, 0).toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">Total collected</p>
                  </CardContent>
                </Card>
              </div>

              <DataTable
                data={schemes}
                columns={schemeColumns}
                onEdit={hasPermission("schemes", "update") ? (scheme) => setEditingScheme(scheme) : undefined}
                onDelete={hasPermission("schemes", "delete") ? handleDeleteScheme : undefined}
                searchPlaceholder="Search schemes..."
                searchKey="name"
                exportData={hasPermission("schemes", "export") ? schemes : undefined}
                exportType="Schemes"
              />
            </div>
          )}

          {activeTab === "repairs" && hasPermission("repairs", "read") && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Repair & Custom Orders</h2>
                  <p className="text-muted-foreground">Track jewelry repairs and custom manufacturing orders</p>
                </div>
                {hasPermission("repairs", "create") && (
                  <Dialog
                    open={isRepairDialogOpen || !!editingRepair}
                    onOpenChange={(open) => {
                      if (!open) {
                        setIsRepairDialogOpen(false)
                        setEditingRepair(undefined)
                      }
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button onClick={() => setIsRepairDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Repair Order
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle>{editingRepair ? "Edit Repair Order" : "Create Repair Order"}</DialogTitle>
                        <DialogDescription>
                          {editingRepair ? "Update repair order details" : "Register a new repair or custom order"}
                        </DialogDescription>
                      </DialogHeader>
                      <RepairForm
                        repair={editingRepair}
                        onSubmit={() => {
                          setIsRepairDialogOpen(false)
                          setEditingRepair(undefined)
                        }}
                        onCancel={() => {
                          setIsRepairDialogOpen(false)
                          setEditingRepair(undefined)
                        }}
                      />
                    </DialogContent>
                  </Dialog>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
                    <Clock className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{repairs.filter((r) => r.status === "pending").length}</div>
                    <p className="text-xs text-muted-foreground">Awaiting work</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{repairs.filter((r) => r.status === "in-progress").length}</div>
                    <p className="text-xs text-muted-foreground">Being worked on</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Ready for Delivery</CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{repairs.filter((r) => r.status === "completed").length}</div>
                    <p className="text-xs text-muted-foreground">Completed work</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                    <IndianRupee className="h-4 w-4 text-purple-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ₹{repairs.reduce((sum, r) => sum + r.charges, 0).toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">From repairs</p>
                  </CardContent>
                </Card>
              </div>

              <DataTable
                data={repairs}
                columns={repairColumns}
                onEdit={hasPermission("repairs", "update") ? (repair) => setEditingRepair(repair) : undefined}
                onDelete={hasPermission("repairs", "delete") ? handleDeleteRepair : undefined}
                searchPlaceholder="Search repairs..."
                searchKey="item"
                exportData={hasPermission("repairs", "export") ? repairs : undefined}
                exportType="Repairs"
              />
            </div>
          )}

          {activeTab === "categories" && (
            <CategoriesPage />
          )}

          {activeTab === "barcode" && (
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Barcode Management</h2>
                <p className="text-muted-foreground">Generate and manage barcodes for inventory items</p>
              </div>
              <BarcodeScanner />
            </div>
          )}

          {activeTab === "rate-calculator" && (
            <div className="space-y-6">
              <RateCalculator />
            </div>
          )}
        </main>
      </div>

      {/* Quick Actions Dialogs */}
      {/* Add Customer Dialog */}
      <Dialog open={isAddCustomerOpen} onOpenChange={setIsAddCustomerOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>Enter customer details</DialogDescription>
          </DialogHeader>
          <CustomerForm
            onSubmit={() => setIsAddCustomerOpen(false)}
            onCancel={() => setIsAddCustomerOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Add Item Dialog */}
      <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Jewelry Item</DialogTitle>
            <DialogDescription>Enter the details of the new jewelry item</DialogDescription>
          </DialogHeader>
          <InventoryForm
            onSubmit={() => setIsAddItemOpen(false)}
            onCancel={() => setIsAddItemOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* New Sale Dialog */}
      <Dialog open={isSaleDialogOpen} onOpenChange={setIsSaleDialogOpen}>
        <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Sale</DialogTitle>
            <DialogDescription>Generate a new sales invoice with detailed weight calculations</DialogDescription>
          </DialogHeader>
          <SaleForm
            onSubmit={() => setIsSaleDialogOpen(false)}
            onCancel={() => setIsSaleDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* New Scheme Dialog */}
      <Dialog open={isSchemeDialogOpen} onOpenChange={setIsSchemeDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Scheme</DialogTitle>
            <DialogDescription>Set up a new jewelry scheme or installment plan</DialogDescription>
          </DialogHeader>
          <SchemeForm
            onSubmit={() => setIsSchemeDialogOpen(false)}
            onCancel={() => setIsSchemeDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* New Repair Order Dialog */}
      <Dialog open={isRepairDialogOpen} onOpenChange={setIsRepairDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Repair Order</DialogTitle>
            <DialogDescription>Create a new repair or custom order</DialogDescription>
          </DialogHeader>
          <RepairForm
            onSubmit={() => setIsRepairDialogOpen(false)}
            onCancel={() => setIsRepairDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      {hasPermission("settings", "read") && (
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Application Settings</DialogTitle>
              <DialogDescription>Configure your jewelry store management system</DialogDescription>
            </DialogHeader>
            <SettingsPanel />
          </DialogContent>
        </Dialog>
      )}

      {/* Confirm Dialog */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}
        title={confirmDialog.title}
        description={confirmDialog.description}
        onConfirm={confirmDialog.onConfirm}
      />
    </div>
  )
}
