import { getPool } from './config'
import fs from 'fs'
import path from 'path'

export interface Migration {
  id: string
  name: string
  sql: string
  executed_at?: Date
}

export class MigrationManager {
  private pool = getPool()

  async createMigrationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    await this.pool.execute(sql)
  }

  async getExecutedMigrations(): Promise<string[]> {
    await this.createMigrationsTable()
    const [rows] = await this.pool.execute('SELECT id FROM migrations ORDER BY executed_at')
    return (rows as any[]).map(row => row.id)
  }

  async executeMigration(migration: Migration): Promise<void> {
    const connection = await this.pool.getConnection()
    try {
      await connection.beginTransaction()
      
      // Execute the migration SQL
      const statements = migration.sql.split(';').filter(stmt => stmt.trim())
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.execute(statement)
        }
      }
      
      // Record the migration as executed
      await connection.execute(
        'INSERT INTO migrations (id, name) VALUES (?, ?)',
        [migration.id, migration.name]
      )
      
      await connection.commit()
      console.log(`Migration ${migration.id} executed successfully`)
    } catch (error) {
      await connection.rollback()
      console.error(`Migration ${migration.id} failed:`, error)
      throw error
    } finally {
      connection.release()
    }
  }

  async runMigrations(): Promise<void> {
    try {
      const executedMigrations = await this.getExecutedMigrations()
      
      // Read schema.sql as initial migration
      const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql')
      const schemaSql = fs.readFileSync(schemaPath, 'utf8')
      
      const initialMigration: Migration = {
        id: '001_initial_schema',
        name: 'Initial database schema',
        sql: schemaSql
      }

      if (!executedMigrations.includes(initialMigration.id)) {
        await this.executeMigration(initialMigration)
      }

      console.log('All migrations completed successfully')
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }

  async resetDatabase(): Promise<void> {
    const connection = await this.pool.getConnection()
    try {
      // Drop all tables
      const tables = [
        'sale_items', 'sales', 'schemes', 'repairs', 'purchases',
        'inventory', 'customers', 'categories', 'users', 'settings', 'migrations'
      ]
      
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
      for (const table of tables) {
        await connection.execute(`DROP TABLE IF EXISTS ${table}`)
      }
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
      
      console.log('Database reset completed')
    } catch (error) {
      console.error('Database reset failed:', error)
      throw error
    } finally {
      connection.release()
    }
  }
}
