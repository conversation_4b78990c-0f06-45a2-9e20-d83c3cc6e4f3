import { BaseService } from '../base-service'
import { RepairOrder } from '../../types'
import { RowDataPacket } from 'mysql2'

export class RepairService extends BaseService<RepairOrder> {
  protected tableName = 'repairs'

  async findById(id: string): Promise<RepairOrder | null> {
    const sql = `
      SELECT r.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} r
      LEFT JOIN customers c ON r.customer_id = c.id
      WHERE r.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    return this.transformRepairRow(rows[0])
  }

  async findAll(conditions: Record<string, any> = {}): Promise<RepairOrder[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `
      SELECT r.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} r
      LEFT JOIN customers c ON r.customer_id = c.id
      ${whereClause}
      ORDER BY r.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    
    return rows.map(row => this.transformRepairRow(row))
  }

  private transformRepairRow(row: any): RepairOrder {
    return {
      id: row.id,
      customer: {
        id: row.customer_id,
        name: row.customer_name,
        phone: row.customer_phone,
        email: row.customer_email,
        address: row.customer_address,
        gstNumber: row.customer_gst_number,
        totalPurchases: row.customer_total_purchases,
        lastVisit: row.customer_last_visit,
        createdAt: row.customer_created_at,
        updatedAt: row.customer_updated_at
      },
      item: row.item,
      description: row.description,
      orderType: row.order_type,
      receivedDate: row.received_date,
      promisedDate: row.promised_date,
      status: row.status,
      charges: row.charges,
      specialInstructions: row.special_instructions,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }

  async create(data: Omit<RepairOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<RepairOrder> {
    const id = this.generateId()
    const now = new Date().toISOString()
    
    const repairData = {
      id,
      customer_id: data.customer.id,
      item: data.item,
      description: data.description,
      order_type: data.orderType,
      received_date: data.receivedDate,
      promised_date: data.promisedDate,
      status: data.status,
      charges: data.charges,
      special_instructions: data.specialInstructions,
      created_at: now,
      updated_at: now
    }

    const keys = Object.keys(repairData)
    const values = Object.values(repairData)
    const placeholders = keys.map(() => '?').join(', ')
    
    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)
    
    return this.findById(id) as Promise<RepairOrder>
  }

  async getRepairsByStatus(status: string): Promise<RepairOrder[]> {
    return this.findAll({ status })
  }

  async getRepairsByCustomer(customerId: string): Promise<RepairOrder[]> {
    return this.findAll({ customer_id: customerId })
  }

  async getOverdueRepairs(): Promise<RepairOrder[]> {
    const today = new Date().toISOString().split('T')[0]
    const sql = `
      SELECT r.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} r
      LEFT JOIN customers c ON r.customer_id = c.id
      WHERE r.promised_date < ? AND r.status NOT IN ('completed', 'delivered')
      ORDER BY r.promised_date ASC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [today])
    
    return rows.map(row => this.transformRepairRow(row))
  }

  async getDueTodayRepairs(): Promise<RepairOrder[]> {
    const today = new Date().toISOString().split('T')[0]
    const sql = `
      SELECT r.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} r
      LEFT JOIN customers c ON r.customer_id = c.id
      WHERE r.promised_date = ? AND r.status NOT IN ('completed', 'delivered')
      ORDER BY r.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [today])
    
    return rows.map(row => this.transformRepairRow(row))
  }

  async updateStatus(id: string, status: RepairOrder['status']): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET status = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [status, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  async getRepairStats(): Promise<{
    totalRepairs: number
    pendingRepairs: number
    inProgressRepairs: number
    completedRepairs: number
    overdueRepairs: number
    totalRevenue: number
  }> {
    const totalRepairs = await this.count()
    const pendingRepairs = await this.count({ status: 'pending' })
    const inProgressRepairs = await this.count({ status: 'in-progress' })
    const completedRepairs = await this.count({ status: 'completed' })
    
    const overdueRepairs = (await this.getOverdueRepairs()).length

    const revenueResult = await this.executeQuery(
      `SELECT SUM(charges) as total_revenue FROM ${this.tableName} WHERE status IN ('completed', 'delivered')`
    )

    return {
      totalRepairs,
      pendingRepairs,
      inProgressRepairs,
      completedRepairs,
      overdueRepairs,
      totalRevenue: revenueResult[0]?.total_revenue || 0
    }
  }
}
