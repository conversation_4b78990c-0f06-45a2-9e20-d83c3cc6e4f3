import { NextRequest, NextResponse } from 'next/server'
import { exchangeService } from '@/lib/database/services'
import { ValidationMiddleware } from '@/lib/middleware/validation-middleware'

export async function GET() {
  try {
    const transactions = await exchangeService.findAll()
    return NextResponse.json(transactions)
  } catch (error) {
    console.error('Error fetching exchange transactions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exchange transactions' },
      { status: 500 }
    )
  }
}

export const POST = ValidationMiddleware.withValidation(
  ValidationMiddleware.validateExchangeTransactionRequest,
  async (data, request) => {
    try {
      const transaction = await exchangeService.create(data)
      return ValidationMiddleware.createSuccessResponse(transaction)
    } catch (error) {
      console.error('Error creating exchange transaction:', error)
      return ValidationMiddleware.createErrorResponse(
        'Failed to create exchange transaction',
        [error instanceof Error ? error.message : 'Unknown error'],
        [],
        500
      )
    }
  }
)

export async function PUT(request: NextRequest) {
  try {
    const validation = await ValidationMiddleware.validateExchangeTransactionRequest(request)

    if (!validation.isValid) {
      return validation.response!
    }

    const { id, ...updateData } = validation.data

    if (!id) {
      return ValidationMiddleware.createErrorResponse('Transaction ID is required for update')
    }

    const idValidation = ValidationMiddleware.validateIdParameter(id, 'transaction ID')
    if (!idValidation.isValid) {
      return idValidation.response!
    }

    const transaction = await exchangeService.update(id, updateData)
    return ValidationMiddleware.createSuccessResponse(transaction)
  } catch (error) {
    console.error('Error updating exchange transaction:', error)
    return ValidationMiddleware.createErrorResponse(
      'Failed to update exchange transaction',
      [error instanceof Error ? error.message : 'Unknown error'],
      [],
      500
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: 'Transaction ID is required' },
        { status: 400 }
      )
    }

    await exchangeService.delete(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting exchange transaction:', error)
    return NextResponse.json(
      { error: 'Failed to delete exchange transaction' },
      { status: 500 }
    )
  }
}
