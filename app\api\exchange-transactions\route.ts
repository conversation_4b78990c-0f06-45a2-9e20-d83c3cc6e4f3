import { NextRequest, NextResponse } from 'next/server'
import { exchangeService } from '@/lib/database/services'

export async function GET() {
  try {
    const transactions = await exchangeService.findAll()
    return NextResponse.json(transactions)
  } catch (error) {
    console.error('Error fetching exchange transactions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exchange transactions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const transaction = await exchangeService.create(data)
    return NextResponse.json(transaction, { status: 201 })
  } catch (error) {
    console.error('Error creating exchange transaction:', error)
    return NextResponse.json(
      { error: 'Failed to create exchange transaction' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data
    const transaction = await exchangeService.update(id, updateData)
    return NextResponse.json(transaction)
  } catch (error) {
    console.error('Error updating exchange transaction:', error)
    return NextResponse.json(
      { error: 'Failed to update exchange transaction' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: 'Transaction ID is required' },
        { status: 400 }
      )
    }

    await exchangeService.delete(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting exchange transaction:', error)
    return NextResponse.json(
      { error: 'Failed to delete exchange transaction' },
      { status: 500 }
    )
  }
}
