"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Printer, Download, X } from "lucide-react"
import { Sale } from "@/lib/types"
import { formatCurrency, formatDate } from "@/lib/utils"

interface EnhancedInvoiceTemplateProps {
  sale: Sale
  onClose: () => void
}

export function EnhancedInvoiceTemplate({ sale, onClose }: EnhancedInvoiceTemplateProps) {
  // Mock settings - replace with actual settings
  const settings = {
    businessName: "Shree Jewellers",
    address: "123 Main Street, Mumbai, Maharashtra 400001",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    gstNumber: "27ABCDE1234F1Z5",
    cgstRate: "3.0",
    sgstRate: "3.0"
  }

  const handlePrint = () => {
    window.print()
  }

  const handleDownloadPDF = () => {
    // Mock PDF generation - replace with actual implementation
    alert('PDF download functionality will be implemented with actual backend')
  }

  const hasExchangeItems = sale.exchangeItems && sale.exchangeItems.length > 0
  const exchangeDeduction = sale.exchangeDeduction || 0
  const finalTotal = sale.finalTotal || sale.total

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[95vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between print:hidden">
          <CardTitle>Sales Invoice</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline" onClick={handleDownloadPDF}>
              <Download className="h-4 w-4 mr-2" />
              PDF
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6" id="invoice-content">
          {/* Invoice Header */}
          <div className="text-center border-b pb-6">
            <h1 className="text-3xl font-bold">{settings.businessName}</h1>
            <p className="text-sm text-muted-foreground mt-2">{settings.address}</p>
            <p className="text-sm text-muted-foreground">
              Phone: {settings.phone} | Email: {settings.email}
            </p>
            <p className="text-sm text-muted-foreground">GST No: {settings.gstNumber}</p>
            <div className="mt-4">
              <Badge variant="outline" className="text-lg px-4 py-2">
                SALES INVOICE
              </Badge>
            </div>
          </div>

          {/* Invoice Details */}
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Invoice Details</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Invoice No:</span> {sale.id}</p>
                <p><span className="font-medium">Date:</span> {formatDate(sale.date)}</p>
                <p><span className="font-medium">Status:</span> 
                  <Badge variant={sale.status === 'paid' ? 'default' : 'secondary'} className="ml-2">
                    {sale.status}
                  </Badge>
                </p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Customer Details</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Name:</span> {sale.customer.name}</p>
                <p><span className="font-medium">Phone:</span> {sale.customer.phone}</p>
                {sale.customer.email && (
                  <p><span className="font-medium">Email:</span> {sale.customer.email}</p>
                )}
                {sale.customer.address && (
                  <p><span className="font-medium">Address:</span> {sale.customer.address}</p>
                )}
                {sale.customer.gstNumber && (
                  <p><span className="font-medium">GST No:</span> {sale.customer.gstNumber}</p>
                )}
              </div>
            </div>
          </div>

          {/* Sale Items Table */}
          <div>
            <h3 className="font-semibold mb-4">Items Sold</h3>
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-muted">
                  <tr className="text-left">
                    <th className="p-3 text-sm font-medium">Item</th>
                    <th className="p-3 text-sm font-medium">Category</th>
                    <th className="p-3 text-sm font-medium">Net Wt (g)</th>
                    <th className="p-3 text-sm font-medium">Rate/g</th>
                    <th className="p-3 text-sm font-medium">Making</th>
                    <th className="p-3 text-sm font-medium text-right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {sale.items.map((item, index) => (
                    <tr key={index} className="border-t">
                      <td className="p-3 text-sm">{item.item.name}</td>
                      <td className="p-3 text-sm">{item.item.category}</td>
                      <td className="p-3 text-sm">{item.netWeight.toFixed(3)}</td>
                      <td className="p-3 text-sm">₹{item.rate.toFixed(2)}</td>
                      <td className="p-3 text-sm">₹{item.makingCharges.toFixed(2)}</td>
                      <td className="p-3 text-sm text-right">{formatCurrency(item.amount)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Exchange Items Section */}
          {hasExchangeItems && (
            <div>
              <h3 className="font-semibold mb-4 text-orange-600">Exchange Items (Old Gold/Silver)</h3>
              <div className="border rounded-lg overflow-hidden border-orange-200">
                <table className="w-full">
                  <thead className="bg-orange-50">
                    <tr className="text-left">
                      <th className="p-3 text-sm font-medium">Description</th>
                      <th className="p-3 text-sm font-medium">Metal/Purity</th>
                      <th className="p-3 text-sm font-medium">Net Wt (g)</th>
                      <th className="p-3 text-sm font-medium">Rate/g</th>
                      <th className="p-3 text-sm font-medium text-right">Deduction</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sale.exchangeItems?.map((exchangeItem, index) => (
                      <tr key={index} className="border-t">
                        <td className="p-3 text-sm">{exchangeItem.exchangeItem?.itemDescription}</td>
                        <td className="p-3 text-sm">
                          {exchangeItem.exchangeItem?.metalType} {exchangeItem.exchangeItem?.purity}
                        </td>
                        <td className="p-3 text-sm">{exchangeItem.exchangeItem?.netWeight.toFixed(3)}</td>
                        <td className="p-3 text-sm">₹{exchangeItem.appliedRate.toFixed(2)}</td>
                        <td className="p-3 text-sm text-right text-orange-600">
                          -{formatCurrency(exchangeItem.deductionAmount)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Totals Section */}
          <div className="flex justify-end">
            <div className="w-80 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(sale.subtotal)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>CGST ({settings.cgstRate}%):</span>
                <span>{formatCurrency(sale.cgst)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>SGST ({settings.sgstRate}%):</span>
                <span>{formatCurrency(sale.sgst)}</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>Total:</span>
                <span>{formatCurrency(sale.total)}</span>
              </div>
              
              {exchangeDeduction > 0 && (
                <>
                  <div className="flex justify-between text-orange-600 font-medium">
                    <span>Exchange Deduction:</span>
                    <span>-{formatCurrency(exchangeDeduction)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between text-lg font-bold text-primary">
                    <span>Final Amount Payable:</span>
                    <span>{formatCurrency(finalTotal)}</span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Payment and Terms */}
          <div className="border-t pt-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Payment Information</h4>
                <div className="text-sm space-y-1">
                  <p><span className="font-medium">Status:</span> {sale.status}</p>
                  {hasExchangeItems && (
                    <p className="text-orange-600">
                      <span className="font-medium">Exchange Applied:</span> {formatCurrency(exchangeDeduction)}
                    </p>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Terms & Conditions</h4>
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>• All sales are final unless otherwise specified</p>
                  <p>• Exchange items are valued as per current market rates</p>
                  <p>• GST is applicable as per government regulations</p>
                  <p>• This is a computer generated invoice</p>
                </div>
              </div>
            </div>
          </div>

          {/* Exchange Summary Box */}
          {hasExchangeItems && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h4 className="font-semibold text-orange-800 mb-2">Exchange Summary</h4>
              <div className="text-sm text-orange-700 space-y-1">
                <p>• {sale.exchangeItems?.length} exchange item(s) applied to this sale</p>
                <p>• Total exchange value: {formatCurrency(exchangeDeduction)}</p>
                <p>• Net amount after exchange: {formatCurrency(finalTotal)}</p>
                <p className="text-xs mt-2 text-orange-600">
                  Exchange items have been valued as per current market rates and applied as deduction to this invoice.
                </p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="text-center text-xs text-muted-foreground border-t pt-4">
            <p>Thank you for your business!</p>
            <p>For any queries, please contact us at {settings.phone} or {settings.email}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
