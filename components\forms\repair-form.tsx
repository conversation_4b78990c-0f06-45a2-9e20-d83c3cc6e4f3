"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useStore } from "@/lib/store"
import type { RepairOrder } from "@/lib/types"

interface RepairFormProps {
  repair?: RepairOrder
  onSubmit: () => void
  onCancel: () => void
}

export function RepairForm({ repair, onSubmit, onCancel }: RepairFormProps) {
  const { addRepair, updateRepair, customers } = useStore()
  const [formData, setFormData] = useState({
    customerId: "",
    item: "",
    description: "",
    orderType: "",
    receivedDate: "",
    promisedDate: "",
    status: "pending",
    charges: "",
    specialInstructions: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (repair) {
      setFormData({
        customerId: repair.customer.id,
        item: repair.item,
        description: repair.description,
        orderType: repair.orderType,
        receivedDate: repair.receivedDate,
        promisedDate: repair.promisedDate,
        status: repair.status,
        charges: repair.charges.toString(),
        specialInstructions: repair.specialInstructions || "",
      })
    } else {
      // Set default dates
      const today = new Date().toISOString().split("T")[0]
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]
      setFormData((prev) => ({
        ...prev,
        receivedDate: today,
        promisedDate: nextWeek,
      }))
    }
  }, [repair])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.customerId) {
      newErrors.customerId = "Customer is required"
    }

    if (!formData.item.trim()) {
      newErrors.item = "Item description is required"
    }

    if (!formData.description.trim()) {
      newErrors.description = "Repair description is required"
    }

    if (!formData.orderType) {
      newErrors.orderType = "Order type is required"
    }

    if (!formData.receivedDate) {
      newErrors.receivedDate = "Received date is required"
    }

    if (!formData.promisedDate) {
      newErrors.promisedDate = "Promised date is required"
    }

    if (formData.receivedDate && formData.promisedDate && formData.promisedDate < formData.receivedDate) {
      newErrors.promisedDate = "Promised date must be after received date"
    }

    if (!formData.charges || Number.parseFloat(formData.charges) < 0) {
      newErrors.charges = "Charges must be 0 or greater"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const customer = customers.find((c) => c.id === formData.customerId)
      if (!customer) {
        setErrors({ submit: "Selected customer not found" })
        return
      }

      const repairData = {
        customer,
        item: formData.item.trim(),
        description: formData.description.trim(),
        orderType: formData.orderType as "repair" | "custom" | "resize" | "polish",
        receivedDate: formData.receivedDate,
        promisedDate: formData.promisedDate,
        status: formData.status as "pending" | "in-progress" | "completed" | "delivered",
        charges: Number.parseFloat(formData.charges),
        specialInstructions: formData.specialInstructions.trim() || undefined,
      }

      if (repair) {
        await updateRepair(repair.id, repairData)
      } else {
        await addRepair(repairData)
      }

      // Reset form
      setFormData({
        customerId: "",
        item: "",
        description: "",
        orderType: "",
        receivedDate: "",
        promisedDate: "",
        status: "pending",
        charges: "",
        specialInstructions: "",
      })
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving repair order:", error)
      setErrors({ submit: "Failed to save repair order. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customerId">Customer *</Label>
          <Select value={formData.customerId} onValueChange={(value) => handleInputChange("customerId", value)}>
            <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
              <SelectValue placeholder="Select customer" />
            </SelectTrigger>
            <SelectContent>
              {customers.map((customer) => (
                <SelectItem key={customer.id} value={customer.id}>
                  {customer.name} - {customer.phone}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="orderType">Order Type *</Label>
          <Select value={formData.orderType} onValueChange={(value) => handleInputChange("orderType", value)}>
            <SelectTrigger className={errors.orderType ? "border-red-500" : ""}>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="repair">Repair</SelectItem>
              <SelectItem value="custom">Custom Order</SelectItem>
              <SelectItem value="resize">Resize</SelectItem>
              <SelectItem value="polish">Polish</SelectItem>
            </SelectContent>
          </Select>
          {errors.orderType && <p className="text-sm text-red-500">{errors.orderType}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="item">Item *</Label>
        <Input
          id="item"
          value={formData.item}
          onChange={(e) => handleInputChange("item", e.target.value)}
          placeholder="Gold ring, Silver necklace, etc."
          required
          className={errors.item ? "border-red-500" : ""}
        />
        {errors.item && <p className="text-sm text-red-500">{errors.item}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Repair Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder="Describe the repair work needed"
          required
          className={errors.description ? "border-red-500" : ""}
          rows={3}
        />
        {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="receivedDate">Received Date *</Label>
          <Input
            id="receivedDate"
            type="date"
            value={formData.receivedDate}
            onChange={(e) => handleInputChange("receivedDate", e.target.value)}
            required
            className={errors.receivedDate ? "border-red-500" : ""}
          />
          {errors.receivedDate && <p className="text-sm text-red-500">{errors.receivedDate}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="promisedDate">Promised Date *</Label>
          <Input
            id="promisedDate"
            type="date"
            value={formData.promisedDate}
            onChange={(e) => handleInputChange("promisedDate", e.target.value)}
            required
            className={errors.promisedDate ? "border-red-500" : ""}
          />
          {errors.promisedDate && <p className="text-sm text-red-500">{errors.promisedDate}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="charges">Charges (₹) *</Label>
          <Input
            id="charges"
            type="number"
            step="0.01"
            min="0"
            value={formData.charges}
            onChange={(e) => handleInputChange("charges", e.target.value)}
            placeholder="1500"
            required
            className={errors.charges ? "border-red-500" : ""}
          />
          {errors.charges && <p className="text-sm text-red-500">{errors.charges}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="delivered">Delivered</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="specialInstructions">Special Instructions</Label>
        <Textarea
          id="specialInstructions"
          value={formData.specialInstructions}
          onChange={(e) => handleInputChange("specialInstructions", e.target.value)}
          placeholder="Any special instructions or notes"
          rows={2}
        />
      </div>

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : repair ? "Update Order" : "Create Order"}
        </Button>
      </div>
    </form>
  )
}
