-- Quick Setup Script for Exchange System
-- Run this in MySQL command line or phpMyAdmin

-- Create database
CREATE DATABASE IF NOT EXISTS jewellers_db;
USE jewellers_db;

-- Create customers table (if not exists)
CREATE TABLE IF NOT EXISTS customers (
    id VARCHAR(36) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT,
    gst_number VARCHAR(50),
    total_purchases DECIMAL(12, 2) DEFAULT 0.00,
    last_visit DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create sales table (if not exists)
CREATE TABLE IF NOT EXISTS sales (
    id VARCHAR(36) PRIMARY KEY,
    customer_id VARCHAR(36),
    subtotal DECIMAL(12, 2) NOT NULL,
    cgst DECIMAL(12, 2) NOT NULL,
    sgst DECIMAL(12, 2) NOT NULL,
    total DECIMAL(12, 2) NOT NULL,
    status ENUM('draft', 'paid', 'pending', 'cancelled') DEFAULT 'draft',
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create exchange_rates table
CREATE TABLE IF NOT EXISTS exchange_rates (
    id VARCHAR(36) PRIMARY KEY,
    metal_type ENUM('gold', 'silver') NOT NULL,
    purity VARCHAR(10) NOT NULL,
    rate_per_gram DECIMAL(10, 2) NOT NULL,
    effective_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create exchange_transactions table
CREATE TABLE IF NOT EXISTS exchange_transactions (
    id VARCHAR(36) PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(36),
    transaction_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
    notes TEXT,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    purchase_bill_generated BOOLEAN DEFAULT FALSE,
    purchase_bill_id VARCHAR(36),
    purchase_bill_number VARCHAR(50),
    purchase_bill_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create exchange_items table
CREATE TABLE IF NOT EXISTS exchange_items (
    id VARCHAR(36) PRIMARY KEY,
    transaction_id VARCHAR(36) NOT NULL,
    item_description VARCHAR(255) NOT NULL,
    metal_type ENUM('gold', 'silver') NOT NULL,
    purity VARCHAR(10) NOT NULL,
    gross_weight DECIMAL(8, 3) NOT NULL,
    stone_weight DECIMAL(8, 3) DEFAULT 0.000,
    net_weight DECIMAL(8, 3) NOT NULL,
    rate_per_gram DECIMAL(10, 2) NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    item_condition ENUM('good', 'fair', 'poor') DEFAULT 'good',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create bill_sequences table
CREATE TABLE IF NOT EXISTS bill_sequences (
    id VARCHAR(36) PRIMARY KEY,
    sequence_type VARCHAR(50) NOT NULL,
    prefix VARCHAR(10) NOT NULL,
    current_number INT NOT NULL DEFAULT 0,
    financial_year VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create exchange_purchase_bills table
CREATE TABLE IF NOT EXISTS exchange_purchase_bills (
    id VARCHAR(36) PRIMARY KEY,
    bill_number VARCHAR(50) UNIQUE NOT NULL,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    customer_id VARCHAR(36),
    bill_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL,
    cgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    sgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    total_with_tax DECIMAL(12, 2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
    payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create sales_exchange_items table
CREATE TABLE IF NOT EXISTS sales_exchange_items (
    id VARCHAR(36) PRIMARY KEY,
    sale_id VARCHAR(36) NOT NULL,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    exchange_item_id VARCHAR(36) NOT NULL,
    deduction_amount DECIMAL(12, 2) NOT NULL,
    applied_rate DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create exchange_audit_trail table
CREATE TABLE IF NOT EXISTS exchange_audit_trail (
    id VARCHAR(36) PRIMARY KEY,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    action_type ENUM('created', 'updated', 'billed', 'voucher_generated', 'used_in_sale', 'cancelled') NOT NULL,
    action_description TEXT NOT NULL,
    old_values JSON,
    new_values JSON,
    related_bill_id VARCHAR(36),
    related_sale_id VARCHAR(36),
    performed_by VARCHAR(36),
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default exchange rates
INSERT IGNORE INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active) VALUES
('rate_001', 'gold', '24K', 6800.00, CURDATE(), TRUE),
('rate_002', 'gold', '22K', 6200.00, CURDATE(), TRUE),
('rate_003', 'gold', '18K', 5100.00, CURDATE(), TRUE),
('rate_004', 'gold', '14K', 3950.00, CURDATE(), TRUE),
('rate_005', 'silver', '999', 85.00, CURDATE(), TRUE),
('rate_006', 'silver', '925', 78.00, CURDATE(), TRUE),
('rate_007', 'silver', '900', 76.00, CURDATE(), TRUE);

-- Insert bill sequence
INSERT IGNORE INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) VALUES
('seq_001', 'exchange_purchase', 'EPB', 0, CONCAT(YEAR(CURDATE()), '-', RIGHT(YEAR(CURDATE()) + 1, 2)));

-- Insert sample customers
INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) VALUES
('cust_001', 'Rajesh Kumar', '9876543210', '<EMAIL>', '123 MG Road, Mumbai, Maharashtra 400001', 150000.00, '2024-01-31', NOW(), NOW()),
('cust_002', 'Priya Sharma', '9876543211', '<EMAIL>', '456 Brigade Road, Bangalore, Karnataka 560001', 85000.00, '2024-01-30', NOW(), NOW()),
('cust_003', 'Amit Patel', '9876543212', '<EMAIL>', '789 CG Road, Ahmedabad, Gujarat 380001', 220000.00, '2024-01-29', NOW(), NOW());

-- Insert sample exchange transactions
INSERT IGNORE INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, purchase_bill_generated, created_at, updated_at) VALUES
('exg_001', 'EXG-20240131-001', 'cust_001', '2024-01-31', 33000.00, 'cash', 'Old gold bar exchange', 'completed', TRUE, NOW(), NOW()),
('exg_002', 'EXG-20240130-001', 'cust_002', '2024-01-30', 15600.00, 'cash', 'Silver bangles exchange', 'completed', TRUE, NOW(), NOW()),
('exg_003', 'EXG-20240129-001', 'cust_003', '2024-01-29', 45000.00, 'cash', 'Gold chain exchange', 'completed', FALSE, NOW(), NOW());

-- Insert sample exchange items
INSERT IGNORE INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) VALUES
('item_001', 'exg_001', 'GOLD OLD BAR', 'gold', '22K', 10.000, 2.500, 7.500, 6200.00, 46500.00, 'good', 'Clean gold bar', NOW(), NOW()),
('item_002', 'exg_002', 'OLD SILVER BANGLES', 'silver', '925', 220.000, 20.000, 200.000, 78.00, 15600.00, 'good', 'Set of 4 bangles', NOW(), NOW()),
('item_003', 'exg_003', 'GOLD CHAIN OLD', 'gold', '18K', 15.000, 3.000, 12.000, 5100.00, 61200.00, 'fair', 'Slightly worn chain', NOW(), NOW());

-- Insert sample purchase bills
INSERT IGNORE INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) VALUES
('bill_001', 'EPB/2024-25/0001', 'exg_001', 'cust_001', '2024-01-31', 46500.00, 697.50, 697.50, 47895.00, 'cash', 'paid', 'Purchase bill for gold bar exchange', NOW(), NOW()),
('bill_002', 'EPB/2024-25/0002', 'exg_002', 'cust_002', '2024-01-30', 15600.00, 234.00, 234.00, 16068.00, 'cash', 'paid', 'Purchase bill for silver bangles', NOW(), NOW());

-- Update exchange transactions with correct totals and bill references
UPDATE exchange_transactions SET 
    total_amount = 46500.00,
    purchase_bill_id = 'bill_001',
    purchase_bill_number = 'EPB/2024-25/0001',
    purchase_bill_date = '2024-01-31'
WHERE id = 'exg_001';

UPDATE exchange_transactions SET 
    purchase_bill_id = 'bill_002',
    purchase_bill_number = 'EPB/2024-25/0002',
    purchase_bill_date = '2024-01-30'
WHERE id = 'exg_002';

-- Update bill sequence
UPDATE bill_sequences SET current_number = 2 WHERE sequence_type = 'exchange_purchase';

-- Insert audit trail
INSERT IGNORE INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, related_bill_id, performed_at) VALUES
('audit_001', 'exg_001', 'created', 'Exchange transaction created for gold bar', '{"totalAmount": 46500, "items": 1}', NULL, '2024-01-31 10:30:00'),
('audit_002', 'exg_001', 'billed', 'Purchase bill EPB/2024-25/0001 generated', '{"billNumber": "EPB/2024-25/0001", "totalWithTax": 47895}', 'bill_001', '2024-01-31 11:00:00'),
('audit_003', 'exg_002', 'created', 'Exchange transaction created for silver bangles', '{"totalAmount": 15600, "items": 1}', NULL, '2024-01-30 14:15:00'),
('audit_004', 'exg_002', 'billed', 'Purchase bill EPB/2024-25/0002 generated', '{"billNumber": "EPB/2024-25/0002", "totalWithTax": 16068}', 'bill_002', '2024-01-30 14:45:00');

-- Show setup summary
SELECT 'Exchange System Setup Complete!' as Status;
SELECT 'Sample Data Created:' as Info;
SELECT COUNT(*) as 'Exchange Rates' FROM exchange_rates WHERE is_active = TRUE;
SELECT COUNT(*) as 'Sample Customers' FROM customers WHERE id LIKE 'cust_%';
SELECT COUNT(*) as 'Sample Transactions' FROM exchange_transactions WHERE id LIKE 'exg_%';
SELECT COUNT(*) as 'Sample Bills' FROM exchange_purchase_bills WHERE id LIKE 'bill_%';
