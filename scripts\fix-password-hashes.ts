#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'

async function fixPasswordHashes() {
  console.log('🔐 Fixing Password Hashes with Proper bcrypt Implementation...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Check current password hashes
    console.log('🔍 Step 1: Checking current password hashes...')
    
    const [currentUsers] = await connection.execute(`
      SELECT id, username, email, password_hash 
      FROM users 
      ORDER BY username
    `)

    console.log(`   Found ${(currentUsers as any[]).length} users with current hashes:`)
    ;(currentUsers as any[]).forEach(user => {
      const hashType = user.password_hash?.startsWith('$2b$') ? 'bcrypt' : 'plain/invalid'
      console.log(`      ${user.username} | ${user.email} | Hash: ${hashType}`)
    })

    // Step 2: Generate proper bcrypt hashes
    console.log('\n🔒 Step 2: Generating proper bcrypt hashes...')
    
    const defaultPassword = 'admin123' // Default password for all users
    const saltRounds = 12 // Higher security
    
    console.log(`   Using default password: "${defaultPassword}"`)
    console.log(`   Salt rounds: ${saltRounds}`)
    
    const properHash = await bcrypt.hash(defaultPassword, saltRounds)
    console.log(`   Generated bcrypt hash: ${properHash.substring(0, 20)}...`)

    // Step 3: Update all user passwords with proper bcrypt hashes
    console.log('\n🔄 Step 3: Updating all user passwords...')
    
    const userUpdates = [
      { username: 'admin', password: 'admin123' },
      { username: 'manager', password: 'admin123' },
      { username: 'sales_manager', password: 'admin123' },
      { username: 'sales1', password: 'admin123' },
      { username: 'sales2', password: 'admin123' },
      { username: 'accountant', password: 'admin123' },
      { username: 'cashier1', password: 'admin123' },
      { username: 'cashier2', password: 'admin123' }
    ]

    let updatedCount = 0
    for (const userUpdate of userUpdates) {
      try {
        // Generate individual hash for each user (more secure)
        const hashedPassword = await bcrypt.hash(userUpdate.password, saltRounds)
        
        const [result] = await connection.execute(`
          UPDATE users 
          SET password_hash = ?, updated_at = NOW() 
          WHERE username = ?
        `, [hashedPassword, userUpdate.username])

        const updateResult = result as any
        if (updateResult.affectedRows > 0) {
          console.log(`   ✅ Updated ${userUpdate.username} with bcrypt hash`)
          updatedCount++
        } else {
          console.log(`   ⚠️  User ${userUpdate.username} not found`)
        }
      } catch (error) {
        console.log(`   ❌ Failed to update ${userUpdate.username}: ${error}`)
      }
    }

    console.log(`\n   📊 Updated ${updatedCount}/${userUpdates.length} users`)

    // Step 4: Verify the updated hashes
    console.log('\n✅ Step 4: Verifying updated password hashes...')
    
    const [updatedUsers] = await connection.execute(`
      SELECT id, username, email, password_hash 
      FROM users 
      ORDER BY username
    `)

    console.log(`   Verification results:`)
    for (const user of (updatedUsers as any[])) {
      const hashType = user.password_hash?.startsWith('$2b$') ? 'bcrypt ✅' : 'invalid ❌'
      const hashLength = user.password_hash?.length || 0
      console.log(`      ${user.username} | Hash: ${hashType} | Length: ${hashLength}`)
      
      // Test password verification
      if (user.password_hash?.startsWith('$2b$')) {
        try {
          const isValid = await bcrypt.compare('admin123', user.password_hash)
          console.log(`         Password verification: ${isValid ? '✅ Valid' : '❌ Invalid'}`)
        } catch (error) {
          console.log(`         Password verification: ❌ Error - ${error}`)
        }
      }
    }

    // Step 5: Create a test authentication function
    console.log('\n🧪 Step 5: Testing authentication function...')
    
    const testAuthentication = async (username: string, password: string) => {
      try {
        const [userRows] = await connection.execute(`
          SELECT id, username, email, password_hash, role, is_active 
          FROM users 
          WHERE username = ? AND is_active = TRUE
        `, [username])

        if ((userRows as any[]).length === 0) {
          return { success: false, message: 'User not found' }
        }

        const user = (userRows as any[])[0]
        if (!user.password_hash) {
          return { success: false, message: 'No password hash found' }
        }

        const isValidPassword = await bcrypt.compare(password, user.password_hash)
        if (!isValidPassword) {
          return { success: false, message: 'Invalid password' }
        }

        return { 
          success: true, 
          message: 'Authentication successful',
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role
          }
        }
      } catch (error) {
        return { success: false, message: `Authentication error: ${error}` }
      }
    }

    // Test authentication for admin user
    console.log('   Testing admin authentication...')
    const authResult = await testAuthentication('admin', 'admin123')
    console.log(`   Result: ${authResult.success ? '✅' : '❌'} ${authResult.message}`)
    if (authResult.success && authResult.user) {
      console.log(`   User: ${authResult.user.username} (${authResult.user.role})`)
    }

    // Step 6: Generate summary report
    console.log('\n📊 Step 6: Password Security Summary...')
    
    const [finalStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN password_hash LIKE '$2b$%' THEN 1 END) as bcrypt_hashes,
        COUNT(CASE WHEN password_hash NOT LIKE '$2b$%' THEN 1 END) as invalid_hashes,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users
      FROM users
    `)

    const stats = (finalStats as any[])[0]
    console.log(`   📋 Password Security Report:`)
    console.log(`      Total Users: ${stats.total_users}`)
    console.log(`      Bcrypt Hashes: ${stats.bcrypt_hashes} ✅`)
    console.log(`      Invalid Hashes: ${stats.invalid_hashes} ${stats.invalid_hashes > 0 ? '❌' : '✅'}`)
    console.log(`      Active Users: ${stats.active_users}`)
    console.log(`      Security Status: ${stats.bcrypt_hashes === stats.total_users ? 'SECURE ✅' : 'NEEDS ATTENTION ❌'}`)

    console.log('\n🎉 Password Hash Fix Completed Successfully!')

    console.log('\n🔐 UPDATED LOGIN CREDENTIALS:')
    console.log('=' .repeat(50))
    console.log('All users now have secure bcrypt password hashes')
    console.log('Default password for all accounts: admin123')
    console.log('=' .repeat(50))
    console.log('Username: admin     | Password: admin123')
    console.log('Username: manager   | Password: admin123')
    console.log('Username: sales1    | Password: admin123')
    console.log('Username: sales2    | Password: admin123')
    console.log('Username: accountant| Password: admin123')
    console.log('Username: cashier1  | Password: admin123')
    console.log('Username: cashier2  | Password: admin123')
    console.log('=' .repeat(50))
    console.log('⚠️  IMPORTANT: Change passwords on first login!')

  } catch (error) {
    console.error('\n❌ Password hash fix failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the password hash fix
fixPasswordHashes().catch(console.error)
