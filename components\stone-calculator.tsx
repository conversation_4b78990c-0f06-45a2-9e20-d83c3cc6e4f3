"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Gem, Calculator } from "lucide-react"

export function StoneCalculator() {
  const [formData, setFormData] = useState({
    stoneType: "",
    weight: "",
    quality: "",
    pricePerCarat: "",
  })

  const [result, setResult] = useState<{
    totalValue: number
    weightInCarats: number
    qualityMultiplier: number
  } | null>(null)

  const stoneTypes = [
    { value: "diamond", label: "Diamond" },
    { value: "ruby", label: "Ruby" },
    { value: "emerald", label: "Emerald" },
    { value: "sapphire", label: "Sapphire" },
    { value: "pearl", label: "Pearl" },
    { value: "topaz", label: "Topaz" },
  ]

  const qualities = [
    { value: "premium", label: "Premium", multiplier: 1.5 },
    { value: "high", label: "High", multiplier: 1.2 },
    { value: "medium", label: "Medium", multiplier: 1.0 },
    { value: "low", label: "Low", multiplier: 0.8 },
  ]

  const calculateStoneValue = () => {
    const weight = Number.parseFloat(formData.weight)
    const pricePerCarat = Number.parseFloat(formData.pricePerCarat)

    if (!weight || !pricePerCarat || !formData.quality) {
      alert("Please fill all fields")
      return
    }

    // Convert grams to carats (1 gram = 5 carats)
    const weightInCarats = weight * 5

    const qualityData = qualities.find((q) => q.value === formData.quality)
    const qualityMultiplier = qualityData?.multiplier || 1

    const baseValue = weightInCarats * pricePerCarat
    const totalValue = baseValue * qualityMultiplier

    setResult({
      totalValue,
      weightInCarats,
      qualityMultiplier,
    })
  }

  const resetCalculator = () => {
    setFormData({
      stoneType: "",
      weight: "",
      quality: "",
      pricePerCarat: "",
    })
    setResult(null)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gem className="h-5 w-5 text-purple-600" />
          Stone Calculator
        </CardTitle>
        <CardDescription>Calculate the value of precious stones</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="stoneType">Stone Type</Label>
            <Select
              value={formData.stoneType}
              onValueChange={(value) => setFormData({ ...formData, stoneType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select stone" />
              </SelectTrigger>
              <SelectContent>
                {stoneTypes.map((stone) => (
                  <SelectItem key={stone.value} value={stone.value}>
                    {stone.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="weight">Weight (grams)</Label>
            <Input
              id="weight"
              type="number"
              step="0.01"
              value={formData.weight}
              onChange={(e) => setFormData({ ...formData, weight: e.target.value })}
              placeholder="0.00"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="quality">Quality Grade</Label>
            <Select value={formData.quality || undefined} onValueChange={(value) => setFormData({ ...formData, quality: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select quality" />
              </SelectTrigger>
              <SelectContent>
                {qualities.map((quality) => (
                  <SelectItem key={quality.value} value={quality.value}>
                    {quality.label} ({quality.multiplier}x)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="pricePerCarat">Price per Carat (₹)</Label>
            <Input
              id="pricePerCarat"
              type="number"
              value={formData.pricePerCarat}
              onChange={(e) => setFormData({ ...formData, pricePerCarat: e.target.value })}
              placeholder="10000"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={calculateStoneValue} className="flex-1">
            <Calculator className="h-4 w-4 mr-2" />
            Calculate
          </Button>
          <Button variant="outline" onClick={resetCalculator}>
            Reset
          </Button>
        </div>

        {result && (
          <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">Calculation Result</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Weight in Carats:</span>
                <span className="font-medium">{result.weightInCarats.toFixed(2)} ct</span>
              </div>
              <div className="flex justify-between">
                <span>Quality Multiplier:</span>
                <span className="font-medium">{result.qualityMultiplier}x</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="font-semibold">Total Value:</span>
                <span className="font-bold text-purple-600">₹{result.totalValue.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
