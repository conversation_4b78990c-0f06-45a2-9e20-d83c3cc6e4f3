import { NextRequest, NextResponse } from 'next/server'
import { exchangeRateService } from '@/lib/database/services'

export async function GET() {
  try {
    const rates = await exchangeRateService.findAll()
    return NextResponse.json(rates)
  } catch (error) {
    console.error('Error fetching exchange rates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exchange rates' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const rate = await exchangeRateService.create(data)
    return NextResponse.json(rate, { status: 201 })
  } catch (error) {
    console.error('Error creating exchange rate:', error)
    return NextResponse.json(
      { error: 'Failed to create exchange rate' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, ...updateData } = data
    const rate = await exchangeRateService.update(id, updateData)
    return NextResponse.json(rate)
  } catch (error) {
    console.error('Error updating exchange rate:', error)
    return NextResponse.json(
      { error: 'Failed to update exchange rate' },
      { status: 500 }
    )
  }
}
