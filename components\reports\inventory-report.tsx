"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useStore } from "@/lib/store"
import { Package, AlertTriangle, TrendingUp, IndianRupee, Download } from "lucide-react"
import { useState } from "react"

export function InventoryReport() {
  const { inventory } = useStore()
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockFilter, setStockFilter] = useState("all")

  const filteredInventory = inventory.filter((item) => {
    if (categoryFilter !== "all" && item.category !== categoryFilter) return false
    if (stockFilter === "low" && item.stock >= 5) return false
    if (stockFilter === "out" && item.stock > 0) return false
    return true
  })

  const calculateTotals = () => {
    const totalValue = filteredInventory.reduce((sum, item) => sum + item.currentValue * item.stock, 0)
    const totalNetWeight = filteredInventory.reduce((sum, item) => sum + item.netWeight * item.stock, 0)
    const totalGrossWeight = filteredInventory.reduce((sum, item) => sum + item.grossWeight * item.stock, 0)
    const totalItems = filteredInventory.reduce((sum, item) => sum + item.stock, 0)
    const lowStockItems = inventory.filter((item) => item.stock < 5).length
    const outOfStockItems = inventory.filter((item) => item.stock === 0).length

    return { totalValue, totalNetWeight, totalGrossWeight, totalItems, lowStockItems, outOfStockItems }
  }

  const totals = calculateTotals()

  const getCategories = () => {
    return [...new Set(inventory.map((item) => item.category))]
  }

  const exportReport = () => {
    alert("Inventory report exported successfully!")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold tracking-tight">Inventory Report</h3>
          <p className="text-muted-foreground">Complete inventory analysis and stock management</p>
        </div>
        <Button onClick={exportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {getCategories().map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={stockFilter} onValueChange={setStockFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Stock status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Items</SelectItem>
            <SelectItem value="low">Low Stock</SelectItem>
            <SelectItem value="out">Out of Stock</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totals.totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{filteredInventory.length} unique items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Net Weight</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totals.totalNetWeight.toFixed(1)}g</div>
            <p className="text-xs text-muted-foreground">Pure metal weight</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totals.totalItems}</div>
            <p className="text-xs text-muted-foreground">In stock</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{totals.lowStockItems}</div>
            <p className="text-xs text-muted-foreground">Low stock items</p>
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Category Analysis</CardTitle>
          <CardDescription>Inventory breakdown by jewelry category</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead>Items</TableHead>
                <TableHead>Total Stock</TableHead>
                <TableHead>Net Weight</TableHead>
                <TableHead>Total Value</TableHead>
                <TableHead>Avg. Value</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {getCategories().map((category) => {
                const categoryItems = inventory.filter((item) => item.category === category)
                const totalStock = categoryItems.reduce((sum, item) => sum + item.stock, 0)
                const totalNetWeight = categoryItems.reduce((sum, item) => sum + item.netWeight * item.stock, 0)
                const totalValue = categoryItems.reduce((sum, item) => sum + item.currentValue * item.stock, 0)
                const avgValue = totalValue / categoryItems.length

                return (
                  <TableRow key={category}>
                    <TableCell>
                      <Badge variant="outline">{category}</Badge>
                    </TableCell>
                    <TableCell>{categoryItems.length}</TableCell>
                    <TableCell>{totalStock}</TableCell>
                    <TableCell className="text-blue-600 font-medium">{totalNetWeight.toFixed(1)}g</TableCell>
                    <TableCell>₹{totalValue.toLocaleString()}</TableCell>
                    <TableCell>₹{avgValue.toLocaleString()}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Detailed Inventory */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Inventory</CardTitle>
          <CardDescription>Complete item-wise inventory details</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item Code</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Net Weight</TableHead>
                <TableHead>Stock</TableHead>
                <TableHead>Unit Value</TableHead>
                <TableHead>Total Value</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInventory.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.id}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.category}</Badge>
                  </TableCell>
                  <TableCell className="text-blue-600 font-medium">{item.netWeight}g</TableCell>
                  <TableCell>
                    <Badge variant={item.stock < 5 ? "destructive" : item.stock < 10 ? "secondary" : "default"}>
                      {item.stock}
                    </Badge>
                  </TableCell>
                  <TableCell>₹{item.currentValue.toLocaleString()}</TableCell>
                  <TableCell className="font-medium">₹{(item.currentValue * item.stock).toLocaleString()}</TableCell>
                  <TableCell>
                    {item.stock === 0 ? (
                      <Badge variant="destructive">Out of Stock</Badge>
                    ) : item.stock < 5 ? (
                      <Badge variant="secondary">Low Stock</Badge>
                    ) : (
                      <Badge variant="default">In Stock</Badge>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
