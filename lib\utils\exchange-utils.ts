/**
 * Utility functions for exchange operations
 */

// Generate exchange transaction number
export function generateExchangeTransactionNumber(date?: Date): string {
  const transactionDate = date || new Date()
  const dateStr = transactionDate.toISOString().slice(0, 10).replace(/-/g, '')
  
  // In a real implementation, you would query the database to get the count
  // For now, we'll use a random number for demo purposes
  const count = Math.floor(Math.random() * 999) + 1
  
  return `EXG-${dateStr}-${String(count).padStart(3, '0')}`
}

// Calculate net weight from gross weight and stone weight
export function calculateNetWeight(grossWeight: number, stoneWeight: number = 0): number {
  return Math.max(0, grossWeight - stoneWeight)
}

// Calculate amount from net weight and rate
export function calculateAmount(netWeight: number, ratePerGram: number): number {
  return Math.round(netWeight * ratePerGram * 100) / 100
}

// Calculate purity percentage for display
export function getPurityPercentage(purity: string): number {
  switch (purity) {
    case '24K': return 100
    case '22K': return 91.6
    case '18K': return 75.0
    case '14K': return 58.3
    case '916': return 91.6
    case '750': return 75.0
    case '999': return 99.9
    case '925': return 92.5
    case '900': return 90.0
    default: return 0
  }
}

// Get common purities for a metal type
export function getCommonPurities(metalType: 'gold' | 'silver'): string[] {
  if (metalType === 'gold') {
    return ['24K', '22K', '18K', '14K', '916', '750']
  } else {
    return ['999', '925', '900']
  }
}

// Format weight for display
export function formatWeight(weight: number, decimals: number = 3): string {
  return weight.toFixed(decimals)
}

// Calculate wastage amount
export function calculateWastage(netWeight: number, wastagePercentage: number): number {
  return (netWeight * wastagePercentage) / 100
}

// Calculate making charges
export function calculateMakingCharges(netWeight: number, makingChargePerGram: number): number {
  return netWeight * makingChargePerGram
}

// Validate exchange item data
export function validateExchangeItem(item: {
  itemDescription: string
  grossWeight: number
  stoneWeight: number
  ratePerGram: number
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!item.itemDescription.trim()) {
    errors.push('Item description is required')
  }
  
  if (item.grossWeight <= 0) {
    errors.push('Gross weight must be greater than 0')
  }
  
  if (item.stoneWeight < 0) {
    errors.push('Stone weight cannot be negative')
  }
  
  if (item.stoneWeight >= item.grossWeight) {
    errors.push('Stone weight cannot be greater than or equal to gross weight')
  }
  
  if (item.ratePerGram <= 0) {
    errors.push('Rate per gram must be greater than 0')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Calculate exchange statistics
export function calculateExchangeStats(transactions: any[]): {
  totalTransactions: number
  totalAmount: number
  goldAmount: number
  silverAmount: number
  averageTransactionValue: number
  totalGoldWeight: number
  totalSilverWeight: number
} {
  const completedTransactions = transactions.filter(t => t.status === 'completed')
  
  let totalAmount = 0
  let goldAmount = 0
  let silverAmount = 0
  let totalGoldWeight = 0
  let totalSilverWeight = 0
  
  completedTransactions.forEach(transaction => {
    totalAmount += transaction.totalAmount
    
    transaction.items.forEach((item: any) => {
      if (item.metalType === 'gold') {
        goldAmount += item.amount
        totalGoldWeight += item.netWeight
      } else if (item.metalType === 'silver') {
        silverAmount += item.amount
        totalSilverWeight += item.netWeight
      }
    })
  })
  
  return {
    totalTransactions: completedTransactions.length,
    totalAmount,
    goldAmount,
    silverAmount,
    averageTransactionValue: completedTransactions.length > 0 ? totalAmount / completedTransactions.length : 0,
    totalGoldWeight,
    totalSilverWeight
  }
}

// Get exchange rate for metal type and purity
export function getExchangeRate(
  rates: { metalType: string; purity: string; ratePerGram: number }[],
  metalType: string,
  purity: string
): number {
  const rate = rates.find(r => r.metalType === metalType && r.purity === purity)
  return rate ? rate.ratePerGram : 0
}

// Format exchange transaction for export
export function formatTransactionForExport(transaction: any): any {
  return {
    'Transaction Number': transaction.transactionNumber,
    'Date': transaction.transactionDate,
    'Customer': transaction.customer?.name || 'Walk-in Customer',
    'Phone': transaction.customer?.phone || '',
    'Total Amount': transaction.totalAmount,
    'Payment Method': transaction.paymentMethod,
    'Status': transaction.status,
    'Items Count': transaction.items.length,
    'Notes': transaction.notes || ''
  }
}

// Format exchange item for export
export function formatItemForExport(item: any, transactionNumber: string): any {
  return {
    'Transaction Number': transactionNumber,
    'Item Description': item.itemDescription,
    'Metal Type': item.metalType,
    'Purity': item.purity,
    'Gross Weight (g)': item.grossWeight,
    'Stone Weight (g)': item.stoneWeight,
    'Net Weight (g)': item.netWeight,
    'Rate per Gram': item.ratePerGram,
    'Amount': item.amount,
    'Condition': item.itemCondition,
    'Notes': item.notes || ''
  }
}
