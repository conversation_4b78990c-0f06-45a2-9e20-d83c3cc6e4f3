#!/usr/bin/env tsx

// Load environment variables FIRST before any other imports
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

// Now import database modules
import { getPool } from '../lib/database/config'
import type { RowDataPacket } from 'mysql2'
import { randomUUID } from 'crypto'

async function addSampleData() {
  console.log('🌱 Adding sample data to the database...\n')

  const pool = getPool()

  try {
    // Add sample customers
    console.log('1. Adding sample customers...')
    const customers = [
      {
        id: randomUUID(),
        name: '<PERSON><PERSON>',
        phone: '+91 98765 43210',
        email: 'raj<PERSON>.<EMAIL>',
        address: '123 MG Road, Mumbai, Maharashtra 400001',
        gst_number: '27ABCDE1234F1Z5'
      },
      {
        id: randomUUID(),
        name: '<PERSON><PERSON>',
        phone: '+91 87654 32109',
        email: '<EMAIL>',
        address: '456 Park Street, Delhi, Delhi 110001',
        gst_number: null
      },
      {
        id: randomUUID(),
        name: '<PERSON><PERSON>',
        phone: '+91 76543 21098',
        email: '<EMAIL>',
        address: '789 Commercial Street, Bangalore, Karnataka 560001',
        gst_number: '29FGHIJ5678K2L6'
      }
    ]

    for (const customer of customers) {
      await pool.execute(
        `INSERT IGNORE INTO customers (id, name, phone, email, address, gst_number, total_purchases, last_visit, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), NOW(), NOW())`,
        [customer.id, customer.name, customer.phone, customer.email, customer.address, customer.gst_number]
      )
    }
    console.log('✅ Sample customers added')

    // Add sample inventory items
    console.log('2. Adding sample inventory items...')
    const inventoryItems = [
      {
        id: randomUUID(),
        name: 'Gold Ring 22K',
        category: 'rings',
        metal_type: 'gold',
        purity: '22K',
        gross_weight: 5.5,
        stone_weight: 0.0,
        net_weight: 5.5,
        making_charges: 500,
        stone_amount: 0,
        current_value: 35000,
        stock: 25,
        description: 'Beautiful 22K gold ring'
      },
      {
        id: randomUUID(),
        name: 'Diamond Necklace',
        category: 'necklaces',
        metal_type: 'gold',
        purity: '18K',
        gross_weight: 15.2,
        stone_weight: 2.0,
        net_weight: 13.2,
        making_charges: 2500,
        stone_amount: 15000,
        current_value: 85000,
        stock: 8,
        description: 'Elegant diamond necklace with 18K gold'
      },
      {
        id: randomUUID(),
        name: 'Silver Bracelet',
        category: 'bracelets',
        metal_type: 'silver',
        purity: '925',
        gross_weight: 12.0,
        stone_weight: 0.0,
        net_weight: 12.0,
        making_charges: 300,
        stone_amount: 0,
        current_value: 2500,
        stock: 15,
        description: '925 sterling silver bracelet'
      },
      {
        id: randomUUID(),
        name: 'Gold Earrings 24K',
        category: 'earrings',
        metal_type: 'gold',
        purity: '24K',
        gross_weight: 8.3,
        stone_weight: 0.5,
        net_weight: 7.8,
        making_charges: 800,
        stone_amount: 2000,
        current_value: 45000,
        stock: 12,
        description: '24K gold earrings with precious stones'
      }
    ]

    for (const item of inventoryItems) {
      await pool.execute(
        `INSERT IGNORE INTO inventory (id, name, category, metal_type, purity, gross_weight, stone_weight, net_weight, making_charges, stone_amount, current_value, stock, description, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [item.id, item.name, item.category, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.making_charges, item.stone_amount, item.current_value, item.stock, item.description]
      )
    }
    console.log('✅ Sample inventory items added')

    // Add sample sales
    console.log('3. Adding sample sales...')
    const sales = [
      {
        id: randomUUID(),
        customer_id: customers[0].id,
        subtotal: 35000,
        cgst: 1575, // 4.5%
        sgst: 1575, // 4.5%
        total: 38150,
        status: 'paid',
        date: new Date().toISOString().split('T')[0]
      },
      {
        id: randomUUID(),
        customer_id: customers[1].id,
        subtotal: 85000,
        cgst: 3825, // 4.5%
        sgst: 3825, // 4.5%
        total: 92650,
        status: 'paid',
        date: new Date().toISOString().split('T')[0]
      }
    ]

    for (const sale of sales) {
      await pool.execute(
        `INSERT INTO sales (id, customer_id, subtotal, cgst, sgst, total, status, date, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [sale.id, sale.customer_id, sale.subtotal, sale.cgst, sale.sgst, sale.total, sale.status, sale.date]
      )
    }
    console.log('✅ Sample sales added')

    // Add sample repair orders
    console.log('4. Adding sample repair orders...')
    const repairs = [
      {
        id: randomUUID(),
        customer_id: customers[0].id,
        item: 'Gold chain repair - broken link',
        description: 'Chain link is broken, needs soldering',
        order_type: 'repair',
        received_date: new Date().toISOString().split('T')[0],
        promised_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        charges: 500,
        status: 'in-progress',
        special_instructions: 'Handle with care, antique piece'
      },
      {
        id: randomUUID(),
        customer_id: customers[2].id,
        item: 'Silver ring resizing',
        description: 'Ring size needs to be increased by 2 sizes',
        order_type: 'resize',
        received_date: new Date().toISOString().split('T')[0],
        promised_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 days from now
        charges: 300,
        status: 'pending',
        special_instructions: 'Customer prefers matte finish'
      }
    ]

    for (const repair of repairs) {
      await pool.execute(
        `INSERT INTO repairs (id, customer_id, item, description, order_type, received_date, promised_date, charges, status, special_instructions, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [repair.id, repair.customer_id, repair.item, repair.description, repair.order_type, repair.received_date, repair.promised_date, repair.charges, repair.status, repair.special_instructions]
      )
    }
    console.log('✅ Sample repair orders added')

    // Add sample schemes
    console.log('5. Adding sample schemes...')
    const schemes = [
      {
        id: randomUUID(),
        name: 'Gold Savings Scheme',
        customer_id: customers[1].id,
        total_amount: 50000,
        monthly_amount: 5000,
        duration: 10,
        paid_amount: 15000,
        start_date: new Date().toISOString().split('T')[0],
        status: 'active'
      }
    ]

    for (const scheme of schemes) {
      await pool.execute(
        `INSERT INTO schemes (id, name, customer_id, total_amount, monthly_amount, duration, paid_amount, start_date, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [scheme.id, scheme.name, scheme.customer_id, scheme.total_amount, scheme.monthly_amount, scheme.duration, scheme.paid_amount, scheme.start_date, scheme.status]
      )
    }
    console.log('✅ Sample schemes added')

    console.log('\n🎉 Sample data added successfully!')
    console.log('\nSample data includes:')
    console.log('- 3 customers')
    console.log('- 4 inventory items')
    console.log('- 2 sales records')
    console.log('- 2 repair orders')
    console.log('- 1 scheme')

  } catch (error) {
    console.error('❌ Error adding sample data:', error)
    throw error
  } finally {
    pool.end()
  }
}

// Run the script
addSampleData().catch(console.error)
