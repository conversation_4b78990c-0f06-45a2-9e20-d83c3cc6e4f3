#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function seedTiruppurTestData() {
  console.log('🌱 Seeding Comprehensive Test Data for JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Create remaining essential tables
    console.log('🏗️  Step 1: Creating remaining essential tables...')
    
    // Categories table
    console.log('   Creating categories table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS categories (
        id VARCHAR(36) PRIMARY KEY,
        category_code VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        parent_id VARCHAR(36),
        category_level INT DEFAULT 1,
        
        -- Business Configuration
        hsn_code VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        
        -- Visual
        image_url VARCHAR(500),
        
        -- Pricing Configuration
        making_charge_type ENUM('percentage', 'fixed', 'per_gram', 'per_piece') DEFAULT 'percentage',
        making_charge_value DECIMAL(10,2) DEFAULT 0.00,
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        margin_percentage DECIMAL(5,2) DEFAULT 0.00,
        
        -- Business Rules
        requires_hallmarking BOOLEAN DEFAULT FALSE,
        requires_certification BOOLEAN DEFAULT FALSE,
        allows_customization BOOLEAN DEFAULT TRUE,
        minimum_weight DECIMAL(8,3) DEFAULT 0.000,
        maximum_weight DECIMAL(8,3) DEFAULT 999.999,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_category_code (category_code),
        INDEX idx_name (name),
        INDEX idx_parent_id (parent_id),
        INDEX idx_hsn_code (hsn_code),
        INDEX idx_is_active (is_active),
        INDEX idx_sort_order (sort_order)
      )
    `)

    // Metal Rates table
    console.log('   Creating metal_rates table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS metal_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20) NOT NULL,
        
        -- Pricing Information
        rate_per_gram DECIMAL(12,2) NOT NULL,
        rate_per_10gram DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 10) STORED,
        rate_per_tola DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 11.664) STORED,
        rate_per_ounce DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 31.1035) STORED,
        
        -- Validity
        effective_date DATE NOT NULL,
        effective_time TIME DEFAULT '00:00:00',
        expiry_date DATE,
        is_active BOOLEAN DEFAULT TRUE,
        
        -- Source Information
        source ENUM('manual', 'api', 'import', 'market') DEFAULT 'manual',
        source_reference VARCHAR(255),
        
        -- Market Information
        opening_rate DECIMAL(12,2),
        closing_rate DECIMAL(12,2),
        high_rate DECIMAL(12,2),
        low_rate DECIMAL(12,2),
        change_amount DECIMAL(12,2),
        change_percentage DECIMAL(5,2),
        
        -- Additional Information
        notes TEXT,
        currency VARCHAR(10) DEFAULT 'INR',
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_active_rate (metal_type, purity, effective_date, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date),
        INDEX idx_is_active (is_active),
        INDEX idx_source (source)
      )
    `)

    // Exchange Rates table
    console.log('   Creating exchange_rates table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS exchange_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20) NOT NULL,
        
        -- Exchange Pricing
        rate_per_gram DECIMAL(12,2) NOT NULL,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        effective_rate DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * (100 - discount_percentage) / 100) STORED,
        
        -- Validity
        effective_date DATE NOT NULL,
        effective_time TIME DEFAULT '00:00:00',
        expiry_date DATE,
        is_active BOOLEAN DEFAULT TRUE,
        
        -- Source Information
        source ENUM('manual', 'derived', 'import') DEFAULT 'derived',
        base_metal_rate_id VARCHAR(36),
        
        -- Conditions
        minimum_weight DECIMAL(8,3) DEFAULT 0.000,
        maximum_weight DECIMAL(8,3) DEFAULT 999.999,
        
        -- Additional Information
        notes TEXT,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_active_exchange_rate (metal_type, purity, effective_date, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date),
        INDEX idx_is_active (is_active),
        INDEX idx_effective_rate (effective_rate)
      )
    `)

    // Inventory table
    console.log('   Creating inventory table...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS inventory (
        id VARCHAR(36) PRIMARY KEY,
        item_code VARCHAR(100) UNIQUE NOT NULL,
        barcode VARCHAR(100) UNIQUE,
        
        -- Basic Information
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id VARCHAR(36),
        brand VARCHAR(100),
        
        -- Metal Information
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20),
        hallmark_number VARCHAR(100),
        
        -- Weight Information
        gross_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
        diamond_weight DECIMAL(8,3) DEFAULT 0.000,
        
        -- Wastage Calculation
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        wastage_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight * wastage_percentage / 100) STORED,
        chargeable_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight + wastage_weight) STORED,
        
        -- Stone Information
        diamond_pieces INT DEFAULT 0,
        colored_stone_pieces INT DEFAULT 0,
        stone_details JSON,
        
        -- Design Information
        size VARCHAR(50),
        gender ENUM('male', 'female', 'unisex', 'kids') DEFAULT 'unisex',
        occasion VARCHAR(100),
        design_number VARCHAR(100),
        
        -- Pricing Information
        purchase_rate DECIMAL(12,2) DEFAULT 0.00,
        metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (chargeable_weight * purchase_rate) STORED,
        making_charges DECIMAL(12,2) DEFAULT 0.00,
        stone_charges DECIMAL(12,2) DEFAULT 0.00,
        other_charges DECIMAL(12,2) DEFAULT 0.00,
        total_cost DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges) STORED,
        
        -- Selling Price Calculation
        margin_percentage DECIMAL(5,2) DEFAULT 0.00,
        selling_price DECIMAL(12,2) DEFAULT 0.00,
        mrp DECIMAL(12,2) DEFAULT 0.00,
        
        -- Stock Information
        stock_quantity INT DEFAULT 1,
        reserved_quantity INT DEFAULT 0,
        available_quantity INT GENERATED ALWAYS AS (stock_quantity - reserved_quantity) STORED,
        min_stock_level INT DEFAULT 0,
        
        -- Location Information
        location VARCHAR(100),
        
        -- Status Information
        status ENUM('active', 'sold', 'reserved', 'damaged', 'repair', 'lost', 'stolen', 'inactive') DEFAULT 'active',
        condition_status ENUM('new', 'excellent', 'good', 'fair', 'poor') DEFAULT 'new',
        
        -- Additional Information
        images JSON,
        tags JSON,
        
        -- Compliance Information
        hsn_code VARCHAR(20),
        country_of_origin VARCHAR(100) DEFAULT 'India',
        requires_hallmarking BOOLEAN DEFAULT FALSE,
        is_hallmarked BOOLEAN DEFAULT FALSE,
        
        -- Business Rules
        is_customizable BOOLEAN DEFAULT FALSE,
        is_returnable BOOLEAN DEFAULT TRUE,
        return_days INT DEFAULT 7,
        warranty_period_months INT DEFAULT 0,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_item_code (item_code),
        INDEX idx_barcode (barcode),
        INDEX idx_name (name),
        INDEX idx_category (category_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity (purity),
        INDEX idx_status (status),
        INDEX idx_location (location),
        INDEX idx_selling_price (selling_price),
        INDEX idx_stock_quantity (stock_quantity),
        INDEX idx_created_at (created_at)
      )
    `)

    console.log('   ✅ Essential tables created successfully')

    // Step 2: Seed comprehensive categories
    console.log('\n📂 Step 2: Creating comprehensive jewelry categories...')
    const categories = [
      { name: 'Gold Rings', code: 'GRING', hsn: '71131900', charge_type: 'percentage', charge_value: 18.00, wastage: 10.00, hallmark: true },
      { name: 'Silver Rings', code: 'SRING', hsn: '71131900', charge_type: 'percentage', charge_value: 15.00, wastage: 8.00, hallmark: false },
      { name: 'Gold Necklaces', code: 'GNECK', hsn: '71131100', charge_type: 'percentage', charge_value: 20.00, wastage: 12.00, hallmark: true },
      { name: 'Silver Necklaces', code: 'SNECK', hsn: '71131100', charge_type: 'percentage', charge_value: 18.00, wastage: 10.00, hallmark: false },
      { name: 'Gold Earrings', code: 'GEARR', hsn: '71131200', charge_type: 'percentage', charge_value: 15.00, wastage: 8.00, hallmark: false },
      { name: 'Silver Earrings', code: 'SEARR', hsn: '71131200', charge_type: 'percentage', charge_value: 12.00, wastage: 6.00, hallmark: false },
      { name: 'Gold Bangles', code: 'GBANG', hsn: '71131300', charge_type: 'percentage', charge_value: 12.00, wastage: 6.00, hallmark: true },
      { name: 'Silver Bangles', code: 'SBANG', hsn: '71131300', charge_type: 'percentage', charge_value: 10.00, wastage: 5.00, hallmark: false },
      { name: 'Gold Chains', code: 'GCHAI', hsn: '71131400', charge_type: 'percentage', charge_value: 8.00, wastage: 4.00, hallmark: true },
      { name: 'Silver Chains', code: 'SCHAI', hsn: '71131400', charge_type: 'percentage', charge_value: 12.00, wastage: 6.00, hallmark: false },
      { name: 'Gold Pendants', code: 'GPEND', hsn: '71131500', charge_type: 'percentage', charge_value: 16.00, wastage: 9.00, hallmark: false },
      { name: 'Silver Pendants', code: 'SPEND', hsn: '71131500', charge_type: 'percentage', charge_value: 14.00, wastage: 7.00, hallmark: false },
      { name: 'Temple Jewelry', code: 'TEMP', hsn: '71131900', charge_type: 'percentage', charge_value: 25.00, wastage: 15.00, hallmark: true },
      { name: 'Antique Jewelry', code: 'ANTQ', hsn: '71131900', charge_type: 'percentage', charge_value: 30.00, wastage: 20.00, hallmark: false },
      { name: 'Gold Coins', code: 'GCOIN', hsn: '71131000', charge_type: 'fixed', charge_value: 300.00, wastage: 0.00, hallmark: true },
      { name: 'Silver Coins', code: 'SCOIN', hsn: '71131000', charge_type: 'fixed', charge_value: 100.00, wastage: 0.00, hallmark: false }
    ]

    for (const category of categories) {
      await connection.execute(`
        INSERT INTO categories (
          id, category_code, name, hsn_code, making_charge_type, making_charge_value,
          wastage_percentage, requires_hallmarking, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())
      `, [
        randomUUID(), category.code, category.name, category.hsn,
        category.charge_type, category.charge_value, category.wastage, category.hallmark
      ])
    }
    console.log(`   ✅ Created ${categories.length} jewelry categories`)

    // Step 3: Seed current metal rates
    console.log('\n💰 Step 3: Setting up current metal rates...')
    const metalRates = [
      { metal: 'gold', purity: '24K', rate: 7350.00, opening: 7320.00, closing: 7380.00, high: 7400.00, low: 7300.00, change: 30.00, change_pct: 0.41 },
      { metal: 'gold', purity: '22K', rate: 6740.00, opening: 6710.00, closing: 6770.00, high: 6790.00, low: 6690.00, change: 28.00, change_pct: 0.42 },
      { metal: 'gold', purity: '21K', rate: 6430.00, opening: 6400.00, closing: 6460.00, high: 6480.00, low: 6380.00, change: 26.00, change_pct: 0.41 },
      { metal: 'gold', purity: '18K', rate: 5510.00, opening: 5480.00, closing: 5540.00, high: 5560.00, low: 5460.00, change: 24.00, change_pct: 0.44 },
      { metal: 'silver', purity: '999', rate: 92.50, opening: 91.80, closing: 93.20, high: 93.50, low: 91.50, change: 0.70, change_pct: 0.76 },
      { metal: 'silver', purity: '925', rate: 85.50, opening: 84.90, closing: 86.10, high: 86.30, low: 84.70, change: 0.65, change_pct: 0.77 },
      { metal: 'silver', purity: '900', rate: 83.25, opening: 82.70, closing: 83.80, high: 84.00, low: 82.50, change: 0.60, change_pct: 0.73 },
      { metal: 'platinum', purity: '950', rate: 3280.00, opening: 3250.00, closing: 3310.00, high: 3330.00, low: 3230.00, change: 20.00, change_pct: 0.61 }
    ]

    for (const rate of metalRates) {
      const rateId = randomUUID()

      await connection.execute(`
        INSERT INTO metal_rates (
          id, metal_type, purity, rate_per_gram, opening_rate, closing_rate, high_rate, low_rate,
          change_amount, change_percentage, effective_date, is_active, source, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), TRUE, 'manual', NOW(), NOW())
      `, [
        rateId, rate.metal, rate.purity, rate.rate, rate.opening, rate.closing,
        rate.high, rate.low, rate.change, rate.change_pct
      ])

      // Create corresponding exchange rates with Tiruppur-specific discounts
      const exchangeDiscount = rate.metal === 'gold' ? 2.5 : rate.metal === 'silver' ? 3.0 : 2.0
      await connection.execute(`
        INSERT INTO exchange_rates (
          id, metal_type, purity, rate_per_gram, discount_percentage, effective_date,
          is_active, source, base_metal_rate_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, CURDATE(), TRUE, 'derived', ?, NOW(), NOW())
      `, [randomUUID(), rate.metal, rate.purity, rate.rate, exchangeDiscount, rateId])
    }
    console.log(`   ✅ Set up ${metalRates.length} metal rates with exchange rates`)

    // Step 4: Create diverse Tiruppur customers
    console.log('\n👥 Step 4: Creating diverse Tiruppur customers...')
    const customers = [
      {
        id: randomUUID(),
        code: 'TRP000001',
        first_name: 'Rajesh',
        last_name: 'Murugan',
        phone: '+91-9876543301',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Textile Business Owner',
        purchases: 450000.00,
        loyalty_tier: 'gold',
        preferred_language: 'tamil'
      },
      {
        id: randomUUID(),
        code: 'TRP000002',
        first_name: 'Priya',
        last_name: 'Selvam',
        phone: '+91-9876543302',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Doctor',
        purchases: 280000.00,
        loyalty_tier: 'silver',
        preferred_language: 'english'
      },
      {
        id: randomUUID(),
        code: 'TRP000003',
        first_name: 'Murugan',
        last_name: 'Raman',
        phone: '+91-9876543303',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Engineer',
        purchases: 320000.00,
        loyalty_tier: 'gold',
        preferred_language: 'tamil'
      },
      {
        id: randomUUID(),
        code: 'TRP000004',
        first_name: 'Kavitha',
        last_name: 'Devi',
        phone: '+91-9876543304',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Teacher',
        purchases: 180000.00,
        loyalty_tier: 'silver',
        preferred_language: 'tamil'
      },
      {
        id: randomUUID(),
        code: 'TRP000005',
        first_name: 'Senthil',
        last_name: 'Kumar',
        phone: '+91-98********',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Garment Exporter',
        purchases: 650000.00,
        loyalty_tier: 'platinum',
        preferred_language: 'english'
      },
      {
        id: randomUUID(),
        code: 'TRP000006',
        first_name: 'Lakshmi',
        last_name: 'Narayanan',
        phone: '+91-**********',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Chartered Accountant',
        purchases: 380000.00,
        loyalty_tier: 'gold',
        preferred_language: 'english'
      },
      {
        id: randomUUID(),
        code: 'TRP000007',
        first_name: 'Arjun',
        last_name: 'Prasad',
        phone: '+91-**********',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Software Engineer',
        purchases: 220000.00,
        loyalty_tier: 'silver',
        preferred_language: 'english'
      },
      {
        id: randomUUID(),
        code: 'TRP000008',
        first_name: 'Meera',
        last_name: 'Bai',
        phone: '+91-**********',
        email: '<EMAIL>',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        occupation: 'Housewife',
        purchases: 150000.00,
        loyalty_tier: 'bronze',
        preferred_language: 'tamil'
      }
    ]

    for (const customer of customers) {
      await connection.execute(`
        INSERT INTO customers (
          id, customer_code, customer_type, first_name, last_name, phone, email,
          city, state, country, occupation, total_purchases, loyalty_points, loyalty_tier,
          preferred_contact, preferred_language, preferred_metal, is_active,
          last_visit, created_at, updated_at
        ) VALUES (?, ?, 'individual', ?, ?, ?, ?, ?, ?, 'India', ?, ?, ?, ?, 'phone', ?, 'gold', TRUE, CURDATE(), NOW(), NOW())
      `, [
        customer.id, customer.code, customer.first_name, customer.last_name,
        customer.phone, customer.email, customer.city, customer.state,
        customer.occupation, customer.purchases, Math.floor(customer.purchases / 1000),
        customer.loyalty_tier, customer.preferred_language
      ])
    }
    console.log(`   ✅ Created ${customers.length} diverse Tiruppur customers`)

    console.log('\n🎉 Comprehensive Test Data Seeded Successfully for JJ Jewellers Tiruppur!')

  } catch (error) {
    console.error('\n❌ Tiruppur test data seeding failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the Tiruppur test data seeding
seedTiruppurTestData().catch(console.error)
