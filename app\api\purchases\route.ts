import { NextRequest, NextResponse } from 'next/server'
import { purchaseService } from '@/lib/database/services'

export async function GET() {
  try {
    const purchases = await purchaseService.findAll()
    return NextResponse.json({ purchases })
  } catch (error) {
    console.error('Error fetching purchases:', error)
    return NextResponse.json(
      { error: 'Failed to fetch purchases' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const purchaseData = await request.json()
    const purchase = await purchaseService.create(purchaseData)
    return NextResponse.json({ purchase })
  } catch (error) {
    console.error('Error creating purchase:', error)
    return NextResponse.json(
      { error: 'Failed to create purchase' },
      { status: 500 }
    )
  }
}
