/**
 * Migration script to add HSN code support to the inventory table
 * Run this script to update the database schema and existing data
 */

const fs = require('fs')
const path = require('path')
const mysql = require('mysql2/promise')

async function runMigration() {
  let connection

  try {
    // Database connection configuration
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'jjjewellers_db',
      multipleStatements: true
    }

    console.log('🔄 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../lib/database/migrations/add-hsn-code.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    console.log('🔄 Running HSN code migration...')

    // Split SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => {
        // Remove empty statements and comment-only statements
        if (stmt.length === 0) return false
        // Remove lines that are only comments
        const lines = stmt.split('\n').filter(line => !line.trim().startsWith('--') && line.trim().length > 0)
        return lines.length > 0
      })
      .map(stmt => {
        // Remove comment lines from statements
        return stmt.split('\n')
          .filter(line => !line.trim().startsWith('--'))
          .join('\n')
          .trim()
      })
      .filter(stmt => stmt.length > 0)

    console.log('📋 Statements to execute:')
    statements.forEach((stmt, i) => {
      console.log(`   ${i + 1}: ${stmt.substring(0, 50)}...`)
    })

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement) {
        console.log(`   Executing statement ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`)
        await connection.execute(statement)
      }
    }
    
    console.log('✅ HSN code migration completed successfully!')
    
    // Verify the migration
    console.log('🔄 Verifying migration...')
    const [rows] = await connection.execute('DESCRIBE inventory')
    const hsnColumn = rows.find(row => row.Field === 'hsn_code')
    
    if (hsnColumn) {
      console.log('✅ HSN code column added successfully')
      console.log(`   Type: ${hsnColumn.Type}`)
      console.log(`   Null: ${hsnColumn.Null}`)
      console.log(`   Default: ${hsnColumn.Default}`)
    } else {
      console.log('❌ HSN code column not found')
    }

    // Check updated records
    const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM inventory WHERE hsn_code IS NOT NULL')
    console.log(`✅ Updated ${countResult[0].count} inventory records with HSN codes`)

    // Show sample of updated records
    const [sampleRecords] = await connection.execute('SELECT id, name, category, hsn_code FROM inventory LIMIT 5')
    console.log('\n📋 Sample updated records:')
    console.table(sampleRecords)

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

// Check if this script is being run directly
if (require.main === module) {
  console.log('🚀 Starting HSN Code Migration')
  console.log('================================')
  runMigration()
    .then(() => {
      console.log('\n🎉 Migration completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error)
      process.exit(1)
    })
}

module.exports = { runMigration }
