import { getPool } from './config'
import { RowDataPacket, ResultSetHeader } from 'mysql2'

export abstract class BaseService<T> {
  protected pool = getPool()
  protected abstract tableName: string

  protected generateId(): string {
    return crypto.randomUUID()
  }

  protected async executeQuery<R = RowDataPacket[]>(
    sql: string, 
    params: any[] = []
  ): Promise<R> {
    const [rows] = await this.pool.execute(sql, params)
    return rows as R
  }

  protected async executeUpdate(
    sql: string, 
    params: any[] = []
  ): Promise<ResultSetHeader> {
    const [result] = await this.pool.execute(sql, params)
    return result as ResultSetHeader
  }

  protected buildWhereClause(conditions: Record<string, any>): { sql: string; params: any[] } {
    const keys = Object.keys(conditions).filter(key => conditions[key] !== undefined)
    if (keys.length === 0) {
      return { sql: '', params: [] }
    }

    const sql = 'WHERE ' + keys.map(key => `${key} = ?`).join(' AND ')
    const params = keys.map(key => conditions[key])
    return { sql, params }
  }

  protected buildUpdateClause(data: Partial<T>): { sql: string; params: any[] } {
    const keys = Object.keys(data).filter(key => data[key as keyof T] !== undefined)
    if (keys.length === 0) {
      return { sql: '', params: [] }
    }

    const sql = keys.map(key => `${this.camelToSnake(key)} = ?`).join(', ')
    const params = keys.map(key => data[key as keyof T])
    return { sql, params }
  }

  protected camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
  }

  protected snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  protected transformKeys(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj
    
    const transformed: any = {}
    for (const [key, value] of Object.entries(obj)) {
      transformed[this.snakeToCamel(key)] = value
    }
    return transformed
  }

  protected transformKeysToSnake(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj
    
    const transformed: any = {}
    for (const [key, value] of Object.entries(obj)) {
      transformed[this.camelToSnake(key)] = value
    }
    return transformed
  }

  async findAll(conditions: Record<string, any> = {}): Promise<T[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `SELECT * FROM ${this.tableName} ${whereClause} ORDER BY created_at DESC`
    
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    return rows.map(row => this.transformKeys(row) as T)
  }

  async findById(id: string): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    return this.transformKeys(rows[0]) as T
  }

  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    const id = this.generateId()
    const now = new Date().toISOString()
    
    const fullData = {
      ...data,
      id,
      createdAt: now,
      updatedAt: now
    }

    const transformedData = this.transformKeysToSnake(fullData)
    const keys = Object.keys(transformedData)
    const values = Object.values(transformedData)
    const placeholders = keys.map(() => '?').join(', ')
    
    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)
    
    return fullData as T
  }

  async update(id: string, data: Partial<T>): Promise<T | null> {
    const updateData = {
      ...data,
      updatedAt: new Date().toISOString()
    }

    const { sql: updateClause, params: updateParams } = this.buildUpdateClause(updateData)
    if (!updateClause) return this.findById(id)

    const sql = `UPDATE ${this.tableName} SET ${updateClause} WHERE id = ?`
    const result = await this.executeUpdate(sql, [...updateParams, id])
    
    if (result.affectedRows === 0) return null
    return this.findById(id)
  }

  async delete(id: string): Promise<boolean> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`
    const result = await this.executeUpdate(sql, [id])
    return result.affectedRows > 0
  }

  async count(conditions: Record<string, any> = {}): Promise<number> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`
    
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    return rows[0].count
  }
}
