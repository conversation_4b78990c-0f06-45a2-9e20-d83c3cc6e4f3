import mysql from 'mysql2/promise'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

async function updateUserPermissions() {
  let connection: mysql.Connection | null = null
  
  try {
    console.log('🔄 Connecting to database...')
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'jeweller_user',
      password: process.env.DB_PASSWORD || 'jeweller_password',
      database: process.env.DB_NAME || 'jewellers_db',
      port: parseInt(process.env.DB_PORT || '3306')
    })

    console.log('✅ Connected to database')

    // Update admin user permissions to include categories
    const adminPermissions = JSON.stringify([
      { module: "inventory", actions: ["create", "read", "update", "delete", "export"] },
      { module: "customers", actions: ["create", "read", "update", "delete", "export"] },
      { module: "sales", actions: ["create", "read", "update", "delete", "export"] },
      { module: "purchases", actions: ["create", "read", "update", "delete", "export"] },
      { module: "schemes", actions: ["create", "read", "update", "delete", "export"] },
      { module: "repairs", actions: ["create", "read", "update", "delete", "export"] },
      { module: "categories", actions: ["create", "read", "update", "delete", "export"] },
      { module: "reports", actions: ["read", "export"] },
      { module: "settings", actions: ["read", "update"] },
    ])

    const managerPermissions = JSON.stringify([
      { module: "inventory", actions: ["create", "read", "update", "export"] },
      { module: "customers", actions: ["create", "read", "update", "export"] },
      { module: "sales", actions: ["create", "read", "update", "export"] },
      { module: "schemes", actions: ["create", "read", "update"] },
      { module: "repairs", actions: ["create", "read", "update"] },
      { module: "categories", actions: ["create", "read", "update", "export"] },
      { module: "reports", actions: ["read", "export"] },
    ])

    // Update admin user
    await connection.execute(
      'UPDATE users SET permissions = ? WHERE email = ?',
      [adminPermissions, '<EMAIL>']
    )
    console.log('✅ Updated admin user permissions')

    // Update manager user
    await connection.execute(
      'UPDATE users SET permissions = ? WHERE email = ?',
      [managerPermissions, '<EMAIL>']
    )
    console.log('✅ Updated manager user permissions')

    console.log('🎉 Permissions updated successfully!')
    console.log('\nUsers now have access to Categories module:')
    console.log('- Admin: Full access (create, read, update, delete, export)')
    console.log('- Manager: Most access (create, read, update, export)')

  } catch (error) {
    console.error('❌ Error updating permissions:', error)
    return false
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }

  return true
}

async function main() {
  const success = await updateUserPermissions()
  process.exit(success ? 0 : 1)
}

main()
