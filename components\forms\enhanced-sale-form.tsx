"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useStore } from "@/lib/store"
import type { Sale, InventoryItem, SaleItem, ExchangeTransaction, ExchangeItem } from "@/lib/types"
import { Plus, Trash2, Scale, Calculator, X } from "lucide-react"
import { formatCurrency } from "@/lib/utils"

interface EnhancedSaleFormProps {
  sale?: Sale
  onSubmit: () => void
  onCancel: () => void
}

interface ExchangeItemSelection {
  exchangeTransaction: ExchangeTransaction
  exchangeItem: ExchangeItem
  deductionAmount: number
  appliedRate: number
}

export function EnhancedSaleForm({ sale, onSubmit, onCancel }: EnhancedSaleFormProps) {
  const { addSale, updateSale, customers, inventory, settings, getMetalRate } = useStore()
  const [formData, setFormData] = useState({
    customerId: "",
    date: "",
    status: "paid",
  })
  const [saleItems, setSaleItems] = useState<SaleItem[]>([])
  const [exchangeSelections, setExchangeSelections] = useState<ExchangeItemSelection[]>([])
  const [availableExchanges, setAvailableExchanges] = useState<ExchangeTransaction[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isExchangeDialogOpen, setIsExchangeDialogOpen] = useState(false)

  // Mock exchange data - replace with actual API calls
  useEffect(() => {
    const mockExchanges: ExchangeTransaction[] = [
      {
        id: 'exg_001',
        transactionNumber: 'EXG-20240131-001',
        customerId: 'cust_001',
        customer: {
          id: 'cust_001',
          name: 'Rajesh Kumar',
          phone: '9876543210',
          email: '<EMAIL>',
          address: '123 Main Street, Mumbai',
          totalPurchases: 150000,
          lastVisit: '2024-01-31',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-31'
        },
        transactionDate: '2024-01-31',
        totalAmount: 33000,
        paymentMethod: 'cash',
        status: 'completed',
        items: [
          {
            id: 'item_001',
            transactionId: 'exg_001',
            itemDescription: 'GOLD OLD BAR',
            metalType: 'gold',
            purity: '22K',
            grossWeight: 10.000,
            stoneWeight: 2.500,
            netWeight: 7.500,
            ratePerGram: 4400,
            amount: 33000,
            itemCondition: 'good',
            createdAt: '2024-01-31',
            updatedAt: '2024-01-31'
          }
        ],
        createdAt: '2024-01-31',
        updatedAt: '2024-01-31'
      }
    ]
    setAvailableExchanges(mockExchanges)
  }, [])

  useEffect(() => {
    if (sale) {
      setFormData({
        customerId: sale.customer.id,
        date: sale.date,
        status: sale.status,
      })
      setSaleItems(sale.items)
      // Load exchange items if any
      if (sale.exchangeItems) {
        // Convert to exchange selections format
      }
    } else {
      const today = new Date().toISOString().split("T")[0]
      setFormData((prev) => ({ ...prev, date: today }))
    }
  }, [sale])

  const addSaleItem = () => {
    const newItem: SaleItem = {
      id: `item_${Date.now()}`,
      item: {} as InventoryItem,
      grossWeight: 0,
      stoneWeight: 0,
      netWeight: 0,
      rate: 0,
      makingCharges: 0,
      stoneAmount: 0,
      amount: 0,
    }
    setSaleItems([...saleItems, newItem])
  }

  const removeSaleItem = (index: number) => {
    setSaleItems(saleItems.filter((_, i) => i !== index))
  }

  const updateSaleItem = (index: number, field: string, value: any) => {
    const updatedItems = [...saleItems]
    const item = updatedItems[index]

    if (field === "itemId") {
      const selectedItem = inventory.find((inv) => inv.id === value)
      if (selectedItem) {
        item.item = selectedItem
        item.grossWeight = selectedItem.grossWeight
        item.stoneWeight = selectedItem.stoneWeight
        item.netWeight = selectedItem.netWeight
        item.rate = getMetalRate(selectedItem.metalType, selectedItem.purity)
        item.makingCharges = selectedItem.makingCharges
        item.stoneAmount = selectedItem.stoneAmount
      }
    } else {
      ;(item as any)[field] = value
    }

    // Recalculate amount
    if (field === "grossWeight" || field === "stoneWeight") {
      item.netWeight = Math.max(0, item.grossWeight - item.stoneWeight)
    }

    item.amount = item.netWeight * item.rate + item.makingCharges + item.stoneAmount

    setSaleItems(updatedItems)
  }

  const addExchangeItem = (exchangeTransaction: ExchangeTransaction, exchangeItem: ExchangeItem) => {
    const selection: ExchangeItemSelection = {
      exchangeTransaction,
      exchangeItem,
      deductionAmount: exchangeItem.amount,
      appliedRate: exchangeItem.ratePerGram
    }
    setExchangeSelections([...exchangeSelections, selection])
    setIsExchangeDialogOpen(false)
  }

  const removeExchangeItem = (index: number) => {
    setExchangeSelections(exchangeSelections.filter((_, i) => i !== index))
  }

  const updateExchangeDeduction = (index: number, amount: number) => {
    const updated = [...exchangeSelections]
    updated[index].deductionAmount = Math.min(amount, updated[index].exchangeItem.amount)
    setExchangeSelections(updated)
  }

  const calculateTotals = () => {
    const subtotal = saleItems.reduce((sum, item) => sum + item.amount, 0)
    const cgst = (subtotal * parseFloat(settings.cgstRate)) / 100
    const sgst = (subtotal * parseFloat(settings.sgstRate)) / 100
    const total = subtotal + cgst + sgst
    const exchangeDeduction = exchangeSelections.reduce((sum, sel) => sum + sel.deductionAmount, 0)
    const finalTotal = Math.max(0, total - exchangeDeduction)

    return { subtotal, cgst, sgst, total, exchangeDeduction, finalTotal }
  }

  const getCustomerExchanges = () => {
    if (!formData.customerId) return []
    return availableExchanges.filter(ex => 
      ex.customerId === formData.customerId && 
      ex.status === 'completed' &&
      !ex.purchaseBillGenerated // Only show exchanges that haven't been used
    )
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.customerId) {
      newErrors.customerId = "Customer is required"
    }

    if (!formData.date) {
      newErrors.date = "Date is required"
    }

    if (saleItems.length === 0) {
      newErrors.items = "At least one sale item is required"
    }

    saleItems.forEach((item, index) => {
      if (!item.item.id) {
        newErrors[`item_${index}`] = "Please select an item"
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const customer = customers.find((c) => c.id === formData.customerId)
      if (!customer) {
        setErrors({ submit: "Selected customer not found" })
        return
      }

      const { subtotal, cgst, sgst, total, exchangeDeduction, finalTotal } = calculateTotals()

      const saleData = {
        customer,
        items: saleItems,
        subtotal,
        cgst,
        sgst,
        total,
        status: formData.status as "draft" | "paid" | "pending" | "cancelled",
        date: formData.date,
        exchangeDeduction,
        finalTotal,
        exchangeItems: exchangeSelections.map(sel => ({
          id: `sel_${Date.now()}_${Math.random()}`,
          saleId: '', // Will be set after sale creation
          exchangeTransactionId: sel.exchangeTransaction.id,
          exchangeItemId: sel.exchangeItem.id,
          exchangeItem: sel.exchangeItem,
          deductionAmount: sel.deductionAmount,
          appliedRate: sel.appliedRate,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }

      if (sale) {
        updateSale(sale.id, saleData)
      } else {
        await addSale(saleData)
      }

      // Reset form
      setFormData({
        customerId: "",
        date: "",
        status: "paid",
      })
      setSaleItems([])
      setExchangeSelections([])
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving sale:", error)
      setErrors({ submit: "Failed to save sale. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const { subtotal, cgst, sgst, total, exchangeDeduction, finalTotal } = calculateTotals()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">{sale ? "Edit Sale" : "New Sale"}</h2>
        <Button variant="ghost" onClick={onCancel}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer and Date */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="customer">Customer *</Label>
            <Select
              value={formData.customerId}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, customerId: value }))}
            >
              <SelectTrigger className={errors.customerId ? "border-red-500" : ""}>
                <SelectValue placeholder="Select customer" />
              </SelectTrigger>
              <SelectContent>
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id}>
                    {customer.name} - {customer.phone}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.customerId && <p className="text-sm text-red-500">{errors.customerId}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="date">Date *</Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => setFormData((prev) => ({ ...prev, date: e.target.value }))}
              className={errors.date ? "border-red-500" : ""}
            />
            {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="items" className="space-y-4">
          <TabsList>
            <TabsTrigger value="items">Sale Items</TabsTrigger>
            <TabsTrigger value="exchange">Exchange Items ({exchangeSelections.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="items">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Sale Items</CardTitle>
                <Button type="button" onClick={addSaleItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {errors.items && <p className="text-sm text-red-500">{errors.items}</p>}

                {saleItems.map((saleItem, index) => (
                  <Card key={saleItem.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeSaleItem(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label>Item *</Label>
                        <Select
                          value={saleItem.item.id || ""}
                          onValueChange={(value) => updateSaleItem(index, "itemId", value)}
                        >
                          <SelectTrigger className={errors[`item_${index}`] ? "border-red-500" : ""}>
                            <SelectValue placeholder="Select item" />
                          </SelectTrigger>
                          <SelectContent>
                            {inventory
                              .filter((item) => item.stock > 0)
                              .map((item) => (
                                <SelectItem key={item.id} value={item.id}>
                                  {item.name} - {item.category}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        {errors[`item_${index}`] && (
                          <p className="text-sm text-red-500">{errors[`item_${index}`]}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label>Net Weight (g)</Label>
                        <Input
                          type="number"
                          step="0.001"
                          value={saleItem.netWeight}
                          onChange={(e) => updateSaleItem(index, "netWeight", parseFloat(e.target.value) || 0)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Rate/g</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={saleItem.rate}
                          onChange={(e) => updateSaleItem(index, "rate", parseFloat(e.target.value) || 0)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Amount</Label>
                        <Input
                          type="text"
                          value={formatCurrency(saleItem.amount)}
                          readOnly
                          className="bg-muted"
                        />
                      </div>
                    </div>
                  </Card>
                ))}

                {saleItems.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No items added yet. Click "Add Item" to get started.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="exchange">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Scale className="h-5 w-5" />
                  Exchange Items
                </CardTitle>
                <Button 
                  type="button" 
                  onClick={() => setIsExchangeDialogOpen(true)} 
                  size="sm"
                  disabled={!formData.customerId}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Exchange
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {!formData.customerId && (
                  <p className="text-sm text-muted-foreground">
                    Please select a customer first to view available exchange items.
                  </p>
                )}

                {exchangeSelections.map((selection, index) => (
                  <Card key={index} className="p-4 border-orange-200">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-medium">{selection.exchangeItem.itemDescription}</h4>
                        <p className="text-sm text-muted-foreground">
                          {selection.exchangeTransaction.transactionNumber} • {selection.exchangeItem.metalType} {selection.exchangeItem.purity}
                        </p>
                      </div>
                      <Button type="button" variant="ghost" size="sm" onClick={() => removeExchangeItem(index)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label className="text-xs">Net Weight</Label>
                        <p className="font-medium">{selection.exchangeItem.netWeight.toFixed(3)}g</p>
                      </div>
                      <div>
                        <Label className="text-xs">Exchange Value</Label>
                        <p className="font-medium">{formatCurrency(selection.exchangeItem.amount)}</p>
                      </div>
                      <div>
                        <Label className="text-xs">Deduction Amount</Label>
                        <Input
                          type="number"
                          step="0.01"
                          max={selection.exchangeItem.amount}
                          value={selection.deductionAmount}
                          onChange={(e) => updateExchangeDeduction(index, parseFloat(e.target.value) || 0)}
                          className="h-8"
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Status</Label>
                        <Badge variant="secondary" className="text-xs">
                          Applied
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}

                {exchangeSelections.length === 0 && formData.customerId && (
                  <div className="text-center py-8 text-muted-foreground">
                    No exchange items added yet. Click "Add Exchange" to include exchange items.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Totals Summary */}
        <Card className="bg-muted/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                <span className="font-semibold">Bill Summary</span>
              </div>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>{formatCurrency(subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>CGST ({settings.cgstRate}%):</span>
                <span>{formatCurrency(cgst)}</span>
              </div>
              <div className="flex justify-between">
                <span>SGST ({settings.sgstRate}%):</span>
                <span>{formatCurrency(sgst)}</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Total:</span>
                <span>{formatCurrency(total)}</span>
              </div>
              {exchangeDeduction > 0 && (
                <>
                  <div className="flex justify-between text-orange-600">
                    <span>Exchange Deduction:</span>
                    <span>-{formatCurrency(exchangeDeduction)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between text-lg font-bold text-primary">
                    <span>Final Amount:</span>
                    <span>{formatCurrency(finalTotal)}</span>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : (sale ? "Update Sale" : "Create Sale")}
          </Button>
        </div>

        {errors.submit && (
          <p className="text-sm text-red-500 text-center">{errors.submit}</p>
        )}
      </form>

      {/* Exchange Selection Dialog */}
      {isExchangeDialogOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-4xl max-h-[80vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Select Exchange Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getCustomerExchanges().map((exchange) => (
                  <Card key={exchange.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-medium">{exchange.transactionNumber}</h4>
                        <p className="text-sm text-muted-foreground">
                          {exchange.transactionDate} • {formatCurrency(exchange.totalAmount)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      {exchange.items.map((item) => (
                        <div key={item.id} className="flex justify-between items-center p-2 border rounded">
                          <div>
                            <p className="font-medium">{item.itemDescription}</p>
                            <p className="text-sm text-muted-foreground">
                              {item.metalType} {item.purity} • {item.netWeight.toFixed(3)}g • {formatCurrency(item.amount)}
                            </p>
                          </div>
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => addExchangeItem(exchange, item)}
                            disabled={exchangeSelections.some(sel => sel.exchangeItem.id === item.id)}
                          >
                            {exchangeSelections.some(sel => sel.exchangeItem.id === item.id) ? 'Added' : 'Add'}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </Card>
                ))}
                
                {getCustomerExchanges().length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No available exchange items for this customer.
                  </div>
                )}
              </div>
              
              <div className="flex justify-end gap-2 mt-6">
                <Button type="button" variant="outline" onClick={() => setIsExchangeDialogOpen(false)}>
                  Close
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
