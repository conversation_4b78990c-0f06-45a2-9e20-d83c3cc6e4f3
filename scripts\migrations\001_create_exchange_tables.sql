-- Exchange System Database Migration Script
-- Version: 1.0.0
-- Description: Creates all tables required for the Exchange Billing Integration System

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Create exchange_rates table
CREATE TABLE IF NOT EXISTS exchange_rates (
    id VARCHAR(36) PRIMARY KEY,
    metal_type ENUM('gold', 'silver') NOT NULL,
    purity VARCHAR(10) NOT NULL,
    rate_per_gram DECIMAL(10, 2) NOT NULL,
    effective_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_active_rate (metal_type, purity, is_active),
    INDEX idx_metal_purity (metal_type, purity),
    INDEX idx_effective_date (effective_date)
);

-- Create exchange_transactions table
CREATE TABLE IF NOT EXISTS exchange_transactions (
    id VARCHAR(36) PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(36),
    transaction_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
    notes TEXT,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    purchase_bill_generated BOOLEAN DEFAULT FALSE,
    purchase_bill_id VARCHAR(36),
    purchase_bill_number VARCHAR(50),
    purchase_bill_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer (customer_id),
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_status (status),
    INDEX idx_transaction_number (transaction_number),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Create exchange_items table
CREATE TABLE IF NOT EXISTS exchange_items (
    id VARCHAR(36) PRIMARY KEY,
    transaction_id VARCHAR(36) NOT NULL,
    item_description VARCHAR(255) NOT NULL,
    metal_type ENUM('gold', 'silver') NOT NULL,
    purity VARCHAR(10) NOT NULL,
    gross_weight DECIMAL(8, 3) NOT NULL,
    stone_weight DECIMAL(8, 3) DEFAULT 0.000,
    net_weight DECIMAL(8, 3) NOT NULL,
    rate_per_gram DECIMAL(10, 2) NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    item_condition ENUM('good', 'fair', 'poor') DEFAULT 'good',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_transaction (transaction_id),
    INDEX idx_metal_type (metal_type),
    INDEX idx_purity (purity),
    FOREIGN KEY (transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE
);

-- Create bill_sequences table for sequential numbering
CREATE TABLE IF NOT EXISTS bill_sequences (
    id VARCHAR(36) PRIMARY KEY,
    sequence_type VARCHAR(50) NOT NULL,
    prefix VARCHAR(10) NOT NULL,
    current_number INT NOT NULL DEFAULT 0,
    financial_year VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_sequence (sequence_type, financial_year)
);

-- Create exchange_purchase_bills table
CREATE TABLE IF NOT EXISTS exchange_purchase_bills (
    id VARCHAR(36) PRIMARY KEY,
    bill_number VARCHAR(50) UNIQUE NOT NULL,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    customer_id VARCHAR(36),
    bill_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL,
    cgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    sgst_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    total_with_tax DECIMAL(12, 2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'adjustment') DEFAULT 'cash',
    payment_status ENUM('pending', 'paid', 'partial') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_bill_number (bill_number),
    INDEX idx_exchange_transaction (exchange_transaction_id),
    INDEX idx_customer (customer_id),
    INDEX idx_bill_date (bill_date),
    INDEX idx_payment_status (payment_status),
    FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Create sales_exchange_items table for linking sales with exchanges
CREATE TABLE IF NOT EXISTS sales_exchange_items (
    id VARCHAR(36) PRIMARY KEY,
    sale_id VARCHAR(36) NOT NULL,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    exchange_item_id VARCHAR(36) NOT NULL,
    deduction_amount DECIMAL(12, 2) NOT NULL,
    applied_rate DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sale (sale_id),
    INDEX idx_exchange_transaction (exchange_transaction_id),
    INDEX idx_exchange_item (exchange_item_id),
    FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (exchange_item_id) REFERENCES exchange_items(id) ON DELETE CASCADE
);

-- Create exchange_vouchers table for compliance
CREATE TABLE IF NOT EXISTS exchange_vouchers (
    id VARCHAR(36) PRIMARY KEY,
    voucher_number VARCHAR(50) UNIQUE NOT NULL,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    voucher_type ENUM('purchase', 'adjustment', 'refund') DEFAULT 'purchase',
    voucher_date DATE NOT NULL,
    amount DECIMAL(12, 2) NOT NULL,
    description TEXT,
    status ENUM('active', 'used', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_voucher_number (voucher_number),
    INDEX idx_exchange_transaction (exchange_transaction_id),
    INDEX idx_voucher_date (voucher_date),
    INDEX idx_status (status),
    FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE
);

-- Create exchange_audit_trail table for complete audit history
CREATE TABLE IF NOT EXISTS exchange_audit_trail (
    id VARCHAR(36) PRIMARY KEY,
    exchange_transaction_id VARCHAR(36) NOT NULL,
    action_type ENUM('created', 'updated', 'billed', 'voucher_generated', 'used_in_sale', 'cancelled') NOT NULL,
    action_description TEXT NOT NULL,
    old_values JSON,
    new_values JSON,
    related_bill_id VARCHAR(36),
    related_sale_id VARCHAR(36),
    performed_by VARCHAR(36),
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_exchange_transaction (exchange_transaction_id),
    INDEX idx_action_type (action_type),
    INDEX idx_performed_at (performed_at),
    INDEX idx_performed_by (performed_by),
    FOREIGN KEY (exchange_transaction_id) REFERENCES exchange_transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (related_bill_id) REFERENCES exchange_purchase_bills(id) ON DELETE SET NULL,
    FOREIGN KEY (related_sale_id) REFERENCES sales(id) ON DELETE SET NULL
);

-- Create exchange_rate_history table for rate tracking
CREATE TABLE IF NOT EXISTS exchange_rate_history (
    id VARCHAR(36) PRIMARY KEY,
    metal_type ENUM('gold', 'silver') NOT NULL,
    purity VARCHAR(10) NOT NULL,
    old_rate DECIMAL(10, 2),
    new_rate DECIMAL(10, 2) NOT NULL,
    change_reason VARCHAR(255),
    effective_date DATE NOT NULL,
    changed_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metal_purity (metal_type, purity),
    INDEX idx_effective_date (effective_date),
    INDEX idx_changed_by (changed_by)
);

-- Add foreign key constraint to link purchase bills back to transactions
ALTER TABLE exchange_transactions 
ADD CONSTRAINT fk_exchange_purchase_bill 
FOREIGN KEY (purchase_bill_id) REFERENCES exchange_purchase_bills(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX idx_exchange_transactions_composite ON exchange_transactions(status, transaction_date, customer_id);
CREATE INDEX idx_exchange_items_composite ON exchange_items(transaction_id, metal_type, purity);
CREATE INDEX idx_purchase_bills_composite ON exchange_purchase_bills(bill_date, payment_status, customer_id);
CREATE INDEX idx_sales_exchange_composite ON sales_exchange_items(sale_id, exchange_transaction_id);

-- Insert default exchange rates
INSERT IGNORE INTO exchange_rates (id, metal_type, purity, rate_per_gram, effective_date, is_active) VALUES
(UUID(), 'gold', '24K', 6800.00, CURDATE(), TRUE),
(UUID(), 'gold', '22K', 6200.00, CURDATE(), TRUE),
(UUID(), 'gold', '18K', 5100.00, CURDATE(), TRUE),
(UUID(), 'gold', '14K', 3950.00, CURDATE(), TRUE),
(UUID(), 'silver', '999', 85.00, CURDATE(), TRUE),
(UUID(), 'silver', '925', 78.00, CURDATE(), TRUE),
(UUID(), 'silver', '900', 76.00, CURDATE(), TRUE);

-- Insert initial bill sequence for current financial year
INSERT IGNORE INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year) VALUES
(UUID(), 'exchange_purchase', 'EPB', 0, CONCAT(YEAR(CURDATE()), '-', RIGHT(YEAR(CURDATE()) + 1, 2)));

COMMIT;

-- Display success message
SELECT 'Exchange System Database Migration Completed Successfully!' as Status;
SELECT 'Tables Created:' as Info, 
       'exchange_rates, exchange_transactions, exchange_items, exchange_purchase_bills, sales_exchange_items, exchange_vouchers, exchange_audit_trail, exchange_rate_history, bill_sequences' as Tables;
SELECT 'Default Data Inserted:' as Info,
       'Exchange rates for Gold (24K, 22K, 18K, 14K) and Silver (999, 925, 900), Bill sequence initialized' as Data;
