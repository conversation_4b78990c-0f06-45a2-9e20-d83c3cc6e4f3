// Import service classes
import { UserService } from './user-service'
import { CustomerService } from './customer-service'
import { InventoryService } from './inventory-service'
import { SalesService } from './sales-service'
import { SchemeService } from './scheme-service'
import { RepairService } from './repair-service'
import { PurchaseService } from './purchase-service'
import { CategoryService } from './category-service'
import { SettingsService } from './settings-service'

// Export service classes
export { UserService } from './user-service'
export { CustomerService } from './customer-service'
export { InventoryService } from './inventory-service'
export { SalesService } from './sales-service'
export { SchemeService } from './scheme-service'
export { RepairService } from './repair-service'
export { PurchaseService } from './purchase-service'
export { CategoryService } from './category-service'
export { SettingsService } from './settings-service'

// Create singleton instances
export const userService = new UserService()
export const customerService = new CustomerService()
export const inventoryService = new InventoryService()
export const salesService = new SalesService()
export const schemeService = new SchemeService()
export const repairService = new RepairService()
export const purchaseService = new PurchaseService()
export const categoryService = new CategoryService()
export const settingsService = new SettingsService()
