// Validation middleware for API endpoints
// Provides server-side validation for all exchange operations

import { NextRequest, NextResponse } from 'next/server'
import { ExchangeValidator, ValidationResult } from '../validation/exchange-validation'

export interface ValidationError {
  field: string
  message: string
  code: string
}

export interface ValidationResponse {
  success: boolean
  errors: ValidationError[]
  warnings: string[]
  data?: any
}

export class ValidationMiddleware {
  
  // Create standardized validation error response
  static createErrorResponse(
    message: string, 
    errors: string[] = [], 
    warnings: string[] = [],
    statusCode: number = 400
  ): NextResponse {
    const response: ValidationResponse = {
      success: false,
      errors: errors.map(error => ({
        field: 'general',
        message: error,
        code: 'VALIDATION_ERROR'
      })),
      warnings
    }

    return NextResponse.json(response, { status: statusCode })
  }

  // Create success response with warnings
  static createSuccessResponse(data: any, warnings: string[] = []): NextResponse {
    const response: ValidationResponse = {
      success: true,
      errors: [],
      warnings,
      data
    }

    return NextResponse.json(response)
  }

  // Validate exchange transaction request
  static async validateExchangeTransactionRequest(request: NextRequest): Promise<{
    isValid: boolean
    data?: any
    response?: NextResponse
  }> {
    try {
      const body = await request.json()
      
      // Basic structure validation
      if (!body) {
        return {
          isValid: false,
          response: this.createErrorResponse('Request body is required')
        }
      }

      // Required fields check
      const requiredFields = ['transactionNumber', 'customerId', 'transactionDate', 'items']
      const missingFields = requiredFields.filter(field => !body[field])
      
      if (missingFields.length > 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Missing required fields',
            missingFields.map(field => `${field} is required`)
          )
        }
      }

      // Validate using ExchangeValidator
      const validation = ExchangeValidator.validateExchangeTransaction(body)
      
      if (!validation.isValid) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            validation.errors,
            validation.warnings
          )
        }
      }

      return {
        isValid: true,
        data: body
      }

    } catch (error) {
      return {
        isValid: false,
        response: this.createErrorResponse(
          'Invalid JSON in request body',
          [error instanceof Error ? error.message : 'Unknown error']
        )
      }
    }
  }

  // Validate exchange item request
  static async validateExchangeItemRequest(request: NextRequest): Promise<{
    isValid: boolean
    data?: any
    response?: NextResponse
  }> {
    try {
      const body = await request.json()
      
      if (!body) {
        return {
          isValid: false,
          response: this.createErrorResponse('Request body is required')
        }
      }

      // Required fields for exchange item
      const requiredFields = ['itemDescription', 'metalType', 'purity', 'grossWeight', 'netWeight', 'ratePerGram', 'amount']
      const missingFields = requiredFields.filter(field => body[field] === undefined || body[field] === null)
      
      if (missingFields.length > 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Missing required fields',
            missingFields.map(field => `${field} is required`)
          )
        }
      }

      // Validate using ExchangeValidator
      const validation = ExchangeValidator.validateExchangeItem(body)
      
      if (!validation.isValid) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            validation.errors,
            validation.warnings
          )
        }
      }

      return {
        isValid: true,
        data: body
      }

    } catch (error) {
      return {
        isValid: false,
        response: this.createErrorResponse(
          'Invalid JSON in request body',
          [error instanceof Error ? error.message : 'Unknown error']
        )
      }
    }
  }

  // Validate purchase bill request
  static async validatePurchaseBillRequest(request: NextRequest): Promise<{
    isValid: boolean
    data?: any
    response?: NextResponse
  }> {
    try {
      const body = await request.json()
      
      if (!body) {
        return {
          isValid: false,
          response: this.createErrorResponse('Request body is required')
        }
      }

      // Required fields for purchase bill
      const requiredFields = ['exchangeTransactionId', 'customerId', 'billDate', 'totalAmount']
      const missingFields = requiredFields.filter(field => !body[field])
      
      if (missingFields.length > 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Missing required fields',
            missingFields.map(field => `${field} is required`)
          )
        }
      }

      // Set default values for optional fields
      body.cgstAmount = body.cgstAmount || 0
      body.sgstAmount = body.sgstAmount || 0
      body.totalWithTax = body.totalWithTax || (body.totalAmount + body.cgstAmount + body.sgstAmount)
      body.paymentMethod = body.paymentMethod || 'cash'

      // Validate using ExchangeValidator
      const validation = ExchangeValidator.validatePurchaseBill(body)
      
      if (!validation.isValid) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            validation.errors,
            validation.warnings
          )
        }
      }

      return {
        isValid: true,
        data: body
      }

    } catch (error) {
      return {
        isValid: false,
        response: this.createErrorResponse(
          'Invalid JSON in request body',
          [error instanceof Error ? error.message : 'Unknown error']
        )
      }
    }
  }

  // Validate exchange rate request
  static async validateExchangeRateRequest(request: NextRequest): Promise<{
    isValid: boolean
    data?: any
    response?: NextResponse
  }> {
    try {
      const body = await request.json()
      
      if (!body) {
        return {
          isValid: false,
          response: this.createErrorResponse('Request body is required')
        }
      }

      // Required fields for exchange rate
      const requiredFields = ['metalType', 'purity', 'ratePerGram', 'effectiveDate']
      const missingFields = requiredFields.filter(field => !body[field])
      
      if (missingFields.length > 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Missing required fields',
            missingFields.map(field => `${field} is required`)
          )
        }
      }

      // Validate using ExchangeValidator
      const validation = ExchangeValidator.validateExchangeRate(body)
      
      if (!validation.isValid) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            validation.errors,
            validation.warnings
          )
        }
      }

      return {
        isValid: true,
        data: body
      }

    } catch (error) {
      return {
        isValid: false,
        response: this.createErrorResponse(
          'Invalid JSON in request body',
          [error instanceof Error ? error.message : 'Unknown error']
        )
      }
    }
  }

  // Validate sales exchange deduction request
  static async validateSalesExchangeRequest(request: NextRequest): Promise<{
    isValid: boolean
    data?: any
    response?: NextResponse
  }> {
    try {
      const body = await request.json()
      
      if (!body) {
        return {
          isValid: false,
          response: this.createErrorResponse('Request body is required')
        }
      }

      // Required fields for sales exchange
      const requiredFields = ['saleId', 'exchangeTransactionId', 'exchangeItemId', 'deductionAmount', 'appliedRate']
      const missingFields = requiredFields.filter(field => !body[field])
      
      if (missingFields.length > 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Missing required fields',
            missingFields.map(field => `${field} is required`)
          )
        }
      }

      // Additional validation for sales exchange
      if (body.deductionAmount < 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            ['Deduction amount cannot be negative']
          )
        }
      }

      if (body.appliedRate <= 0) {
        return {
          isValid: false,
          response: this.createErrorResponse(
            'Validation failed',
            ['Applied rate must be greater than zero']
          )
        }
      }

      return {
        isValid: true,
        data: body
      }

    } catch (error) {
      return {
        isValid: false,
        response: this.createErrorResponse(
          'Invalid JSON in request body',
          [error instanceof Error ? error.message : 'Unknown error']
        )
      }
    }
  }

  // Validate ID parameter
  static validateIdParameter(id: string | null, paramName: string = 'id'): {
    isValid: boolean
    response?: NextResponse
  } {
    if (!id) {
      return {
        isValid: false,
        response: this.createErrorResponse(`${paramName} parameter is required`)
      }
    }

    // Basic UUID format validation
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidPattern.test(id)) {
      return {
        isValid: false,
        response: this.createErrorResponse(`Invalid ${paramName} format`)
      }
    }

    return { isValid: true }
  }

  // Validate pagination parameters
  static validatePaginationParameters(searchParams: URLSearchParams): {
    isValid: boolean
    page: number
    limit: number
    response?: NextResponse
  } {
    const pageParam = searchParams.get('page')
    const limitParam = searchParams.get('limit')

    let page = 1
    let limit = 10

    if (pageParam) {
      page = parseInt(pageParam, 10)
      if (isNaN(page) || page < 1) {
        return {
          isValid: false,
          page: 1,
          limit: 10,
          response: this.createErrorResponse('Page must be a positive integer')
        }
      }
    }

    if (limitParam) {
      limit = parseInt(limitParam, 10)
      if (isNaN(limit) || limit < 1 || limit > 100) {
        return {
          isValid: false,
          page,
          limit: 10,
          response: this.createErrorResponse('Limit must be between 1 and 100')
        }
      }
    }

    return {
      isValid: true,
      page,
      limit
    }
  }

  // Validate date range parameters
  static validateDateRangeParameters(searchParams: URLSearchParams): {
    isValid: boolean
    startDate?: Date
    endDate?: Date
    response?: NextResponse
  } {
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')

    let startDate: Date | undefined
    let endDate: Date | undefined

    if (startDateParam) {
      startDate = new Date(startDateParam)
      if (isNaN(startDate.getTime())) {
        return {
          isValid: false,
          response: this.createErrorResponse('Invalid start date format')
        }
      }
    }

    if (endDateParam) {
      endDate = new Date(endDateParam)
      if (isNaN(endDate.getTime())) {
        return {
          isValid: false,
          response: this.createErrorResponse('Invalid end date format')
        }
      }
    }

    if (startDate && endDate && startDate > endDate) {
      return {
        isValid: false,
        response: this.createErrorResponse('Start date cannot be after end date')
      }
    }

    return {
      isValid: true,
      startDate,
      endDate
    }
  }

  // Generic validation wrapper for API routes
  static withValidation<T>(
    validationFn: (request: NextRequest) => Promise<{
      isValid: boolean
      data?: T
      response?: NextResponse
    }>,
    handlerFn: (data: T, request: NextRequest) => Promise<NextResponse>
  ) {
    return async (request: NextRequest): Promise<NextResponse> => {
      try {
        const validation = await validationFn(request)
        
        if (!validation.isValid) {
          return validation.response || this.createErrorResponse('Validation failed')
        }

        return await handlerFn(validation.data!, request)
        
      } catch (error) {
        console.error('Validation middleware error:', error)
        return this.createErrorResponse(
          'Internal server error',
          [error instanceof Error ? error.message : 'Unknown error'],
          [],
          500
        )
      }
    }
  }
}
