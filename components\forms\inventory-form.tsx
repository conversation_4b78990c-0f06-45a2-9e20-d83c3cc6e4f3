"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useStore } from "@/lib/store"
import type { InventoryItem } from "@/lib/types"
import { generateJewelryItemId, getHSNCode } from "@/lib/utils/jewelry-id-generator"

interface InventoryFormProps {
  item?: InventoryItem
  onSubmit: () => void
  onCancel: () => void
}

export function InventoryForm({ item, onSubmit, onCancel }: InventoryFormProps) {
  const { addInventoryItem, updateInventoryItem, getMetalRate } = useStore()
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    metalType: "",
    grossWeight: "",
    stoneWeight: "",
    netWeight: "",
    stoneAmount: "",
    purity: "",
    makingCharges: "",
    stock: "",
    stoneDetails: "",
    description: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [estimatedValue, setEstimatedValue] = useState(0)

  useEffect(() => {
    if (item) {
      console.log('🔍 Edit form received item:', item)
      setFormData({
        name: item.name || "",
        category: item.category || "",
        metalType: item.metalType || "",
        grossWeight: item.grossWeight?.toString() || "",
        stoneWeight: item.stoneWeight?.toString() || "",
        netWeight: item.netWeight?.toString() || "",
        stoneAmount: item.stoneAmount?.toString() || "",
        purity: item.purity || "",
        makingCharges: item.makingCharges?.toString() || "",
        stock: item.stock?.toString() || "",
        stoneDetails: item.stoneDetails || "",
        description: item.description || "",
      })
    }
  }, [item])

  // Calculate net weight when gross weight or stone weight changes
  useEffect(() => {
    const gross = Number.parseFloat(formData.grossWeight) || 0
    const stone = Number.parseFloat(formData.stoneWeight) || 0
    const net = Math.max(0, gross - stone)

    if (net !== Number.parseFloat(formData.netWeight)) {
      setFormData((prev) => ({ ...prev, netWeight: net.toString() }))
    }
  }, [formData.grossWeight, formData.stoneWeight])

  // Calculate estimated value
  useEffect(() => {
    const netWeight = Number.parseFloat(formData.netWeight) || 0
    const stoneAmount = Number.parseFloat(formData.stoneAmount) || 0
    const makingCharges = Number.parseFloat(formData.makingCharges) || 0
    const metalRate = getMetalRate(formData.metalType, formData.purity)

    const metalValue = netWeight * metalRate
    const totalValue = metalValue + stoneAmount + makingCharges

    setEstimatedValue(totalValue)
  }, [
    formData.netWeight,
    formData.stoneAmount,
    formData.makingCharges,
    formData.metalType,
    formData.purity,
    getMetalRate,
  ])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Item name is required"
    }

    if (!formData.category) {
      newErrors.category = "Category is required"
    }

    if (!formData.metalType) {
      newErrors.metalType = "Metal type is required"
    }

    if (!formData.purity) {
      newErrors.purity = "Purity is required"
    }

    if (!formData.grossWeight || Number.parseFloat(formData.grossWeight) <= 0) {
      newErrors.grossWeight = "Gross weight must be greater than 0"
    }

    if (Number.parseFloat(formData.stoneWeight) < 0) {
      newErrors.stoneWeight = "Stone weight cannot be negative"
    }

    if (Number.parseFloat(formData.stoneWeight) >= Number.parseFloat(formData.grossWeight)) {
      newErrors.stoneWeight = "Stone weight must be less than gross weight"
    }

    if (!formData.stock || Number.parseInt(formData.stock) < 0) {
      newErrors.stock = "Stock must be 0 or greater"
    }

    if (Number.parseFloat(formData.stoneAmount) < 0) {
      newErrors.stoneAmount = "Stone amount cannot be negative"
    }

    if (Number.parseFloat(formData.makingCharges) < 0) {
      newErrors.makingCharges = "Making charges cannot be negative"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const itemData = {
        name: formData.name.trim(),
        category: formData.category,
        metalType: formData.metalType,
        grossWeight: Number.parseFloat(formData.grossWeight),
        stoneWeight: Number.parseFloat(formData.stoneWeight) || 0,
        netWeight: Number.parseFloat(formData.netWeight),
        stoneAmount: Number.parseFloat(formData.stoneAmount) || 0,
        purity: formData.purity,
        makingCharges: Number.parseFloat(formData.makingCharges) || 0,
        currentValue: estimatedValue,
        stock: Number.parseInt(formData.stock),
        stoneDetails: formData.stoneDetails.trim(),
        description: formData.description.trim(),
      }

      if (item) {
        await updateInventoryItem(item.id, itemData)
      } else {
        await addInventoryItem(itemData)
      }

      // Reset form
      setFormData({
        name: "",
        category: "",
        metalType: "",
        grossWeight: "",
        stoneWeight: "",
        netWeight: "",
        stoneAmount: "",
        purity: "",
        makingCharges: "",
        stock: "",
        stoneDetails: "",
        description: "",
      })
      setErrors({})

      onSubmit()
    } catch (error) {
      console.error("Error saving inventory item:", error)
      setErrors({ submit: "Failed to save item. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const getPurityOptions = () => {
    if (formData.metalType === "gold") {
      return ["22K", "18K"]
    } else if (formData.metalType === "silver") {
      return ["925"]
    }
    return []
  }

  // Generate preview ID and HSN code
  const previewId = formData.category && formData.metalType && formData.purity
    ? generateJewelryItemId(formData.category, formData.metalType, formData.purity)
    : ''

  const previewHSN = formData.category
    ? getHSNCode(formData.category, formData.metalType)
    : ''

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Item Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Gold Ring"
            required
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select value={formData.category || undefined} onValueChange={(value) => handleInputChange("category", value)}>
            <SelectTrigger className={errors.category ? "border-red-500" : ""}>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="rings">Rings</SelectItem>
              <SelectItem value="necklaces">Necklaces</SelectItem>
              <SelectItem value="earrings">Earrings</SelectItem>
              <SelectItem value="bracelets">Bracelets</SelectItem>
              <SelectItem value="pendants">Pendants</SelectItem>
              <SelectItem value="chains">Chains</SelectItem>
              <SelectItem value="bangles">Bangles</SelectItem>
              <SelectItem value="anklets">Anklets</SelectItem>
              <SelectItem value="sets">Sets</SelectItem>
              <SelectItem value="nose-pins">Nose Pins</SelectItem>
              <SelectItem value="toe-rings">Toe Rings</SelectItem>
              <SelectItem value="watches">Watches</SelectItem>
              <SelectItem value="precious-stones">Precious Stones</SelectItem>
              <SelectItem value="pearls">Pearls</SelectItem>
            </SelectContent>
          </Select>
          {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="metalType">Metal Type *</Label>
          <Select
            value={formData.metalType || undefined}
            onValueChange={(value) => {
              handleInputChange("metalType", value)
              setFormData((prev) => ({ ...prev, purity: "" })) // Reset purity when metal type changes
            }}
          >
            <SelectTrigger className={errors.metalType ? "border-red-500" : ""}>
              <SelectValue placeholder="Select metal" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gold">Gold</SelectItem>
              <SelectItem value="silver">Silver</SelectItem>
              <SelectItem value="platinum">Platinum</SelectItem>
            </SelectContent>
          </Select>
          {errors.metalType && <p className="text-sm text-red-500">{errors.metalType}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="purity">Purity *</Label>
          <Select value={formData.purity || undefined} onValueChange={(value) => handleInputChange("purity", value)}>
            <SelectTrigger className={errors.purity ? "border-red-500" : ""}>
              <SelectValue placeholder="Select purity" />
            </SelectTrigger>
            <SelectContent>
              {getPurityOptions().map((purity) => (
                <SelectItem key={purity} value={purity}>
                  {purity}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.purity && <p className="text-sm text-red-500">{errors.purity}</p>}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="grossWeight">Gross Weight (g) *</Label>
          <Input
            id="grossWeight"
            type="number"
            step="0.01"
            min="0"
            value={formData.grossWeight}
            onChange={(e) => handleInputChange("grossWeight", e.target.value)}
            placeholder="25.50"
            required
            className={errors.grossWeight ? "border-red-500" : ""}
          />
          {errors.grossWeight && <p className="text-sm text-red-500">{errors.grossWeight}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="stoneWeight">Stone Weight (g)</Label>
          <Input
            id="stoneWeight"
            type="number"
            step="0.01"
            min="0"
            value={formData.stoneWeight}
            onChange={(e) => handleInputChange("stoneWeight", e.target.value)}
            placeholder="2.50"
            className={errors.stoneWeight ? "border-red-500" : ""}
          />
          {errors.stoneWeight && <p className="text-sm text-red-500">{errors.stoneWeight}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="netWeight">Net Weight (g)</Label>
          <Input
            id="netWeight"
            type="number"
            step="0.01"
            value={formData.netWeight}
            readOnly
            className="bg-gray-50"
            placeholder="Auto-calculated"
          />
          <p className="text-xs text-muted-foreground">Auto-calculated: Gross - Stone</p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="stoneAmount">Stone Amount (₹)</Label>
          <Input
            id="stoneAmount"
            type="number"
            step="0.01"
            min="0"
            value={formData.stoneAmount}
            onChange={(e) => handleInputChange("stoneAmount", e.target.value)}
            placeholder="15000"
            className={errors.stoneAmount ? "border-red-500" : ""}
          />
          {errors.stoneAmount && <p className="text-sm text-red-500">{errors.stoneAmount}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="makingCharges">Making Charges (₹)</Label>
          <Input
            id="makingCharges"
            type="number"
            step="0.01"
            min="0"
            value={formData.makingCharges}
            onChange={(e) => handleInputChange("makingCharges", e.target.value)}
            placeholder="8500"
            className={errors.makingCharges ? "border-red-500" : ""}
          />
          {errors.makingCharges && <p className="text-sm text-red-500">{errors.makingCharges}</p>}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="stock">Stock Quantity *</Label>
          <Input
            id="stock"
            type="number"
            min="0"
            value={formData.stock}
            onChange={(e) => handleInputChange("stock", e.target.value)}
            placeholder="1"
            required
            className={errors.stock ? "border-red-500" : ""}
          />
          {errors.stock && <p className="text-sm text-red-500">{errors.stock}</p>}
        </div>

        <div className="space-y-2">
          <Label>Estimated Value</Label>
          <div className="p-2 bg-green-50 border border-green-200 rounded-md">
            <p className="text-lg font-semibold text-green-700">₹{estimatedValue.toLocaleString()}</p>
            <p className="text-xs text-green-600">Metal + Stone + Making</p>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="stoneDetails">Stone Details</Label>
        <Input
          id="stoneDetails"
          value={formData.stoneDetails}
          onChange={(e) => handleInputChange("stoneDetails", e.target.value)}
          placeholder="Diamond, Ruby, etc."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder="Additional details about the item"
          rows={3}
        />
      </div>

      {/* Item ID and HSN Code Preview */}
      {previewId && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="font-medium text-blue-900 mb-2">Generated Item Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700 font-medium">Item ID:</span>
              <p className="font-mono text-blue-900 bg-white px-2 py-1 rounded border mt-1">{previewId}</p>
            </div>
            <div>
              <span className="text-blue-700 font-medium">HSN Code:</span>
              <p className="font-mono text-blue-900 bg-white px-2 py-1 rounded border mt-1">{previewHSN}</p>
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-2">
            This ID will be automatically generated when you save the item
          </p>
        </div>
      )}

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : item ? "Update Item" : "Add Item"}
        </Button>
      </div>
    </form>
  )
}
