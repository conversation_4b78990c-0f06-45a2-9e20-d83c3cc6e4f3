import { BaseService } from '../base-service'
import { Sale, SaleItem, Customer, InventoryItem } from '../../types'
import { RowDataPacket } from 'mysql2'

export class SalesService extends BaseService<Sale> {
  protected tableName = 'sales'

  async create(data: Omit<Sale, 'id' | 'createdAt' | 'updatedAt'>): Promise<Sale> {
    const connection = await this.pool.getConnection()
    try {
      await connection.beginTransaction()

      // Create the sale
      const saleId = this.generateId()
      const now = new Date().toISOString()
      
      const saleData = {
        id: saleId,
        customer_id: data.customer.id,
        subtotal: data.subtotal,
        cgst: data.cgst,
        sgst: data.sgst,
        total: data.total,
        status: data.status,
        date: data.date,
        created_at: now,
        updated_at: now
      }

      await connection.execute(
        `INSERT INTO ${this.tableName} (${Object.keys(saleData).join(', ')}) VALUES (${Object.keys(saleData).map(() => '?').join(', ')})`,
        Object.values(saleData)
      )

      // Create sale items
      for (const item of data.items) {
        const itemData = {
          id: this.generateId(),
          sale_id: saleId,
          inventory_id: item.item.id,
          gross_weight: item.grossWeight,
          stone_weight: item.stoneWeight,
          net_weight: item.netWeight,
          rate: item.rate,
          making_charges: item.makingCharges,
          stone_amount: item.stoneAmount,
          amount: item.amount,
          created_at: now
        }

        await connection.execute(
          `INSERT INTO sale_items (${Object.keys(itemData).join(', ')}) VALUES (${Object.keys(itemData).map(() => '?').join(', ')})`,
          Object.values(itemData)
        )

        // Update inventory stock
        await connection.execute(
          'UPDATE inventory SET stock = stock - 1, updated_at = ? WHERE id = ?',
          [now, item.item.id]
        )
      }

      // Update customer total purchases and last visit
      await connection.execute(
        'UPDATE customers SET total_purchases = total_purchases + ?, last_visit = ?, updated_at = ? WHERE id = ?',
        [data.total, now, now, data.customer.id]
      )

      await connection.commit()
      
      // Return the created sale with full data
      return this.findById(saleId) as Promise<Sale>
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  async findById(id: string): Promise<Sale | null> {
    const sql = `
      SELECT s.*, c.id as customer_id,
             CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name,
             c.phone as customer_phone, c.email as customer_email,
             CONCAT(COALESCE(c.address_line1, ''), ' ', COALESCE(c.address_line2, '')) as customer_address,
             c.gst_number as customer_gst_number, c.total_purchases as customer_total_purchases,
             c.last_visit as customer_last_visit, c.created_at as customer_created_at,
             c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    const row = rows[0]
    const sale = this.transformSaleRow(row)
    
    // Get sale items
    sale.items = await this.getSaleItems(id)
    
    return sale
  }

  async findAll(conditions: Record<string, any> = {}): Promise<Sale[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `
      SELECT s.*, c.id as customer_id,
             CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as customer_name,
             c.phone as customer_phone, c.email as customer_email,
             CONCAT(COALESCE(c.address_line1, ''), ' ', COALESCE(c.address_line2, '')) as customer_address,
             c.gst_number as customer_gst_number, c.total_purchases as customer_total_purchases,
             c.last_visit as customer_last_visit, c.created_at as customer_created_at,
             c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      ${whereClause}
      ORDER BY s.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    
    const sales = []
    for (const row of rows) {
      const sale = this.transformSaleRow(row)
      sale.items = await this.getSaleItems(sale.id)
      sales.push(sale)
    }
    
    return sales
  }

  private transformSaleRow(row: any): Sale {
    return {
      id: row.id,
      customer: {
        id: row.customer_id,
        name: (row.customer_name || '').trim() || 'Walk-in Customer',
        phone: row.customer_phone || '',
        email: row.customer_email || '',
        address: (row.customer_address || '').trim() || '',
        gstNumber: row.customer_gst_number || '',
        totalPurchases: parseFloat(row.customer_total_purchases) || 0,
        lastVisit: row.customer_last_visit || '',
        createdAt: row.customer_created_at || '',
        updatedAt: row.customer_updated_at || ''
      },
      subtotal: parseFloat(row.subtotal) || 0,
      cgst: parseFloat(row.cgst) || 0,
      sgst: parseFloat(row.sgst) || 0,
      total: parseFloat(row.final_amount || row.total) || 0,
      status: row.status || 'pending',
      date: row.invoice_date || row.date || row.created_at,
      createdAt: row.created_at || '',
      updatedAt: row.updated_at || '',
      items: [] // Will be populated separately
    }
  }

  private async getSaleItems(saleId: string): Promise<SaleItem[]> {
    const sql = `
      SELECT si.*, i.id as item_id, i.name as item_name, i.description as item_description,
             i.metal_type as item_metal_type, i.gross_weight as item_gross_weight,
             i.stone_weight as item_stone_weight, i.net_weight as item_net_weight,
             i.stone_charges as item_stone_charges, i.purity as item_purity,
             i.making_charges as item_making_charges, i.selling_price as item_selling_price,
             i.stock_quantity as item_stock_quantity, i.stone_details as item_stone_details,
             i.created_at as item_created_at, i.updated_at as item_updated_at
      FROM sale_items si
      LEFT JOIN inventory i ON si.inventory_id = i.id
      WHERE si.sale_id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [saleId])
    
    return rows.map(row => ({
      id: row.id,
      item: {
        id: row.item_id,
        name: row.item_name,
        description: row.item_description,
        metalType: row.item_metal_type,
        grossWeight: parseFloat(row.item_gross_weight) || 0,
        stoneWeight: parseFloat(row.item_stone_weight) || 0,
        netWeight: parseFloat(row.item_net_weight) || 0,
        stoneAmount: parseFloat(row.item_stone_charges) || 0,
        purity: row.item_purity,
        makingCharges: parseFloat(row.item_making_charges) || 0,
        currentValue: parseFloat(row.item_selling_price) || 0,
        stock: parseInt(row.item_stock_quantity) || 0,
        stoneDetails: row.item_stone_details,
        createdAt: row.item_created_at,
        updatedAt: row.item_updated_at
      },
      grossWeight: parseFloat(row.gross_weight) || 0,
      stoneWeight: parseFloat(row.stone_weight) || 0,
      netWeight: parseFloat(row.net_weight) || 0,
      rate: parseFloat(row.rate) || 0,
      makingCharges: parseFloat(row.making_charges) || 0,
      stoneAmount: parseFloat(row.stone_amount) || 0,
      amount: parseFloat(row.amount) || 0
    }))
  }

  async getSalesStats(startDate?: string, endDate?: string): Promise<{
    totalSales: number
    totalRevenue: number
    averageOrderValue: number
    salesByStatus: { status: string; count: number }[]
  }> {
    let whereClause = ''
    const params: any[] = []
    
    if (startDate && endDate) {
      whereClause = 'WHERE date BETWEEN ? AND ?'
      params.push(startDate, endDate)
    }

    const totalSales = await this.executeQuery(
      `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`,
      params
    )

    const revenueResult = await this.executeQuery(
      `SELECT SUM(total) as revenue FROM ${this.tableName} ${whereClause}`,
      params
    )

    const statusResult = await this.executeQuery(
      `SELECT status, COUNT(*) as count FROM ${this.tableName} ${whereClause} GROUP BY status`,
      params
    )

    const totalCount = totalSales[0]?.count || 0
    const totalRevenue = revenueResult[0]?.revenue || 0
    const averageOrderValue = totalCount > 0 ? totalRevenue / totalCount : 0

    return {
      totalSales: totalCount,
      totalRevenue,
      averageOrderValue,
      salesByStatus: statusResult.map((row: any) => ({
        status: row.status,
        count: row.count
      }))
    }
  }
}
