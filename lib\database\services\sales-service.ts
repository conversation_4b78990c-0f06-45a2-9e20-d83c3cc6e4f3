import { BaseService } from '../base-service'
import { Sale, SaleItem, Customer, InventoryItem } from '../../types'
import { RowDataPacket } from 'mysql2'

export class SalesService extends BaseService<Sale> {
  protected tableName = 'sales'

  async create(data: Omit<Sale, 'id' | 'createdAt' | 'updatedAt'>): Promise<Sale> {
    const connection = await this.pool.getConnection()
    try {
      await connection.beginTransaction()

      // Create the sale
      const saleId = this.generateId()
      const now = new Date().toISOString()
      
      const saleData = {
        id: saleId,
        customer_id: data.customer.id,
        subtotal: data.subtotal,
        cgst: data.cgst,
        sgst: data.sgst,
        total: data.total,
        status: data.status,
        date: data.date,
        created_at: now,
        updated_at: now
      }

      await connection.execute(
        `INSERT INTO ${this.tableName} (${Object.keys(saleData).join(', ')}) VALUES (${Object.keys(saleData).map(() => '?').join(', ')})`,
        Object.values(saleData)
      )

      // Create sale items
      for (const item of data.items) {
        const itemData = {
          id: this.generateId(),
          sale_id: saleId,
          inventory_id: item.item.id,
          gross_weight: item.grossWeight,
          stone_weight: item.stoneWeight,
          net_weight: item.netWeight,
          rate: item.rate,
          making_charges: item.makingCharges,
          stone_amount: item.stoneAmount,
          amount: item.amount,
          created_at: now
        }

        await connection.execute(
          `INSERT INTO sale_items (${Object.keys(itemData).join(', ')}) VALUES (${Object.keys(itemData).map(() => '?').join(', ')})`,
          Object.values(itemData)
        )

        // Update inventory stock
        await connection.execute(
          'UPDATE inventory SET stock = stock - 1, updated_at = ? WHERE id = ?',
          [now, item.item.id]
        )
      }

      // Update customer total purchases and last visit
      await connection.execute(
        'UPDATE customers SET total_purchases = total_purchases + ?, last_visit = ?, updated_at = ? WHERE id = ?',
        [data.total, now, now, data.customer.id]
      )

      await connection.commit()
      
      // Return the created sale with full data
      return this.findById(saleId) as Promise<Sale>
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  async findById(id: string): Promise<Sale | null> {
    const sql = `
      SELECT s.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [id])
    
    if (rows.length === 0) return null
    
    const row = rows[0]
    const sale = this.transformSaleRow(row)
    
    // Get sale items
    sale.items = await this.getSaleItems(id)
    
    return sale
  }

  async findAll(conditions: Record<string, any> = {}): Promise<Sale[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `
      SELECT s.*, c.id as customer_id, c.name as customer_name, c.phone as customer_phone, 
             c.email as customer_email, c.address as customer_address, c.gst_number as customer_gst_number,
             c.total_purchases as customer_total_purchases, c.last_visit as customer_last_visit,
             c.created_at as customer_created_at, c.updated_at as customer_updated_at
      FROM ${this.tableName} s
      LEFT JOIN customers c ON s.customer_id = c.id
      ${whereClause}
      ORDER BY s.created_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, params)
    
    const sales = []
    for (const row of rows) {
      const sale = this.transformSaleRow(row)
      sale.items = await this.getSaleItems(sale.id)
      sales.push(sale)
    }
    
    return sales
  }

  private transformSaleRow(row: any): Sale {
    return {
      id: row.id,
      customer: {
        id: row.customer_id,
        name: row.customer_name,
        phone: row.customer_phone,
        email: row.customer_email,
        address: row.customer_address,
        gstNumber: row.customer_gst_number,
        totalPurchases: row.customer_total_purchases,
        lastVisit: row.customer_last_visit,
        createdAt: row.customer_created_at,
        updatedAt: row.customer_updated_at
      },
      subtotal: row.subtotal,
      cgst: row.cgst,
      sgst: row.sgst,
      total: row.total,
      status: row.status,
      date: row.date,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      items: [] // Will be populated separately
    }
  }

  private async getSaleItems(saleId: string): Promise<SaleItem[]> {
    const sql = `
      SELECT si.*, i.id as item_id, i.name as item_name, i.category as item_category,
             i.metal_type as item_metal_type, i.gross_weight as item_gross_weight,
             i.stone_weight as item_stone_weight, i.net_weight as item_net_weight,
             i.stone_amount as item_stone_amount, i.purity as item_purity,
             i.making_charges as item_making_charges, i.current_value as item_current_value,
             i.stock as item_stock, i.stone_details as item_stone_details,
             i.description as item_description, i.created_at as item_created_at,
             i.updated_at as item_updated_at
      FROM sale_items si
      LEFT JOIN inventory i ON si.inventory_id = i.id
      WHERE si.sale_id = ?
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [saleId])
    
    return rows.map(row => ({
      id: row.id,
      item: {
        id: row.item_id,
        name: row.item_name,
        category: row.item_category,
        metalType: row.item_metal_type,
        grossWeight: row.item_gross_weight,
        stoneWeight: row.item_stone_weight,
        netWeight: row.item_net_weight,
        stoneAmount: row.item_stone_amount,
        purity: row.item_purity,
        makingCharges: row.item_making_charges,
        currentValue: row.item_current_value,
        stock: row.item_stock,
        stoneDetails: row.item_stone_details,
        description: row.item_description,
        createdAt: row.item_created_at,
        updatedAt: row.item_updated_at
      },
      grossWeight: row.gross_weight,
      stoneWeight: row.stone_weight,
      netWeight: row.net_weight,
      rate: row.rate,
      makingCharges: row.making_charges,
      stoneAmount: row.stone_amount,
      amount: row.amount
    }))
  }

  async getSalesStats(startDate?: string, endDate?: string): Promise<{
    totalSales: number
    totalRevenue: number
    averageOrderValue: number
    salesByStatus: { status: string; count: number }[]
  }> {
    let whereClause = ''
    const params: any[] = []
    
    if (startDate && endDate) {
      whereClause = 'WHERE date BETWEEN ? AND ?'
      params.push(startDate, endDate)
    }

    const totalSales = await this.executeQuery(
      `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`,
      params
    )

    const revenueResult = await this.executeQuery(
      `SELECT SUM(total) as revenue FROM ${this.tableName} ${whereClause}`,
      params
    )

    const statusResult = await this.executeQuery(
      `SELECT status, COUNT(*) as count FROM ${this.tableName} ${whereClause} GROUP BY status`,
      params
    )

    const totalCount = totalSales[0]?.count || 0
    const totalRevenue = revenueResult[0]?.revenue || 0
    const averageOrderValue = totalCount > 0 ? totalRevenue / totalCount : 0

    return {
      totalSales: totalCount,
      totalRevenue,
      averageOrderValue,
      salesByStatus: statusResult.map((row: any) => ({
        status: row.status,
        count: row.count
      }))
    }
  }
}
