#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function updateSampleData() {
  console.log('🔄 Updating Sample Data with Enhanced Examples...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Clear existing sample data
    console.log('🧹 Step 1: Clearing existing sample data...')
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    await connection.execute('DELETE FROM exchange_audit_trail')
    await connection.execute('DELETE FROM sales_exchange_items')
    await connection.execute('DELETE FROM exchange_purchase_bills')
    await connection.execute('DELETE FROM exchange_items')
    await connection.execute('DELETE FROM exchange_transactions')
    await connection.execute('UPDATE bill_sequences SET current_number = 0')
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')
    console.log('   ✅ Cleared existing sample data')

    // Step 2: Update exchange rates to current market rates
    console.log('💰 Step 2: Updating exchange rates to current market rates...')
    const rateUpdates = [
      { metal_type: 'gold', purity: '24K', rate: 7200.00 },
      { metal_type: 'gold', purity: '22K', rate: 6600.00 },
      { metal_type: 'gold', purity: '18K', rate: 5400.00 },
      { metal_type: 'gold', purity: '14K', rate: 4200.00 },
      { metal_type: 'silver', purity: '999', rate: 90.00 },
      { metal_type: 'silver', purity: '925', rate: 83.00 },
      { metal_type: 'silver', purity: '900', rate: 81.00 }
    ]

    for (const rate of rateUpdates) {
      await connection.execute(`
        UPDATE exchange_rates 
        SET rate_per_gram = ?, effective_date = CURDATE(), updated_at = NOW() 
        WHERE metal_type = ? AND purity = ?
      `, [rate.rate, rate.metal_type, rate.purity])
    }
    console.log('   ✅ Updated all exchange rates to current market values')

    // Step 3: Add more diverse customers
    console.log('👥 Step 3: Adding diverse customers...')
    const newCustomers = [
      { id: 'cust_007', name: 'Kavya Nair', phone: '9876543216', email: '<EMAIL>', address: '123 Marine Drive, Kochi, Kerala 682001' },
      { id: 'cust_008', name: 'Rohit Gupta', phone: '9876543217', email: '<EMAIL>', address: '456 Park Street, Kolkata, West Bengal 700001' },
      { id: 'cust_009', name: 'Anita Desai', phone: '9876543218', email: '<EMAIL>', address: '789 Linking Road, Mumbai, Maharashtra 400050' }
    ]

    for (const customer of newCustomers) {
      await connection.execute(`
        INSERT IGNORE INTO customers (id, name, phone, email, address, total_purchases, last_visit, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, 150000.00, CURDATE(), NOW(), NOW())
      `, [customer.id, customer.name, customer.phone, customer.email, customer.address])
    }
    console.log('   ✅ Added diverse customer base')

    // Step 4: Create comprehensive exchange transactions
    console.log('🔄 Step 4: Creating comprehensive exchange transactions...')
    const transactions = [
      {
        id: 'exg_001',
        transaction_number: 'EXG-20250131-001',
        customer_id: 'cust_001',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'Premium gold bar exchange - investment grade'
      },
      {
        id: 'exg_002', 
        transaction_number: 'EXG-20250131-002',
        customer_id: 'cust_002',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'Traditional silver jewelry set - family heirloom'
      },
      {
        id: 'exg_003',
        transaction_number: 'EXG-20250131-003', 
        customer_id: 'cust_003',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'Mixed gold jewelry collection - various occasions'
      },
      {
        id: 'exg_004',
        transaction_number: 'EXG-20250131-004',
        customer_id: 'cust_004', 
        transaction_date: '2025-01-31',
        status: 'pending',
        notes: 'Antique evaluation pending - requires expert assessment'
      },
      {
        id: 'exg_005',
        transaction_number: 'EXG-20250131-005',
        customer_id: 'cust_005',
        transaction_date: '2025-01-31', 
        status: 'completed',
        notes: 'Heritage gold necklace - 3 generations old'
      },
      {
        id: 'exg_006',
        transaction_number: 'EXG-20250131-006',
        customer_id: 'cust_006',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'Contemporary silver collection - modern design'
      },
      {
        id: 'exg_007',
        transaction_number: 'EXG-20250131-007',
        customer_id: 'cust_007',
        transaction_date: '2025-01-31',
        status: 'completed',
        notes: 'Bridal gold set exchange - wedding preparation'
      },
      {
        id: 'exg_008',
        transaction_number: 'EXG-20250131-008',
        customer_id: 'cust_008',
        transaction_date: '2025-01-31',
        status: 'cancelled',
        notes: 'Customer changed mind - transaction cancelled'
      }
    ]

    for (const transaction of transactions) {
      await connection.execute(`
        INSERT INTO exchange_transactions (id, transaction_number, customer_id, transaction_date, total_amount, payment_method, notes, status, created_at, updated_at) 
        VALUES (?, ?, ?, ?, 0.00, 'cash', ?, ?, NOW(), NOW())
      `, [transaction.id, transaction.transaction_number, transaction.customer_id, transaction.transaction_date, transaction.notes, transaction.status])
    }
    console.log(`   ✅ Created ${transactions.length} diverse exchange transactions`)

    // Step 5: Add comprehensive exchange items
    console.log('💎 Step 5: Adding comprehensive exchange items...')
    const items = [
      // Transaction 1: Premium gold bar
      { id: randomUUID(), transaction_id: 'exg_001', description: 'GOLD BAR 24K INVESTMENT GRADE', metal_type: 'gold', purity: '24K', gross_weight: 100.000, stone_weight: 0.000, net_weight: 100.000, rate_per_gram: 7200.00, amount: 720000.00, condition: 'good' },
      
      // Transaction 2: Traditional silver set
      { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER BANGLES SET TRADITIONAL DESIGN', metal_type: 'silver', purity: '925', gross_weight: 200.000, stone_weight: 0.000, net_weight: 200.000, rate_per_gram: 83.00, amount: 16600.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER ANKLETS PAIR HEAVY', metal_type: 'silver', purity: '925', gross_weight: 150.000, stone_weight: 0.000, net_weight: 150.000, rate_per_gram: 83.00, amount: 12450.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_002', description: 'SILVER WAIST CHAIN TRADITIONAL', metal_type: 'silver', purity: '925', gross_weight: 80.000, stone_weight: 0.000, net_weight: 80.000, rate_per_gram: 83.00, amount: 6640.00, condition: 'fair' },
      
      // Transaction 3: Mixed gold collection
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD CHAIN 22K THICK ROPE DESIGN', metal_type: 'gold', purity: '22K', gross_weight: 30.000, stone_weight: 0.000, net_weight: 30.000, rate_per_gram: 6600.00, amount: 198000.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD EARRINGS 18K DIAMOND STUDDED', metal_type: 'gold', purity: '18K', gross_weight: 15.000, stone_weight: 5.000, net_weight: 10.000, rate_per_gram: 5400.00, amount: 54000.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD BRACELET 14K DESIGNER', metal_type: 'gold', purity: '14K', gross_weight: 12.000, stone_weight: 3.000, net_weight: 9.000, rate_per_gram: 4200.00, amount: 37800.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_003', description: 'GOLD RING SET 22K WEDDING COLLECTION', metal_type: 'gold', purity: '22K', gross_weight: 20.000, stone_weight: 6.000, net_weight: 14.000, rate_per_gram: 6600.00, amount: 92400.00, condition: 'fair' },
      
      // Transaction 4: Antique evaluation
      { id: randomUUID(), transaction_id: 'exg_004', description: 'ANTIQUE GOLD NECKLACE KUNDAN WORK', metal_type: 'gold', purity: '22K', gross_weight: 60.000, stone_weight: 20.000, net_weight: 40.000, rate_per_gram: 6600.00, amount: 264000.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_004', description: 'ANTIQUE GOLD BANGLES PAIR HEAVY', metal_type: 'gold', purity: '22K', gross_weight: 80.000, stone_weight: 10.000, net_weight: 70.000, rate_per_gram: 6600.00, amount: 462000.00, condition: 'fair' },
      
      // Transaction 5: Heritage necklace
      { id: randomUUID(), transaction_id: 'exg_005', description: 'HERITAGE GOLD NECKLACE 3 GENERATION', metal_type: 'gold', purity: '22K', gross_weight: 75.000, stone_weight: 25.000, net_weight: 50.000, rate_per_gram: 6600.00, amount: 330000.00, condition: 'good' },
      
      // Transaction 6: Contemporary silver
      { id: randomUUID(), transaction_id: 'exg_006', description: 'MODERN SILVER BANGLES OXIDIZED', metal_type: 'silver', purity: '925', gross_weight: 120.000, stone_weight: 0.000, net_weight: 120.000, rate_per_gram: 83.00, amount: 9960.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_006', description: 'SILVER PENDANT SET CONTEMPORARY', metal_type: 'silver', purity: '925', gross_weight: 50.000, stone_weight: 5.000, net_weight: 45.000, rate_per_gram: 83.00, amount: 3735.00, condition: 'good' },
      
      // Transaction 7: Bridal gold set
      { id: randomUUID(), transaction_id: 'exg_007', description: 'BRIDAL GOLD NECKLACE SET HEAVY', metal_type: 'gold', purity: '22K', gross_weight: 120.000, stone_weight: 30.000, net_weight: 90.000, rate_per_gram: 6600.00, amount: 594000.00, condition: 'good' },
      { id: randomUUID(), transaction_id: 'exg_007', description: 'BRIDAL GOLD BANGLES SET 6 PIECES', metal_type: 'gold', purity: '22K', gross_weight: 150.000, stone_weight: 20.000, net_weight: 130.000, rate_per_gram: 6600.00, amount: 858000.00, condition: 'good' }
    ]

    for (const item of items) {
      await connection.execute(`
        INSERT INTO exchange_items (id, transaction_id, item_description, metal_type, purity, gross_weight, stone_weight, net_weight, rate_per_gram, amount, item_condition, notes, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Enhanced sample data', NOW(), NOW())
      `, [item.id, item.transaction_id, item.description, item.metal_type, item.purity, item.gross_weight, item.stone_weight, item.net_weight, item.rate_per_gram, item.amount, item.condition])
    }
    console.log(`   ✅ Added ${items.length} comprehensive exchange items`)

    // Step 6: Update transaction totals
    console.log('🔢 Step 6: Calculating and updating transaction totals...')
    const totals = [
      { id: 'exg_001', total: 720000.00 },  // Premium gold bar
      { id: 'exg_002', total: 35690.00 },   // Traditional silver set (3 items)
      { id: 'exg_003', total: 382200.00 },  // Mixed gold collection (4 items)
      { id: 'exg_004', total: 726000.00 },  // Antique evaluation (2 items)
      { id: 'exg_005', total: 330000.00 },  // Heritage necklace
      { id: 'exg_006', total: 13695.00 },   // Contemporary silver (2 items)
      { id: 'exg_007', total: 1452000.00 }, // Bridal gold set (2 items)
      { id: 'exg_008', total: 0.00 }        // Cancelled transaction
    ]

    for (const total of totals) {
      await connection.execute(`UPDATE exchange_transactions SET total_amount = ? WHERE id = ?`, [total.total, total.id])
    }
    console.log('   ✅ Updated all transaction totals')

    // Step 7: Generate purchase bills for completed transactions
    console.log('📄 Step 7: Generating purchase bills for completed transactions...')
    const completedTransactions = [
      { id: 'exg_001', customer_id: 'cust_001', amount: 720000.00 },
      { id: 'exg_002', customer_id: 'cust_002', amount: 35690.00 },
      { id: 'exg_003', customer_id: 'cust_003', amount: 382200.00 },
      { id: 'exg_005', customer_id: 'cust_005', amount: 330000.00 },
      { id: 'exg_006', customer_id: 'cust_006', amount: 13695.00 },
      { id: 'exg_007', customer_id: 'cust_007', amount: 1452000.00 }
    ]
    
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`
    
    for (let i = 0; i < completedTransactions.length; i++) {
      const transaction = completedTransactions[i]
      const billId = randomUUID()
      const billNumber = `EPB/${financialYear}/${String(i + 1).padStart(4, '0')}`
      const cgstAmount = (transaction.amount * 1.5) / 100
      const sgstAmount = (transaction.amount * 1.5) / 100
      const totalWithTax = transaction.amount + cgstAmount + sgstAmount
      
      await connection.execute(`
        INSERT INTO exchange_purchase_bills (id, bill_number, exchange_transaction_id, customer_id, bill_date, total_amount, cgst_amount, sgst_amount, total_with_tax, payment_method, payment_status, notes, created_at, updated_at) 
        VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, ?, 'cash', 'paid', 'Enhanced sample bill', NOW(), NOW())
      `, [billId, billNumber, transaction.id, transaction.customer_id, transaction.amount, cgstAmount, sgstAmount, totalWithTax])
      
      // Update transaction with bill reference
      await connection.execute(`
        UPDATE exchange_transactions 
        SET purchase_bill_generated = TRUE, purchase_bill_id = ?, purchase_bill_number = ?, purchase_bill_date = CURDATE() 
        WHERE id = ?
      `, [billId, billNumber, transaction.id])
    }
    
    // Update bill sequence
    await connection.execute(`UPDATE bill_sequences SET current_number = ? WHERE sequence_type = 'exchange_purchase'`, [completedTransactions.length])
    console.log(`   ✅ Generated ${completedTransactions.length} purchase bills`)

    // Step 8: Create comprehensive audit trail
    console.log('📋 Step 8: Creating comprehensive audit trail...')
    const auditEntries = [
      { id: randomUUID(), transaction_id: 'exg_001', action: 'created', description: 'Premium gold bar exchange - investment grade transaction created', values: '{"totalAmount": 720000, "items": 1, "weight": "100g"}' },
      { id: randomUUID(), transaction_id: 'exg_001', action: 'billed', description: 'Purchase bill EPB/2025-26/0001 generated for premium gold bar', values: '{"billNumber": "EPB/2025-26/0001", "totalWithTax": 741600}' },
      { id: randomUUID(), transaction_id: 'exg_002', action: 'created', description: 'Traditional silver jewelry set exchange created', values: '{"totalAmount": 35690, "items": 3, "weight": "430g"}' },
      { id: randomUUID(), transaction_id: 'exg_002', action: 'billed', description: 'Purchase bill EPB/2025-26/0002 generated for silver set', values: '{"billNumber": "EPB/2025-26/0002", "totalWithTax": 36761.7}' },
      { id: randomUUID(), transaction_id: 'exg_003', action: 'created', description: 'Mixed gold jewelry collection exchange created', values: '{"totalAmount": 382200, "items": 4, "purities": ["22K", "18K", "14K"]}' },
      { id: randomUUID(), transaction_id: 'exg_003', action: 'billed', description: 'Purchase bill EPB/2025-26/0003 generated for gold collection', values: '{"billNumber": "EPB/2025-26/0003", "totalWithTax": 393666}' },
      { id: randomUUID(), transaction_id: 'exg_004', action: 'created', description: 'Antique jewelry evaluation pending - expert assessment required', values: '{"totalAmount": 726000, "items": 2, "status": "pending", "specialNote": "Kundan work"}' },
      { id: randomUUID(), transaction_id: 'exg_005', action: 'created', description: 'Heritage gold necklace exchange - 3 generation family piece', values: '{"totalAmount": 330000, "items": 1, "heritage": true}' },
      { id: randomUUID(), transaction_id: 'exg_005', action: 'billed', description: 'Purchase bill EPB/2025-26/0004 generated for heritage necklace', values: '{"billNumber": "EPB/2025-26/0004", "totalWithTax": 339900}' },
      { id: randomUUID(), transaction_id: 'exg_006', action: 'created', description: 'Contemporary silver collection exchange created', values: '{"totalAmount": 13695, "items": 2, "design": "modern"}' },
      { id: randomUUID(), transaction_id: 'exg_006', action: 'billed', description: 'Purchase bill EPB/2025-26/0005 generated for silver collection', values: '{"billNumber": "EPB/2025-26/0005", "totalWithTax": 14105.85}' },
      { id: randomUUID(), transaction_id: 'exg_007', action: 'created', description: 'Bridal gold set exchange - wedding preparation', values: '{"totalAmount": 1452000, "items": 2, "occasion": "bridal", "weight": "270g"}' },
      { id: randomUUID(), transaction_id: 'exg_007', action: 'billed', description: 'Purchase bill EPB/2025-26/0006 generated for bridal set', values: '{"billNumber": "EPB/2025-26/0006", "totalWithTax": 1495560}' },
      { id: randomUUID(), transaction_id: 'exg_008', action: 'created', description: 'Transaction created but later cancelled by customer', values: '{"totalAmount": 0, "status": "cancelled", "reason": "customer_decision"}' },
      { id: randomUUID(), transaction_id: 'exg_008', action: 'cancelled', description: 'Transaction cancelled - customer changed mind', values: '{"cancelledAt": "2025-01-31", "reason": "customer_decision"}' }
    ]

    for (const entry of auditEntries) {
      await connection.execute(`
        INSERT INTO exchange_audit_trail (id, exchange_transaction_id, action_type, action_description, new_values, performed_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
      `, [entry.id, entry.transaction_id, entry.action, entry.description, entry.values])
    }
    console.log(`   ✅ Created ${auditEntries.length} comprehensive audit trail entries`)

    // Step 9: Verify updated data
    console.log('\n🔍 Step 9: Verifying updated sample data...')
    const tables = ['exchange_rates', 'exchange_transactions', 'exchange_items', 'exchange_purchase_bills', 'exchange_audit_trail']
    
    for (const table of tables) {
      const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`)
      const count = (rows as any)[0].count
      console.log(`   ✅ ${table}: ${count} records`)
    }

    console.log('\n🎉 Sample Data Update Completed Successfully!')

    console.log('\n📊 ENHANCED SAMPLE DATA SUMMARY:')
    console.log('=' .repeat(70))
    console.log('💰 Exchange Rates: Updated to current market rates (2025)')
    console.log('👥 Customers: 9 diverse customers across India')
    console.log('🔄 Transactions: 8 transactions (6 completed, 1 pending, 1 cancelled)')
    console.log('💎 Items: 16 diverse items covering all scenarios')
    console.log('📄 Bills: 6 professional purchase bills with GST')
    console.log('📋 Audit: 15 comprehensive audit trail entries')
    console.log('=' .repeat(70))

    console.log('\n💎 SAMPLE DATA HIGHLIGHTS:')
    console.log('🏆 Highest Value: ₹14,52,000 (Bridal gold set - 270g)')
    console.log('💰 Premium Item: ₹7,20,000 (100g gold bar - investment grade)')
    console.log('🏺 Heritage Piece: ₹3,30,000 (3-generation family necklace)')
    console.log('⏳ Pending Evaluation: ₹7,26,000 (Antique Kundan work)')
    console.log('🥈 Silver Collections: Traditional and contemporary designs')
    console.log('💍 Mixed Purities: 24K, 22K, 18K, 14K gold items')
    console.log('📊 Transaction Types: Completed, pending, cancelled')

    console.log('\n🚀 READY FOR COMPREHENSIVE TESTING:')
    console.log('✅ Realistic business scenarios')
    console.log('✅ Diverse customer base')
    console.log('✅ Various transaction statuses')
    console.log('✅ Complete audit trail')
    console.log('✅ Professional billing')
    console.log('✅ Current market rates')

  } catch (error) {
    console.error('\n❌ Sample data update failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the sample data update
updateSampleData().catch(console.error)
