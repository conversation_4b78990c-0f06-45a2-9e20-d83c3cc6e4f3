#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'

async function finalCompleteVerification() {
  console.log('🎯 Final Complete System Verification - JJ Jewellers Tiruppur...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to jj_jewellers_tiruppur database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected successfully\n')

    // Step 1: Complete database structure verification
    console.log('🏗️  Step 1: Complete database structure verification...')
    
    const [tables] = await connection.execute(`
      SELECT table_name, table_rows, data_length, index_length
      FROM information_schema.tables 
      WHERE table_schema = ? 
      ORDER BY table_name
    `, [dbConfig.database])

    console.log('📋 Complete Database Structure:')
    console.log('=' .repeat(80))
    let totalRows = 0
    ;(tables as any[]).forEach(table => {
      const rows = table.table_rows || 0
      totalRows += rows
      const tableName = table.table_name || 'unknown'
      console.log(`   ${tableName} | ${rows} rows | ${(table.data_length / 1024).toFixed(1)} KB`)
    })
    console.log('=' .repeat(80))
    console.log(`   Total Tables: ${(tables as any[]).length} | Total Rows: ${totalRows}`)

    // Step 2: Business configuration verification
    console.log('\n🏢 Step 2: Business configuration verification...')
    
    const [businessConfig] = await connection.execute(`
      SELECT business_name, city, state, gst_number, phone, email,
             default_making_charge_percentage, default_wastage_percentage, default_margin_percentage,
             cgst_rate, sgst_rate, igst_rate
      FROM business_settings LIMIT 1
    `)
    
    if ((businessConfig as any[]).length > 0) {
      const config = (businessConfig as any[])[0]
      console.log(`   ✅ Business: ${config.business_name}`)
      console.log(`   ✅ Location: ${config.city}, ${config.state}`)
      console.log(`   ✅ GST Number: ${config.gst_number}`)
      console.log(`   ✅ Contact: ${config.phone} | ${config.email}`)
      console.log(`   ✅ Making Charge: ${config.default_making_charge_percentage}%`)
      console.log(`   ✅ Wastage: ${config.default_wastage_percentage}%`)
      console.log(`   ✅ Margin: ${config.default_margin_percentage}%`)
      console.log(`   ✅ Tax Rates: CGST ${config.cgst_rate}% | SGST ${config.sgst_rate}% | IGST ${config.igst_rate}%`)
    }

    // Step 3: Complete user management verification
    console.log('\n👥 Step 3: Complete user management verification...')
    
    const [userDetails] = await connection.execute(`
      SELECT username, first_name, last_name, role, department, designation,
             max_discount_percentage, max_transaction_amount, is_active
      FROM users ORDER BY role, username
    `)
    
    console.log(`   ✅ Total Users: ${(userDetails as any[]).length}`)
    ;(userDetails as any[]).forEach(user => {
      const status = user.is_active ? '🟢' : '🔴'
      console.log(`      ${status} ${user.username} | ${user.first_name} ${user.last_name} | ${user.role} | Max Discount: ${user.max_discount_percentage}%`)
    })

    // Step 4: Customer database verification
    console.log('\n🛍️  Step 4: Customer database verification...')
    
    const [customerStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_customers,
        SUM(total_purchases) as total_purchases,
        AVG(total_purchases) as avg_purchase,
        COUNT(CASE WHEN loyalty_tier = 'platinum' THEN 1 END) as platinum,
        COUNT(CASE WHEN loyalty_tier = 'gold' THEN 1 END) as gold,
        COUNT(CASE WHEN loyalty_tier = 'silver' THEN 1 END) as silver,
        COUNT(CASE WHEN loyalty_tier = 'bronze' THEN 1 END) as bronze,
        COUNT(CASE WHEN preferred_language = 'tamil' THEN 1 END) as tamil_speakers,
        COUNT(CASE WHEN preferred_language = 'english' THEN 1 END) as english_speakers
      FROM customers
    `)
    
    const custStats = (customerStats as any[])[0]
    console.log(`   ✅ Total Customers: ${custStats.total_customers}`)
    console.log(`   ✅ Total Customer Purchases: ₹${custStats.total_purchases?.toLocaleString() || 0}`)
    console.log(`   ✅ Average Purchase Value: ₹${custStats.avg_purchase?.toLocaleString() || 0}`)
    console.log(`   ✅ Loyalty Distribution: ${custStats.platinum} Platinum | ${custStats.gold} Gold | ${custStats.silver} Silver | ${custStats.bronze} Bronze`)
    console.log(`   ✅ Language Preference: ${custStats.tamil_speakers} Tamil | ${custStats.english_speakers} English`)

    // Step 5: Metal rates and exchange rates verification
    console.log('\n💰 Step 5: Metal rates and exchange rates verification...')
    
    const [metalRateStats] = await connection.execute(`
      SELECT 
        mr.metal_type, mr.purity, mr.rate_per_gram, mr.change_percentage,
        er.discount_percentage, er.effective_rate
      FROM metal_rates mr
      LEFT JOIN exchange_rates er ON mr.metal_type = er.metal_type AND mr.purity = er.purity
      WHERE mr.is_active = TRUE AND er.is_active = TRUE
      ORDER BY mr.metal_type, mr.purity
    `)
    
    console.log(`   ✅ Active Metal & Exchange Rates: ${(metalRateStats as any[]).length}`)
    ;(metalRateStats as any[]).forEach(rate => {
      const changeIcon = rate.change_percentage > 0 ? '↗️' : rate.change_percentage < 0 ? '↘️' : '➡️'
      console.log(`      ${rate.metal_type.toUpperCase()} ${rate.purity}: Market ₹${rate.rate_per_gram}/g | Exchange ₹${Math.round(rate.effective_rate)}/g (${rate.discount_percentage}% discount) ${changeIcon}`)
    })

    // Step 6: Inventory verification
    console.log('\n📦 Step 6: Inventory verification...')
    
    const [inventoryStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_items,
        COUNT(CASE WHEN status = 'sold' THEN 1 END) as sold_items,
        COUNT(CASE WHEN metal_type = 'gold' THEN 1 END) as gold_items,
        COUNT(CASE WHEN metal_type = 'silver' THEN 1 END) as silver_items,
        SUM(CASE WHEN status = 'active' THEN selling_price ELSE 0 END) as active_inventory_value,
        SUM(CASE WHEN status = 'sold' THEN selling_price ELSE 0 END) as sold_inventory_value,
        COUNT(CASE WHEN is_hallmarked = TRUE THEN 1 END) as hallmarked_items
      FROM inventory
    `)
    
    const invStats = (inventoryStats as any[])[0]
    console.log(`   ✅ Total Inventory Items: ${invStats.total_items}`)
    console.log(`   ✅ Active Items: ${invStats.active_items} | Sold Items: ${invStats.sold_items}`)
    console.log(`   ✅ Metal Distribution: ${invStats.gold_items} Gold | ${invStats.silver_items} Silver`)
    console.log(`   ✅ Active Inventory Value: ₹${invStats.active_inventory_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Sold Inventory Value: ₹${invStats.sold_inventory_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Hallmarked Items: ${invStats.hallmarked_items}`)

    // Step 7: Sales transaction verification
    console.log('\n🛒 Step 7: Sales transaction verification...')
    
    const [salesStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_sales,
        SUM(subtotal) as total_subtotal,
        SUM(discount_amount) as total_discount,
        SUM(final_amount) as total_final_amount,
        AVG(final_amount) as avg_sale_value,
        COUNT(CASE WHEN sale_type = 'cash' THEN 1 END) as cash_sales,
        COUNT(CASE WHEN sale_type = 'exchange' THEN 1 END) as exchange_sales,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_sales
      FROM sales
    `)
    
    const salesSummary = (salesStats as any[])[0]
    console.log(`   ✅ Total Sales: ${salesSummary.total_sales}`)
    console.log(`   ✅ Total Subtotal: ₹${salesSummary.total_subtotal?.toLocaleString() || 0}`)
    console.log(`   ✅ Total Discount: ₹${salesSummary.total_discount?.toLocaleString() || 0}`)
    console.log(`   ✅ Total Final Amount: ₹${salesSummary.total_final_amount?.toLocaleString() || 0}`)
    console.log(`   ✅ Average Sale Value: ₹${salesSummary.avg_sale_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Sale Types: ${salesSummary.cash_sales} Cash | ${salesSummary.exchange_sales} Exchange`)
    console.log(`   ✅ Payment Status: ${salesSummary.paid_sales} Paid`)

    // Step 8: Exchange transaction verification
    console.log('\n🔄 Step 8: Exchange transaction verification...')
    
    const [exchangeStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_exchanges,
        SUM(total_amount) as total_exchange_value,
        AVG(total_amount) as avg_exchange_value,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_exchanges
      FROM exchange_transactions
    `)
    
    const exchStats = (exchangeStats as any[])[0]
    console.log(`   ✅ Total Exchange Transactions: ${exchStats.total_exchanges}`)
    console.log(`   ✅ Total Exchange Value: ₹${exchStats.total_exchange_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Average Exchange Value: ₹${exchStats.avg_exchange_value?.toLocaleString() || 0}`)
    console.log(`   ✅ Completed Exchanges: ${exchStats.completed_exchanges}`)

    // Step 9: Supporting data verification
    console.log('\n🏭 Step 9: Supporting data verification...')
    
    const [supportingStats] = await connection.execute(`
      SELECT 
        (SELECT COUNT(*) FROM suppliers WHERE is_active = TRUE) as active_suppliers,
        (SELECT COUNT(*) FROM categories WHERE is_active = TRUE) as active_categories,
        (SELECT COUNT(*) FROM bill_sequences WHERE is_active = TRUE) as active_sequences,
        (SELECT COUNT(*) FROM system_settings) as system_settings
    `)
    
    const suppStats = (supportingStats as any[])[0]
    console.log(`   ✅ Active Suppliers: ${suppStats.active_suppliers}`)
    console.log(`   ✅ Active Categories: ${suppStats.active_categories}`)
    console.log(`   ✅ Bill Sequences: ${suppStats.active_sequences}`)
    console.log(`   ✅ System Settings: ${suppStats.system_settings}`)

    console.log('\n🎉 Final Complete System Verification Completed Successfully!')

  } catch (error) {
    console.error('\n❌ Final verification failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the final complete verification
finalCompleteVerification().catch(console.error)
