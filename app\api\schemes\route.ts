import { NextRequest, NextResponse } from 'next/server'
import { schemeService } from '@/lib/database/services'

export async function GET() {
  try {
    const schemes = await schemeService.findAll()
    return NextResponse.json({ schemes })
  } catch (error) {
    console.error('Error fetching schemes:', error)
    return NextResponse.json(
      { error: 'Failed to fetch schemes' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const schemeData = await request.json()
    const scheme = await schemeService.create(schemeData)
    return NextResponse.json({ scheme })
  } catch (error) {
    console.error('Error creating scheme:', error)
    return NextResponse.json(
      { error: 'Failed to create scheme' },
      { status: 500 }
    )
  }
}
