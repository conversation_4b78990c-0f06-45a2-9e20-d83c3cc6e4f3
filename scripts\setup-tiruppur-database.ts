#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function setupTiruppurDatabase() {
  console.log('🏭 Setting up JJ Jewellers Tiruppur Database with Complete Professional Schema & Test Data...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jj_jewellers_tiruppur',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to jj_jewellers_tiruppur database\n')

    // Step 1: Create complete professional schema
    console.log('🏗️  Step 1: Creating complete professional schema...')
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    
    // Drop existing tables if they exist
    const tablesToDrop = [
      'sales_exchange_items', 'exchange_purchase_bills', 'exchange_items', 'exchange_transactions',
      'sale_items', 'sales', 'purchase_items', 'purchases', 'inventory', 'suppliers',
      'exchange_rates', 'metal_rates', 'categories', 'customers', 'users', 
      'system_settings', 'business_settings', 'bill_sequences', 'exchange_audit_trail'
    ]

    for (const table of tablesToDrop) {
      try {
        await connection.execute(`DROP TABLE IF EXISTS ${table}`)
      } catch (error) {
        // Ignore errors for non-existent tables
      }
    }

    // 1. Business Settings
    console.log('   Creating business_settings table...')
    await connection.execute(`
      CREATE TABLE business_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        business_name VARCHAR(255) NOT NULL,
        business_type ENUM('retail', 'wholesale', 'manufacturing', 'mixed') DEFAULT 'retail',
        business_registration_number VARCHAR(100),
        establishment_date DATE,
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Contact Information
        phone VARCHAR(20),
        alternate_phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        
        -- Legal Information
        gst_number VARCHAR(50),
        pan_number VARCHAR(20),
        tan_number VARCHAR(20),
        license_number VARCHAR(100),
        
        -- Banking Information
        bank_name VARCHAR(255),
        bank_branch VARCHAR(255),
        bank_account_number VARCHAR(50),
        bank_ifsc VARCHAR(20),
        
        -- Business Configuration
        logo_url VARCHAR(500),
        currency VARCHAR(10) DEFAULT 'INR',
        timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
        financial_year_start ENUM('april', 'january') DEFAULT 'april',
        
        -- Tax Configuration
        cgst_rate DECIMAL(5,2) DEFAULT 1.50,
        sgst_rate DECIMAL(5,2) DEFAULT 1.50,
        igst_rate DECIMAL(5,2) DEFAULT 3.00,
        
        -- Business Rules
        allow_negative_stock BOOLEAN DEFAULT FALSE,
        auto_generate_barcodes BOOLEAN DEFAULT TRUE,
        enable_loyalty_program BOOLEAN DEFAULT TRUE,
        
        -- Pricing Configuration
        default_making_charge_percentage DECIMAL(5,2) DEFAULT 15.00,
        default_wastage_percentage DECIMAL(5,2) DEFAULT 8.00,
        default_margin_percentage DECIMAL(5,2) DEFAULT 25.00,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // 2. System Settings
    console.log('   Creating system_settings table...')
    await connection.execute(`
      CREATE TABLE system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_category ENUM('general', 'security', 'notification', 'integration', 'reporting') DEFAULT 'general',
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json', 'date', 'time', 'datetime') DEFAULT 'string',
        description TEXT,
        is_system BOOLEAN DEFAULT FALSE,
        is_encrypted BOOLEAN DEFAULT FALSE,
        updated_by VARCHAR(36),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_setting_category (setting_category),
        INDEX idx_setting_key (setting_key),
        INDEX idx_is_system (is_system)
      )
    `)

    // 3. Users
    console.log('   Creating users table...')
    await connection.execute(`
      CREATE TABLE users (
        id VARCHAR(36) PRIMARY KEY,
        employee_code VARCHAR(50) UNIQUE,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        
        -- Personal Information
        title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
        first_name VARCHAR(100) NOT NULL,
        middle_name VARCHAR(100),
        last_name VARCHAR(100) NOT NULL,
        date_of_birth DATE,
        gender ENUM('male', 'female', 'other'),
        
        -- Contact Information
        phone VARCHAR(20),
        alternate_phone VARCHAR(20),
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Employment Information
        role ENUM('super_admin', 'admin', 'manager', 'sales_manager', 'sales_staff', 'accountant', 'cashier', 'security', 'viewer') NOT NULL DEFAULT 'sales_staff',
        department ENUM('management', 'sales', 'accounts', 'operations', 'security', 'support') DEFAULT 'sales',
        designation VARCHAR(100),
        joining_date DATE,
        salary DECIMAL(12,2),
        commission_percentage DECIMAL(5,2) DEFAULT 0.00,
        
        -- System Access
        permissions JSON,
        max_discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        max_transaction_amount DECIMAL(15,2) DEFAULT 0.00,
        
        -- Security
        is_active BOOLEAN DEFAULT TRUE,
        is_verified BOOLEAN DEFAULT FALSE,
        last_login TIMESTAMP NULL,
        login_attempts INT DEFAULT 0,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_employee_code (employee_code),
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_department (department),
        INDEX idx_is_active (is_active)
      )
    `)

    // 4. Customers
    console.log('   Creating customers table...')
    await connection.execute(`
      CREATE TABLE customers (
        id VARCHAR(36) PRIMARY KEY,
        customer_code VARCHAR(50) UNIQUE NOT NULL,
        customer_type ENUM('individual', 'business', 'corporate') DEFAULT 'individual',
        
        -- Personal Information
        title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
        first_name VARCHAR(100) NOT NULL,
        middle_name VARCHAR(100),
        last_name VARCHAR(100),
        business_name VARCHAR(255),
        date_of_birth DATE,
        anniversary_date DATE,
        gender ENUM('male', 'female', 'other'),
        occupation VARCHAR(100),
        
        -- Contact Information
        phone VARCHAR(20) NOT NULL,
        alternate_phone VARCHAR(20),
        email VARCHAR(255),
        whatsapp_number VARCHAR(20),
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Legal Information
        gst_number VARCHAR(50),
        pan_number VARCHAR(20),
        aadhar_number VARCHAR(20),
        
        -- Business Information
        credit_limit DECIMAL(15,2) DEFAULT 0.00,
        credit_days INT DEFAULT 0,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        
        -- Financial Tracking
        total_purchases DECIMAL(15,2) DEFAULT 0.00,
        total_outstanding DECIMAL(15,2) DEFAULT 0.00,
        total_payments DECIMAL(15,2) DEFAULT 0.00,
        last_payment_date DATE,
        
        -- Loyalty Program
        loyalty_points INT DEFAULT 0,
        loyalty_tier ENUM('bronze', 'silver', 'gold', 'platinum', 'diamond') DEFAULT 'bronze',
        membership_number VARCHAR(50),
        membership_date DATE,
        
        -- Preferences
        preferred_contact ENUM('phone', 'email', 'sms', 'whatsapp') DEFAULT 'phone',
        preferred_language ENUM('english', 'hindi', 'tamil', 'regional') DEFAULT 'english',
        preferred_metal ENUM('gold', 'silver', 'platinum', 'diamond') DEFAULT 'gold',
        
        -- Additional Information
        notes TEXT,
        special_instructions TEXT,
        referral_source VARCHAR(255),
        referred_by VARCHAR(36),
        
        -- Status
        is_active BOOLEAN DEFAULT TRUE,
        is_vip BOOLEAN DEFAULT FALSE,
        is_blacklisted BOOLEAN DEFAULT FALSE,
        blacklist_reason TEXT,
        last_visit TIMESTAMP NULL,
        visit_count INT DEFAULT 0,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_customer_code (customer_code),
        INDEX idx_customer_type (customer_type),
        INDEX idx_phone (phone),
        INDEX idx_email (email),
        INDEX idx_business_name (business_name),
        INDEX idx_city (city),
        INDEX idx_loyalty_tier (loyalty_tier),
        INDEX idx_is_active (is_active),
        INDEX idx_is_vip (is_vip),
        INDEX idx_last_visit (last_visit)
      )
    `)

    console.log('   ✅ Core tables created successfully')

    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    // Step 2: Seed comprehensive test data
    console.log('\n🌱 Step 2: Seeding comprehensive test data...')

    // Business Settings for Tiruppur
    console.log('   Setting up JJ Jewellers Tiruppur business configuration...')
    await connection.execute(`
      INSERT INTO business_settings (
        business_name, business_type, establishment_date, address_line1, address_line2,
        city, state, postal_code, country, phone, alternate_phone, email, website,
        gst_number, pan_number, bank_name, bank_branch, bank_account_number, bank_ifsc,
        currency, timezone, financial_year_start, cgst_rate, sgst_rate, igst_rate,
        default_making_charge_percentage, default_wastage_percentage, default_margin_percentage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'JJ Jewellers Tiruppur', 'retail', '2015-04-01', '45 Kumaran Road', 'Near Big Bazaar',
      'Tiruppur', 'Tamil Nadu', '641602', 'India', '+91-421-2345678', '+91-421-2345679',
      '<EMAIL>', 'www.jjjewellerstiruppur.com',
      '33**********1Z5', '**********', 'Indian Bank', 'Tiruppur Main Branch',
      '************', 'IDIB000T001', 'INR', 'Asia/Kolkata', 'april',
      1.50, 1.50, 3.00, 18.00, 10.00, 28.00
    ])

    // System Settings
    const systemSettings = [
      ['general', 'app_version', '3.0.0', 'string', 'Application version', true],
      ['general', 'store_location', 'tiruppur', 'string', 'Store location identifier', false],
      ['general', 'business_hours', '09:00-21:00', 'string', 'Business operating hours', false],
      ['general', 'invoice_template', 'tiruppur_premium', 'string', 'Invoice template for Tiruppur store', false],
      ['general', 'language_preference', 'tamil', 'string', 'Primary language preference', false],
      ['general', 'currency_display', 'INR', 'string', 'Currency display format', false],
      ['notification', 'sms_enabled', 'true', 'boolean', 'SMS notifications enabled', false],
      ['notification', 'whatsapp_enabled', 'true', 'boolean', 'WhatsApp notifications enabled', false],
      ['general', 'loyalty_program', 'true', 'boolean', 'Customer loyalty program', false],
      ['general', 'exchange_discount_gold', '2.5', 'number', 'Gold exchange discount percentage', false],
      ['general', 'exchange_discount_silver', '3.0', 'number', 'Silver exchange discount percentage', false],
      ['general', 'making_charge_gold', '18.0', 'number', 'Default gold making charge percentage', false],
      ['general', 'making_charge_silver', '15.0', 'number', 'Default silver making charge percentage', false],
      ['general', 'wastage_gold', '10.0', 'number', 'Default gold wastage percentage', false],
      ['general', 'wastage_silver', '8.0', 'number', 'Default silver wastage percentage', false]
    ]

    for (const [category, key, value, type, description, isSystem] of systemSettings) {
      await connection.execute(`
        INSERT INTO system_settings (setting_category, setting_key, setting_value, setting_type, description, is_system)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [category, key, value, type, description, isSystem])
    }

    // Professional Users for Tiruppur Store
    console.log('   Creating Tiruppur store staff accounts...')
    const users = [
      {
        id: randomUUID(),
        employee_code: 'TRP001',
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'Rajesh',
        last_name: 'Kumar',
        phone: '+91-**********',
        role: 'super_admin',
        department: 'management',
        designation: 'Store Owner',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 50.00,
        max_transaction_amount: ********.00,
        salary: 150000.00
      },
      {
        id: randomUUID(),
        employee_code: 'TRP002',
        username: 'manager',
        email: '<EMAIL>',
        first_name: 'Priya',
        last_name: 'Selvam',
        phone: '+91-**********',
        role: 'manager',
        department: 'management',
        designation: 'Store Manager',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 30.00,
        max_transaction_amount: 5000000.00,
        salary: 80000.00
      },
      {
        id: randomUUID(),
        employee_code: 'TRP003',
        username: 'sales_manager',
        email: '<EMAIL>',
        first_name: 'Murugan',
        last_name: 'Raman',
        phone: '+91-9876543212',
        role: 'sales_manager',
        department: 'sales',
        designation: 'Sales Manager',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 25.00,
        max_transaction_amount: 3000000.00,
        salary: 65000.00,
        commission_percentage: 2.50
      },
      {
        id: randomUUID(),
        employee_code: 'TRP004',
        username: 'sales1',
        email: '<EMAIL>',
        first_name: 'Kavitha',
        last_name: 'Devi',
        phone: '+91-9876543213',
        role: 'sales_staff',
        department: 'sales',
        designation: 'Senior Sales Executive',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 15.00,
        max_transaction_amount: 1500000.00,
        salary: 40000.00,
        commission_percentage: 2.00
      },
      {
        id: randomUUID(),
        employee_code: 'TRP005',
        username: 'sales2',
        email: '<EMAIL>',
        first_name: 'Senthil',
        last_name: 'Kumar',
        phone: '+91-**********',
        role: 'sales_staff',
        department: 'sales',
        designation: 'Sales Executive',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 10.00,
        max_transaction_amount: 1000000.00,
        salary: 35000.00,
        commission_percentage: 1.50
      },
      {
        id: randomUUID(),
        employee_code: 'TRP006',
        username: 'accountant',
        email: '<EMAIL>',
        first_name: 'Lakshmi',
        last_name: 'Narayanan',
        phone: '+91-**********',
        role: 'accountant',
        department: 'accounts',
        designation: 'Chief Accountant',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 0.00,
        max_transaction_amount: ********.00,
        salary: 55000.00
      },
      {
        id: randomUUID(),
        employee_code: 'TRP007',
        username: 'cashier1',
        email: '<EMAIL>',
        first_name: 'Meera',
        last_name: 'Bai',
        phone: '+91-**********',
        role: 'cashier',
        department: 'operations',
        designation: 'Senior Cashier',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 0.00,
        max_transaction_amount: 2000000.00,
        salary: 28000.00
      },
      {
        id: randomUUID(),
        employee_code: 'TRP008',
        username: 'cashier2',
        email: '<EMAIL>',
        first_name: 'Arjun',
        last_name: 'Prasad',
        phone: '+91-**********',
        role: 'cashier',
        department: 'operations',
        designation: 'Cashier',
        city: 'Tiruppur',
        state: 'Tamil Nadu',
        max_discount_percentage: 0.00,
        max_transaction_amount: 1500000.00,
        salary: 25000.00
      }
    ]

    for (const user of users) {
      await connection.execute(`
        INSERT INTO users (
          id, employee_code, username, email, password_hash, first_name, last_name, phone,
          role, department, designation, city, state, max_discount_percentage, max_transaction_amount,
          salary, commission_percentage, is_active, joining_date, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE, CURDATE(), NOW(), NOW())
      `, [
        user.id, user.employee_code, user.username, user.email, '$2b$10$defaulthash',
        user.first_name, user.last_name, user.phone, user.role, user.department,
        user.designation, user.city, user.state, user.max_discount_percentage,
        user.max_transaction_amount, user.salary, user.commission_percentage || 0.00
      ])
    }

    console.log(`   ✅ Created ${users.length} Tiruppur store staff accounts`)

    console.log('\n🎉 JJ Jewellers Tiruppur Database Setup Completed Successfully!')

  } catch (error) {
    console.error('\n❌ Tiruppur database setup failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the Tiruppur database setup
setupTiruppurDatabase().catch(console.error)
