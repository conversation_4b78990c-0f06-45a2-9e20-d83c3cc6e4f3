"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useStore } from "@/lib/store"
import { Category } from "@/lib/types"
import { Badge } from "@/components/ui/badge"
import { X, Plus } from "lucide-react"

interface CategoryFormProps {
  category?: Category
  onSubmit: () => void
  onCancel: () => void
}

export function CategoryForm({ category, onSubmit, onCancel }: CategoryFormProps) {
  const { addCategory, updateCategory } = useStore()
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    parentId: "",
    isActive: true,
    sortOrder: 0,
    image: "",
    tags: [] as string[],
    makingChargePercentage: 0,
    wastagePercentage: 0,
  })
  const [newTag, setNewTag] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name || "",
        description: category.description || "",
        parentId: category.parentId || "",
        isActive: category.isActive ?? true,
        sortOrder: category.sortOrder || 0,
        image: category.image || "",
        tags: category.tags || [],
        makingChargePercentage: category.makingChargePercentage || 0,
        wastagePercentage: category.wastagePercentage || 0,
      })
    }
  }, [category])

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag("")
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) {
      alert("Category name is required")
      return
    }

    setIsSubmitting(true)
    try {
      const categoryData = {
        ...formData,
        name: formData.name.trim(),
        description: formData.description.trim(),
        parentId: formData.parentId || undefined,
        updatedAt: new Date().toISOString(),
      }

      if (category) {
        await updateCategory(category.id, categoryData)
      } else {
        await addCategory({
          ...categoryData,
          id: `cat_${Date.now()}`,
          createdAt: new Date().toISOString(),
        })
      }
      onSubmit()
    } catch (error) {
      console.error("Error saving category:", error)
      alert("Error saving category. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Category Name */}
        <div className="space-y-2">
          <Label htmlFor="name">Category Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="e.g., Rings, Necklaces, Earrings"
            required
          />
        </div>

        {/* Parent Category */}
        <div className="space-y-2">
          <Label htmlFor="parentId">Parent Category</Label>
          <Select value={formData.parentId || undefined} onValueChange={(value) => handleInputChange("parentId", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select parent category (optional)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">No Parent (Top Level)</SelectItem>
              {/* This would be populated with existing categories */}
              <SelectItem value="jewelry">Jewelry</SelectItem>
              <SelectItem value="accessories">Accessories</SelectItem>
              <SelectItem value="precious-stones">Precious Stones</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder="Brief description of this category..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Making Charge Percentage */}
        <div className="space-y-2">
          <Label htmlFor="makingChargePercentage">Default Making Charge (%)</Label>
          <Input
            id="makingChargePercentage"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.makingChargePercentage}
            onChange={(e) => handleInputChange("makingChargePercentage", parseFloat(e.target.value) || 0)}
            placeholder="0.00"
          />
        </div>

        {/* Wastage Percentage */}
        <div className="space-y-2">
          <Label htmlFor="wastagePercentage">Default Wastage (%)</Label>
          <Input
            id="wastagePercentage"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.wastagePercentage}
            onChange={(e) => handleInputChange("wastagePercentage", parseFloat(e.target.value) || 0)}
            placeholder="0.00"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Sort Order */}
        <div className="space-y-2">
          <Label htmlFor="sortOrder">Sort Order</Label>
          <Input
            id="sortOrder"
            type="number"
            min="0"
            value={formData.sortOrder}
            onChange={(e) => handleInputChange("sortOrder", parseInt(e.target.value) || 0)}
            placeholder="0"
          />
          <p className="text-xs text-muted-foreground">Lower numbers appear first</p>
        </div>

        {/* Image URL */}
        <div className="space-y-2">
          <Label htmlFor="image">Category Image URL</Label>
          <Input
            id="image"
            type="url"
            value={formData.image}
            onChange={(e) => handleInputChange("image", e.target.value)}
            placeholder="https://example.com/category-image.jpg"
          />
        </div>
      </div>

      {/* Tags */}
      <div className="space-y-2">
        <Label>Tags</Label>
        <div className="flex gap-2 mb-2">
          <Input
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder="Add a tag..."
            onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
          />
          <Button type="button" onClick={addTag} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {formData.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              {tag}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => removeTag(tag)}
              />
            </Badge>
          ))}
        </div>
      </div>

      {/* Active Status */}
      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => handleInputChange("isActive", checked)}
        />
        <Label htmlFor="isActive">Active Category</Label>
        <p className="text-xs text-muted-foreground ml-2">
          Inactive categories won't appear in product forms
        </p>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : category ? "Update Category" : "Create Category"}
        </Button>
      </div>
    </form>
  )
}
