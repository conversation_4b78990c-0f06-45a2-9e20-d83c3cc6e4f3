"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Download, Calendar, TrendingUp, Scale, Users } from "lucide-react"
import { ExchangeTransaction } from "@/lib/types"
import { formatCurrency, formatDate } from "@/lib/utils"
import { calculateExchangeStats } from "@/lib/utils/exchange-utils"

interface ExchangeReportsProps {
  transactions: ExchangeTransaction[]
}

export function ExchangeReports({ transactions }: ExchangeReportsProps) {
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  })
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'customer'>('summary')
  const [filteredTransactions, setFilteredTransactions] = useState<ExchangeTransaction[]>([])

  useEffect(() => {
    // Filter transactions by date range
    const filtered = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.transactionDate)
      const startDate = new Date(dateRange.startDate)
      const endDate = new Date(dateRange.endDate)
      return transactionDate >= startDate && transactionDate <= endDate
    })
    setFilteredTransactions(filtered)
  }, [transactions, dateRange])

  const stats = calculateExchangeStats(filteredTransactions)

  const handleExportReport = () => {
    // Mock export functionality
    const reportData = {
      dateRange,
      stats,
      transactions: filteredTransactions
    }
    
    console.log('Exporting report:', reportData)
    alert('Report export functionality will be implemented with actual backend')
  }

  const getTopCustomers = () => {
    const customerStats = new Map()
    
    filteredTransactions.forEach(transaction => {
      if (transaction.customer) {
        const customerId = transaction.customer.id
        const existing = customerStats.get(customerId) || {
          customer: transaction.customer,
          totalAmount: 0,
          transactionCount: 0
        }
        
        existing.totalAmount += transaction.totalAmount
        existing.transactionCount += 1
        customerStats.set(customerId, existing)
      }
    })
    
    return Array.from(customerStats.values())
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 5)
  }

  const getDailyStats = () => {
    const dailyStats = new Map()
    
    filteredTransactions.forEach(transaction => {
      const date = transaction.transactionDate
      const existing = dailyStats.get(date) || {
        date,
        transactionCount: 0,
        totalAmount: 0,
        goldAmount: 0,
        silverAmount: 0
      }
      
      existing.transactionCount += 1
      existing.totalAmount += transaction.totalAmount
      
      transaction.items.forEach(item => {
        if (item.metalType === 'gold') {
          existing.goldAmount += item.amount
        } else {
          existing.silverAmount += item.amount
        }
      })
      
      dailyStats.set(date, existing)
    })
    
    return Array.from(dailyStats.values()).sort((a, b) => b.date.localeCompare(a.date))
  }

  return (
    <div className="space-y-6">
      {/* Report Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Exchange Reports
          </CardTitle>
          <CardDescription>
            Generate and view exchange transaction reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Start Date</Label>
              <Input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label>End Date</Label>
              <Input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label>Report Type</Label>
              <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                  <SelectItem value="customer">Customer Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button onClick={handleExportReport} className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
            <Scale className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
            <p className="text-xs text-muted-foreground">
              {filteredTransactions.filter(t => t.status === 'pending').length} pending
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(stats.averageTransactionValue)}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gold Exchange</CardTitle>
            <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.goldAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalGoldWeight.toFixed(3)}g total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Silver Exchange</CardTitle>
            <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.silverAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalSilverWeight.toFixed(3)}g total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Report Content */}
      {reportType === 'summary' && (
        <Card>
          <CardHeader>
            <CardTitle>Daily Summary</CardTitle>
            <CardDescription>
              Daily breakdown of exchange transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getDailyStats().map((day) => (
                <div key={day.date} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">{formatDate(day.date)}</p>
                    <p className="text-sm text-muted-foreground">
                      {day.transactionCount} transaction(s)
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(day.totalAmount)}</p>
                    <div className="flex gap-2 text-xs">
                      <span className="text-yellow-600">Gold: {formatCurrency(day.goldAmount)}</span>
                      <span className="text-gray-600">Silver: {formatCurrency(day.silverAmount)}</span>
                    </div>
                  </div>
                </div>
              ))}
              
              {getDailyStats().length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No transactions found for the selected date range
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {reportType === 'detailed' && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Transaction Report</CardTitle>
            <CardDescription>
              Complete list of all exchange transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredTransactions.map((transaction) => (
                <div key={transaction.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <p className="font-medium">{transaction.transactionNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(transaction.transactionDate)} • {transaction.customer?.name || 'Walk-in Customer'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{formatCurrency(transaction.totalAmount)}</p>
                      <Badge variant={transaction.status === 'completed' ? 'default' : 'secondary'}>
                        {transaction.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    {transaction.items.map((item, index) => (
                      <div key={index} className="text-sm bg-muted/50 p-2 rounded">
                        <div className="flex justify-between">
                          <span>{item.itemDescription} ({item.metalType} {item.purity})</span>
                          <span>{formatCurrency(item.amount)}</span>
                        </div>
                        <div className="text-muted-foreground">
                          {item.netWeight.toFixed(3)}g @ ₹{item.ratePerGram}/g
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              
              {filteredTransactions.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No transactions found for the selected date range
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {reportType === 'customer' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Top Customers
            </CardTitle>
            <CardDescription>
              Customers with highest exchange values
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getTopCustomers().map((customerStat, index) => (
                <div key={customerStat.customer.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{customerStat.customer.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {customerStat.customer.phone}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(customerStat.totalAmount)}</p>
                    <p className="text-sm text-muted-foreground">
                      {customerStat.transactionCount} transaction(s)
                    </p>
                  </div>
                </div>
              ))}
              
              {getTopCustomers().length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  No customer data found for the selected date range
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
