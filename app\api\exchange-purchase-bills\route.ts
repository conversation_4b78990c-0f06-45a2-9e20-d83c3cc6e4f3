import { NextRequest, NextResponse } from 'next/server'
import { exchangePurchaseBillService, exchangeService } from '@/lib/database/services'

export async function GET() {
  try {
    const bills = await exchangePurchaseBillService.findAll()
    return NextResponse.json(bills)
  } catch (error) {
    console.error('Error fetching exchange purchase bills:', error)
    return NextResponse.json(
      { error: 'Failed to fetch exchange purchase bills' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const { exchangeTransactionId, cgstRate = 1.5, sgstRate = 1.5 } = data
    
    // Get the exchange transaction first
    const exchangeTransaction = await exchangeService.findById(exchangeTransactionId)
    if (!exchangeTransaction) {
      return NextResponse.json(
        { error: 'Exchange transaction not found' },
        { status: 404 }
      )
    }

    const bill = await exchangePurchaseBillService.createFromExchangeTransaction(
      exchangeTransaction,
      cgstRate,
      sgstRate
    )
    
    return NextResponse.json(bill, { status: 201 })
  } catch (error) {
    console.error('Error creating exchange purchase bill:', error)
    return NextResponse.json(
      { error: 'Failed to create exchange purchase bill' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()
    const { id, paymentStatus } = data
    
    if (paymentStatus) {
      const success = await exchangePurchaseBillService.updatePaymentStatus(id, paymentStatus)
      if (success) {
        return NextResponse.json({ success: true })
      } else {
        return NextResponse.json(
          { error: 'Bill not found' },
          { status: 404 }
        )
      }
    }
    
    return NextResponse.json(
      { error: 'Invalid update data' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error updating exchange purchase bill:', error)
    return NextResponse.json(
      { error: 'Failed to update exchange purchase bill' },
      { status: 500 }
    )
  }
}
