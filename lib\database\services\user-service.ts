import { BaseService } from '../base-service'
import { User } from '../../types'
import bcrypt from 'bcryptjs'

export class UserService extends BaseService<User> {
  protected tableName = 'users'

  async create(data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    // Hash password if provided
    const userData = { ...data }
    if ('password' in userData && userData.password) {
      const passwordHash = await bcrypt.hash(userData.password as string, 10)
      delete userData.password
      ;(userData as any).passwordHash = passwordHash
    }

    return super.create(userData)
  }

  async findByEmail(email: string): Promise<User | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE email = ?`
    const rows = await this.executeQuery(sql, [email])
    
    if (rows.length === 0) return null
    return this.transformKeys(rows[0]) as User
  }

  async validatePassword(email: string, password: string): Promise<User | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE email = ?`
    const rows = await this.executeQuery(sql, [email])

    if (rows.length === 0) return null

    const user = rows[0] as any
    if (!user.password_hash) return null

    const isValid = await bcrypt.compare(password, user.password_hash)
    if (!isValid) return null

    // Remove password hash from returned user
    delete user.password_hash

    // Parse permissions JSON if it exists
    if (user.permissions && typeof user.permissions === 'string') {
      try {
        user.permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.warn('Failed to parse user permissions JSON:', error)
        user.permissions = null
      }
    }

    return this.transformKeys(user) as User
  }

  async updatePassword(id: string, newPassword: string): Promise<boolean> {
    const passwordHash = await bcrypt.hash(newPassword, 10)
    const sql = `UPDATE ${this.tableName} SET password_hash = ?, updated_at = ? WHERE id = ?`
    const result = await this.executeUpdate(sql, [passwordHash, new Date().toISOString(), id])
    return result.affectedRows > 0
  }

  async findByRole(role: string): Promise<User[]> {
    return this.findAll({ role })
  }

  // Override to exclude password hash from results
  async findAll(conditions: Record<string, any> = {}): Promise<User[]> {
    const { sql: whereClause, params } = this.buildWhereClause(conditions)
    const sql = `SELECT id, name, email, role, permissions, created_at, updated_at FROM ${this.tableName} ${whereClause} ORDER BY created_at DESC`

    const rows = await this.executeQuery(sql, params)
    return rows.map(row => {
      const user = row as any
      // Parse permissions JSON if it exists
      if (user.permissions && typeof user.permissions === 'string') {
        try {
          user.permissions = JSON.parse(user.permissions)
        } catch (error) {
          console.warn('Failed to parse user permissions JSON:', error)
          user.permissions = null
        }
      }
      return this.transformKeys(user) as User
    })
  }

  async findById(id: string): Promise<User | null> {
    const sql = `SELECT id, name, email, role, permissions, created_at, updated_at FROM ${this.tableName} WHERE id = ?`
    const rows = await this.executeQuery(sql, [id])

    if (rows.length === 0) return null

    const user = rows[0] as any
    // Parse permissions JSON if it exists
    if (user.permissions && typeof user.permissions === 'string') {
      try {
        user.permissions = JSON.parse(user.permissions)
      } catch (error) {
        console.warn('Failed to parse user permissions JSON:', error)
        user.permissions = null
      }
    }

    return this.transformKeys(user) as User
  }
}
