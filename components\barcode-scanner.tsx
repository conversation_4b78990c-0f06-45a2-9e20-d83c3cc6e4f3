"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { QrCode, Search, Package, Plus } from "lucide-react"
import { useStore } from "@/lib/store"

export function BarcodeScanner() {
  const { inventory, addInventoryItem } = useStore()
  const [scannedCode, setScannedCode] = useState("")
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isGenerating, setIsGenerating] = useState(false)

  const handleScan = () => {
    if (!scannedCode.trim()) {
      alert("Please enter a barcode to scan")
      return
    }

    // Search for items with matching barcode
    const results = inventory.filter(
      (item) =>
        item.id.toLowerCase().includes(scannedCode.toLowerCase()) ||
        item.name.toLowerCase().includes(scannedCode.toLowerCase()),
    )

    setSearchResults(results)
  }

  const generateBarcode = (item: any) => {
    setIsGenerating(true)

    // Simulate barcode generation
    setTimeout(() => {
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")

      if (ctx) {
        canvas.width = 300
        canvas.height = 100

        // Draw barcode background
        ctx.fillStyle = "#ffffff"
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        // Draw barcode lines
        ctx.fillStyle = "#000000"
        for (let i = 0; i < 50; i++) {
          const x = i * 6
          const height = Math.random() * 60 + 20
          ctx.fillRect(x, 20, Math.random() * 3 + 1, height)
        }

        // Draw item code
        ctx.font = "12px Arial"
        ctx.textAlign = "center"
        ctx.fillText(item.id, canvas.width / 2, 95)

        // Download barcode
        const link = document.createElement("a")
        link.download = `barcode-${item.id}.png`
        link.href = canvas.toDataURL()
        link.click()
      }

      setIsGenerating(false)
    }, 1000)
  }

  const clearResults = () => {
    setScannedCode("")
    setSearchResults([])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Barcode Scanner
          </CardTitle>
          <CardDescription>Scan or search for inventory items using barcodes</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="barcode">Barcode / Item Code</Label>
              <Input
                id="barcode"
                value={scannedCode}
                onChange={(e) => setScannedCode(e.target.value)}
                placeholder="Enter barcode or item code"
                onKeyPress={(e) => e.key === "Enter" && handleScan()}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleScan}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" onClick={clearResults}>
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Search Results
              <Badge variant="secondary">{searchResults.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {searchResults.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium">{item.name}</h4>
                      <Badge variant="outline">{item.id}</Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                      <div>
                        <span className="font-medium">Category:</span> {item.category}
                      </div>
                      <div>
                        <span className="font-medium">Weight:</span> {item.netWeight}g
                      </div>
                      <div>
                        <span className="font-medium">Stock:</span> {item.stock}
                      </div>
                      <div>
                        <span className="font-medium">Value:</span> ₹{item.currentValue.toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => generateBarcode(item)} disabled={isGenerating}>
                      {isGenerating ? "Generating..." : "Generate Barcode"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Barcode Management</CardTitle>
          <CardDescription>Generate and manage barcodes for your inventory</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Bulk Barcode Generation</h4>
              <p className="text-sm text-muted-foreground mb-3">Generate barcodes for all inventory items at once</p>
              <Button variant="outline" className="w-full bg-transparent">
                <Plus className="h-4 w-4 mr-2" />
                Generate All Barcodes
              </Button>
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">Barcode Settings</h4>
              <p className="text-sm text-muted-foreground mb-3">Configure barcode format and printing options</p>
              <Button variant="outline" className="w-full bg-transparent">
                <QrCode className="h-4 w-4 mr-2" />
                Configure Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
