import { BaseService } from '../base-service'
import { ExchangeAuditTrail } from '../../types'
import { RowDataPacket } from 'mysql2'

export class ExchangeAuditService extends BaseService<ExchangeAuditTrail> {
  protected tableName = 'exchange_audit_trail'

  // Create audit trail entry
  async createAuditEntry(
    exchangeTransactionId: string,
    actionType: 'created' | 'updated' | 'billed' | 'voucher_generated' | 'used_in_sale' | 'cancelled',
    actionDescription: string,
    oldValues?: any,
    newValues?: any,
    relatedBillId?: string,
    relatedSaleId?: string,
    performedBy?: string
  ): Promise<ExchangeAuditTrail> {
    const auditData = {
      id: this.generateId(),
      exchangeTransactionId,
      actionType,
      actionDescription,
      oldValues: oldValues ? JSON.stringify(oldValues) : null,
      newValues: newValues ? JSON.stringify(newValues) : null,
      relatedBillId,
      relatedSaleId,
      performedBy,
      performedAt: new Date().toISOString()
    }

    const transformedData = this.transformKeysToSnake(auditData)
    const keys = Object.keys(transformedData)
    const values = Object.values(transformedData)
    const placeholders = keys.map(() => '?').join(', ')

    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`
    await this.executeUpdate(sql, values)

    return auditData as ExchangeAuditTrail
  }

  // Get audit trail for a specific exchange transaction
  async getAuditTrail(exchangeTransactionId: string): Promise<ExchangeAuditTrail[]> {
    const sql = `
      SELECT eat.*, u.name as performed_by_name
      FROM ${this.tableName} eat
      LEFT JOIN users u ON eat.performed_by = u.id
      WHERE eat.exchange_transaction_id = ?
      ORDER BY eat.performed_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [exchangeTransactionId])
    return rows.map(row => {
      const audit = this.transformKeys(row) as ExchangeAuditTrail
      // Parse JSON fields
      if (audit.oldValues && typeof audit.oldValues === 'string') {
        try {
          audit.oldValues = JSON.parse(audit.oldValues as string)
        } catch (e) {
          // Keep as string if parsing fails
        }
      }
      if (audit.newValues && typeof audit.newValues === 'string') {
        try {
          audit.newValues = JSON.parse(audit.newValues as string)
        } catch (e) {
          // Keep as string if parsing fails
        }
      }
      return audit
    })
  }

  // Get audit trail by date range
  async getAuditTrailByDateRange(startDate: string, endDate: string): Promise<ExchangeAuditTrail[]> {
    const sql = `
      SELECT eat.*, et.transaction_number, u.name as performed_by_name
      FROM ${this.tableName} eat
      JOIN exchange_transactions et ON eat.exchange_transaction_id = et.id
      LEFT JOIN users u ON eat.performed_by = u.id
      WHERE DATE(eat.performed_at) BETWEEN ? AND ?
      ORDER BY eat.performed_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [startDate, endDate])
    return rows.map(row => this.transformKeys(row) as ExchangeAuditTrail)
  }

  // Get audit trail by action type
  async getAuditTrailByAction(actionType: string): Promise<ExchangeAuditTrail[]> {
    const sql = `
      SELECT eat.*, et.transaction_number, u.name as performed_by_name
      FROM ${this.tableName} eat
      JOIN exchange_transactions et ON eat.exchange_transaction_id = et.id
      LEFT JOIN users u ON eat.performed_by = u.id
      WHERE eat.action_type = ?
      ORDER BY eat.performed_at DESC
    `
    const rows = await this.executeQuery<RowDataPacket[]>(sql, [actionType])
    return rows.map(row => this.transformKeys(row) as ExchangeAuditTrail)
  }

  // Get comprehensive exchange transaction history
  async getExchangeTransactionHistory(exchangeTransactionId: string): Promise<{
    transaction: any
    auditTrail: ExchangeAuditTrail[]
    purchaseBill?: any
    salesUsage?: any[]
    vouchers?: any[]
  }> {
    // Get transaction details
    const transactionSql = `
      SELECT et.*, c.name as customer_name, c.phone as customer_phone
      FROM exchange_transactions et
      LEFT JOIN customers c ON et.customer_id = c.id
      WHERE et.id = ?
    `
    const transactionRows = await this.executeQuery<RowDataPacket[]>(transactionSql, [exchangeTransactionId])
    const transaction = transactionRows.length > 0 ? this.transformKeys(transactionRows[0]) : null

    // Get audit trail
    const auditTrail = await this.getAuditTrail(exchangeTransactionId)

    // Get purchase bill if exists
    const billSql = `
      SELECT * FROM exchange_purchase_bills 
      WHERE exchange_transaction_id = ?
    `
    const billRows = await this.executeQuery<RowDataPacket[]>(billSql, [exchangeTransactionId])
    const purchaseBill = billRows.length > 0 ? this.transformKeys(billRows[0]) : null

    // Get sales usage
    const salesSql = `
      SELECT sei.*, s.invoice_number, s.sale_date, s.total_amount as sale_total
      FROM sales_exchange_items sei
      JOIN sales s ON sei.sale_id = s.id
      WHERE sei.exchange_transaction_id = ?
    `
    const salesRows = await this.executeQuery<RowDataPacket[]>(salesSql, [exchangeTransactionId])
    const salesUsage = salesRows.map(row => this.transformKeys(row))

    // Get vouchers
    const voucherSql = `
      SELECT * FROM exchange_vouchers 
      WHERE exchange_transaction_id = ?
    `
    const voucherRows = await this.executeQuery<RowDataPacket[]>(voucherSql, [exchangeTransactionId])
    const vouchers = voucherRows.map(row => this.transformKeys(row))

    return {
      transaction,
      auditTrail,
      purchaseBill,
      salesUsage,
      vouchers
    }
  }

  // Generate compliance report
  async generateComplianceReport(startDate: string, endDate: string): Promise<{
    totalExchanges: number
    totalValue: number
    billsGenerated: number
    salesIntegrated: number
    auditEntries: number
    complianceScore: number
    issues: string[]
  }> {
    // Get exchange statistics
    const exchangeStatsSql = `
      SELECT 
        COUNT(*) as total_exchanges,
        SUM(total_amount) as total_value
      FROM exchange_transactions
      WHERE transaction_date BETWEEN ? AND ?
        AND status = 'completed'
    `
    const exchangeStats = await this.executeQuery<RowDataPacket[]>(exchangeStatsSql, [startDate, endDate])
    const { total_exchanges, total_value } = exchangeStats[0] || { total_exchanges: 0, total_value: 0 }

    // Get bills generated
    const billsStatsSql = `
      SELECT COUNT(*) as bills_generated
      FROM exchange_purchase_bills
      WHERE bill_date BETWEEN ? AND ?
    `
    const billsStats = await this.executeQuery<RowDataPacket[]>(billsStatsSql, [startDate, endDate])
    const { bills_generated } = billsStats[0] || { bills_generated: 0 }

    // Get sales integration
    const salesStatsSql = `
      SELECT COUNT(DISTINCT sei.sale_id) as sales_integrated
      FROM sales_exchange_items sei
      JOIN sales s ON sei.sale_id = s.id
      WHERE s.sale_date BETWEEN ? AND ?
    `
    const salesStats = await this.executeQuery<RowDataPacket[]>(salesStatsSql, [startDate, endDate])
    const { sales_integrated } = salesStats[0] || { sales_integrated: 0 }

    // Get audit entries
    const auditStatsSql = `
      SELECT COUNT(*) as audit_entries
      FROM ${this.tableName}
      WHERE DATE(performed_at) BETWEEN ? AND ?
    `
    const auditStats = await this.executeQuery<RowDataPacket[]>(auditStatsSql, [startDate, endDate])
    const { audit_entries } = auditStats[0] || { audit_entries: 0 }

    // Calculate compliance score and identify issues
    const issues: string[] = []
    let complianceScore = 100

    // Check if all completed exchanges have bills
    const unbilledExchangesSql = `
      SELECT COUNT(*) as unbilled_count
      FROM exchange_transactions et
      LEFT JOIN exchange_purchase_bills epb ON et.id = epb.exchange_transaction_id
      WHERE et.transaction_date BETWEEN ? AND ?
        AND et.status = 'completed'
        AND epb.id IS NULL
    `
    const unbilledStats = await this.executeQuery<RowDataPacket[]>(unbilledExchangesSql, [startDate, endDate])
    const { unbilled_count } = unbilledStats[0] || { unbilled_count: 0 }

    if (unbilled_count > 0) {
      issues.push(`${unbilled_count} completed exchanges without purchase bills`)
      complianceScore -= 20
    }

    // Check for exchanges without audit trails
    const unauditedExchangesSql = `
      SELECT COUNT(*) as unaudited_count
      FROM exchange_transactions et
      LEFT JOIN ${this.tableName} eat ON et.id = eat.exchange_transaction_id
      WHERE et.transaction_date BETWEEN ? AND ?
        AND eat.id IS NULL
    `
    const unauditedStats = await this.executeQuery<RowDataPacket[]>(unauditedExchangesSql, [startDate, endDate])
    const { unaudited_count } = unauditedStats[0] || { unaudited_count: 0 }

    if (unaudited_count > 0) {
      issues.push(`${unaudited_count} exchanges without audit trails`)
      complianceScore -= 15
    }

    return {
      totalExchanges: total_exchanges,
      totalValue: total_value,
      billsGenerated: bills_generated,
      salesIntegrated: sales_integrated,
      auditEntries: audit_entries,
      complianceScore: Math.max(0, complianceScore),
      issues
    }
  }

  // Link exchange to sale
  async linkExchangeToSale(
    exchangeTransactionId: string,
    saleId: string,
    deductionAmount: number,
    performedBy?: string
  ): Promise<void> {
    await this.createAuditEntry(
      exchangeTransactionId,
      'used_in_sale',
      `Exchange used in sale with deduction of ₹${deductionAmount}`,
      null,
      { saleId, deductionAmount },
      undefined,
      saleId,
      performedBy
    )
  }

  // Mark exchange as billed
  async markExchangeAsBilled(
    exchangeTransactionId: string,
    billId: string,
    billNumber: string,
    performedBy?: string
  ): Promise<void> {
    await this.createAuditEntry(
      exchangeTransactionId,
      'billed',
      `Purchase bill ${billNumber} generated`,
      null,
      { billId, billNumber },
      billId,
      undefined,
      performedBy
    )
  }

  // Get exchange utilization summary
  async getExchangeUtilization(): Promise<{
    totalExchanges: number
    totalValue: number
    billedAmount: number
    usedInSales: number
    remainingValue: number
    utilizationPercentage: number
  }> {
    const sql = `
      SELECT 
        COUNT(et.id) as total_exchanges,
        SUM(et.total_amount) as total_value,
        SUM(CASE WHEN epb.id IS NOT NULL THEN et.total_amount ELSE 0 END) as billed_amount,
        SUM(COALESCE(sei_sum.total_deduction, 0)) as used_in_sales
      FROM exchange_transactions et
      LEFT JOIN exchange_purchase_bills epb ON et.id = epb.exchange_transaction_id
      LEFT JOIN (
        SELECT 
          exchange_transaction_id,
          SUM(deduction_amount) as total_deduction
        FROM sales_exchange_items
        GROUP BY exchange_transaction_id
      ) sei_sum ON et.id = sei_sum.exchange_transaction_id
      WHERE et.status = 'completed'
    `
    
    const result = await this.executeQuery<RowDataPacket[]>(sql)
    const stats = result[0] || {}
    
    const totalValue = stats.total_value || 0
    const usedInSales = stats.used_in_sales || 0
    const remainingValue = totalValue - usedInSales
    const utilizationPercentage = totalValue > 0 ? (usedInSales / totalValue) * 100 : 0

    return {
      totalExchanges: stats.total_exchanges || 0,
      totalValue,
      billedAmount: stats.billed_amount || 0,
      usedInSales,
      remainingValue,
      utilizationPercentage
    }
  }
}
