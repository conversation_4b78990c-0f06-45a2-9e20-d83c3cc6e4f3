#!/usr/bin/env tsx

// Load environment variables FIRST
import { config } from 'dotenv'
import { resolve } from 'path'
config({ path: resolve(process.cwd(), '.env.local') })

import mysql from 'mysql2/promise'
import { randomUUID } from 'crypto'

async function createCompleteProfessionalDirect() {
  console.log('🚀 Creating Complete Professional Jewelry Management System - Direct Implementation...\n')
  
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jjjewellers_db',
  }

  let connection: mysql.Connection | null = null

  try {
    console.log('📡 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database\n')

    // Step 1: Clean database recreation
    console.log('🗑️  Step 1: Clean database recreation...')
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0')
    await connection.execute(`DROP DATABASE IF EXISTS ${dbConfig.database}`)
    console.log('   ✅ Dropped existing database')
    
    await connection.execute(`CREATE DATABASE ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
    console.log('   ✅ Created fresh database with proper charset')
    
    // Reconnect to the new database
    await connection.end()
    connection = await mysql.createConnection({
      ...dbConfig,
      database: dbConfig.database
    })
    
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1')

    // Step 2: Create core tables directly
    console.log('\n🏗️  Step 2: Creating core professional tables...')
    
    // 1. Business Settings
    console.log('   Creating business_settings table...')
    await connection.execute(`
      CREATE TABLE business_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        business_name VARCHAR(255) NOT NULL,
        business_type ENUM('retail', 'wholesale', 'manufacturing', 'mixed') DEFAULT 'retail',
        business_registration_number VARCHAR(100),
        establishment_date DATE,
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Contact Information
        phone VARCHAR(20),
        alternate_phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        
        -- Legal Information
        gst_number VARCHAR(50),
        pan_number VARCHAR(20),
        tan_number VARCHAR(20),
        license_number VARCHAR(100),
        
        -- Banking Information
        bank_name VARCHAR(255),
        bank_branch VARCHAR(255),
        bank_account_number VARCHAR(50),
        bank_ifsc VARCHAR(20),
        
        -- Business Configuration
        logo_url VARCHAR(500),
        currency VARCHAR(10) DEFAULT 'INR',
        timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
        financial_year_start ENUM('april', 'january') DEFAULT 'april',
        
        -- Tax Configuration
        cgst_rate DECIMAL(5,2) DEFAULT 1.50,
        sgst_rate DECIMAL(5,2) DEFAULT 1.50,
        igst_rate DECIMAL(5,2) DEFAULT 3.00,
        
        -- Business Rules
        allow_negative_stock BOOLEAN DEFAULT FALSE,
        auto_generate_barcodes BOOLEAN DEFAULT TRUE,
        enable_loyalty_program BOOLEAN DEFAULT TRUE,
        
        -- Pricing Configuration
        default_making_charge_percentage DECIMAL(5,2) DEFAULT 15.00,
        default_wastage_percentage DECIMAL(5,2) DEFAULT 8.00,
        default_margin_percentage DECIMAL(5,2) DEFAULT 25.00,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // 2. System Settings
    console.log('   Creating system_settings table...')
    await connection.execute(`
      CREATE TABLE system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_category ENUM('general', 'security', 'notification', 'integration', 'reporting') DEFAULT 'general',
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json', 'date', 'time', 'datetime') DEFAULT 'string',
        description TEXT,
        is_system BOOLEAN DEFAULT FALSE,
        is_encrypted BOOLEAN DEFAULT FALSE,
        updated_by VARCHAR(36),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_setting_category (setting_category),
        INDEX idx_setting_key (setting_key),
        INDEX idx_is_system (is_system)
      )
    `)

    // 3. Users
    console.log('   Creating users table...')
    await connection.execute(`
      CREATE TABLE users (
        id VARCHAR(36) PRIMARY KEY,
        employee_code VARCHAR(50) UNIQUE,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        
        -- Personal Information
        title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
        first_name VARCHAR(100) NOT NULL,
        middle_name VARCHAR(100),
        last_name VARCHAR(100) NOT NULL,
        date_of_birth DATE,
        gender ENUM('male', 'female', 'other'),
        
        -- Contact Information
        phone VARCHAR(20),
        alternate_phone VARCHAR(20),
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Employment Information
        role ENUM('super_admin', 'admin', 'manager', 'sales_manager', 'sales_staff', 'accountant', 'cashier', 'security', 'viewer') NOT NULL DEFAULT 'sales_staff',
        department ENUM('management', 'sales', 'accounts', 'operations', 'security', 'support') DEFAULT 'sales',
        designation VARCHAR(100),
        joining_date DATE,
        salary DECIMAL(12,2),
        commission_percentage DECIMAL(5,2) DEFAULT 0.00,
        
        -- System Access
        permissions JSON,
        max_discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        max_transaction_amount DECIMAL(15,2) DEFAULT 0.00,
        
        -- Security
        is_active BOOLEAN DEFAULT TRUE,
        is_verified BOOLEAN DEFAULT FALSE,
        last_login TIMESTAMP NULL,
        login_attempts INT DEFAULT 0,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_employee_code (employee_code),
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_department (department),
        INDEX idx_is_active (is_active)
      )
    `)

    // 4. Customers
    console.log('   Creating customers table...')
    await connection.execute(`
      CREATE TABLE customers (
        id VARCHAR(36) PRIMARY KEY,
        customer_code VARCHAR(50) UNIQUE NOT NULL,
        customer_type ENUM('individual', 'business', 'corporate') DEFAULT 'individual',
        
        -- Personal Information
        title ENUM('Mr', 'Mrs', 'Ms', 'Dr', 'Prof') NULL,
        first_name VARCHAR(100) NOT NULL,
        middle_name VARCHAR(100),
        last_name VARCHAR(100),
        business_name VARCHAR(255),
        date_of_birth DATE,
        anniversary_date DATE,
        gender ENUM('male', 'female', 'other'),
        occupation VARCHAR(100),
        
        -- Contact Information
        phone VARCHAR(20) NOT NULL,
        alternate_phone VARCHAR(20),
        email VARCHAR(255),
        whatsapp_number VARCHAR(20),
        
        -- Address Information
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100) DEFAULT 'India',
        
        -- Legal Information
        gst_number VARCHAR(50),
        pan_number VARCHAR(20),
        aadhar_number VARCHAR(20),
        
        -- Business Information
        credit_limit DECIMAL(15,2) DEFAULT 0.00,
        credit_days INT DEFAULT 0,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        
        -- Financial Tracking
        total_purchases DECIMAL(15,2) DEFAULT 0.00,
        total_outstanding DECIMAL(15,2) DEFAULT 0.00,
        total_payments DECIMAL(15,2) DEFAULT 0.00,
        last_payment_date DATE,
        
        -- Loyalty Program
        loyalty_points INT DEFAULT 0,
        loyalty_tier ENUM('bronze', 'silver', 'gold', 'platinum', 'diamond') DEFAULT 'bronze',
        membership_number VARCHAR(50),
        membership_date DATE,
        
        -- Preferences
        preferred_contact ENUM('phone', 'email', 'sms', 'whatsapp') DEFAULT 'phone',
        preferred_language ENUM('english', 'hindi', 'regional') DEFAULT 'english',
        preferred_metal ENUM('gold', 'silver', 'platinum', 'diamond') DEFAULT 'gold',
        
        -- Additional Information
        notes TEXT,
        special_instructions TEXT,
        referral_source VARCHAR(255),
        referred_by VARCHAR(36),
        
        -- Status
        is_active BOOLEAN DEFAULT TRUE,
        is_vip BOOLEAN DEFAULT FALSE,
        is_blacklisted BOOLEAN DEFAULT FALSE,
        blacklist_reason TEXT,
        last_visit TIMESTAMP NULL,
        visit_count INT DEFAULT 0,
        
        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_customer_code (customer_code),
        INDEX idx_customer_type (customer_type),
        INDEX idx_phone (phone),
        INDEX idx_email (email),
        INDEX idx_business_name (business_name),
        INDEX idx_city (city),
        INDEX idx_loyalty_tier (loyalty_tier),
        INDEX idx_is_active (is_active),
        INDEX idx_is_vip (is_vip),
        INDEX idx_last_visit (last_visit)
      )
    `)

    // 5. Categories
    console.log('   Creating categories table...')
    await connection.execute(`
      CREATE TABLE categories (
        id VARCHAR(36) PRIMARY KEY,
        category_code VARCHAR(50) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        parent_id VARCHAR(36),
        category_level INT DEFAULT 1,

        -- Business Configuration
        hsn_code VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,

        -- Visual
        image_url VARCHAR(500),

        -- Pricing Configuration
        making_charge_type ENUM('percentage', 'fixed', 'per_gram', 'per_piece') DEFAULT 'percentage',
        making_charge_value DECIMAL(10,2) DEFAULT 0.00,
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        margin_percentage DECIMAL(5,2) DEFAULT 0.00,

        -- Business Rules
        requires_hallmarking BOOLEAN DEFAULT FALSE,
        requires_certification BOOLEAN DEFAULT FALSE,
        allows_customization BOOLEAN DEFAULT TRUE,
        minimum_weight DECIMAL(8,3) DEFAULT 0.000,
        maximum_weight DECIMAL(8,3) DEFAULT 999.999,

        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_category_code (category_code),
        INDEX idx_name (name),
        INDEX idx_parent_id (parent_id),
        INDEX idx_hsn_code (hsn_code),
        INDEX idx_is_active (is_active),
        INDEX idx_sort_order (sort_order)
      )
    `)

    // 6. Metal Rates
    console.log('   Creating metal_rates table...')
    await connection.execute(`
      CREATE TABLE metal_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20) NOT NULL,

        -- Pricing Information
        rate_per_gram DECIMAL(12,2) NOT NULL,
        rate_per_10gram DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 10) STORED,
        rate_per_tola DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 11.664) STORED,
        rate_per_ounce DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * 31.1035) STORED,

        -- Validity
        effective_date DATE NOT NULL,
        effective_time TIME DEFAULT '00:00:00',
        expiry_date DATE,
        is_active BOOLEAN DEFAULT TRUE,

        -- Source Information
        source ENUM('manual', 'api', 'import', 'market') DEFAULT 'manual',
        source_reference VARCHAR(255),

        -- Market Information
        opening_rate DECIMAL(12,2),
        closing_rate DECIMAL(12,2),
        high_rate DECIMAL(12,2),
        low_rate DECIMAL(12,2),
        change_amount DECIMAL(12,2),
        change_percentage DECIMAL(5,2),

        -- Additional Information
        notes TEXT,
        currency VARCHAR(10) DEFAULT 'INR',

        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_active_rate (metal_type, purity, effective_date, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date),
        INDEX idx_is_active (is_active),
        INDEX idx_source (source)
      )
    `)

    // 7. Exchange Rates
    console.log('   Creating exchange_rates table...')
    await connection.execute(`
      CREATE TABLE exchange_rates (
        id VARCHAR(36) PRIMARY KEY,
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20) NOT NULL,

        -- Exchange Pricing
        rate_per_gram DECIMAL(12,2) NOT NULL,
        discount_percentage DECIMAL(5,2) DEFAULT 0.00,
        effective_rate DECIMAL(12,2) GENERATED ALWAYS AS (rate_per_gram * (100 - discount_percentage) / 100) STORED,

        -- Validity
        effective_date DATE NOT NULL,
        effective_time TIME DEFAULT '00:00:00',
        expiry_date DATE,
        is_active BOOLEAN DEFAULT TRUE,

        -- Source Information
        source ENUM('manual', 'derived', 'import') DEFAULT 'derived',
        base_metal_rate_id VARCHAR(36),

        -- Conditions
        minimum_weight DECIMAL(8,3) DEFAULT 0.000,
        maximum_weight DECIMAL(8,3) DEFAULT 999.999,

        -- Additional Information
        notes TEXT,

        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        UNIQUE KEY unique_active_exchange_rate (metal_type, purity, effective_date, is_active),
        INDEX idx_metal_purity (metal_type, purity),
        INDEX idx_effective_date (effective_date),
        INDEX idx_is_active (is_active),
        INDEX idx_effective_rate (effective_rate)
      )
    `)

    // 8. Inventory
    console.log('   Creating inventory table...')
    await connection.execute(`
      CREATE TABLE inventory (
        id VARCHAR(36) PRIMARY KEY,
        item_code VARCHAR(100) UNIQUE NOT NULL,
        barcode VARCHAR(100) UNIQUE,

        -- Basic Information
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id VARCHAR(36),
        brand VARCHAR(100),

        -- Metal Information
        metal_type ENUM('gold', 'silver', 'platinum', 'palladium', 'copper', 'brass', 'other') NOT NULL,
        purity VARCHAR(20),
        hallmark_number VARCHAR(100),

        -- Weight Information
        gross_weight DECIMAL(10,3) DEFAULT 0.000,
        stone_weight DECIMAL(10,3) DEFAULT 0.000,
        net_weight DECIMAL(10,3) GENERATED ALWAYS AS (gross_weight - stone_weight) STORED,
        diamond_weight DECIMAL(8,3) DEFAULT 0.000,

        -- Wastage Calculation
        wastage_percentage DECIMAL(5,2) DEFAULT 0.00,
        wastage_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight * wastage_percentage / 100) STORED,
        chargeable_weight DECIMAL(8,3) GENERATED ALWAYS AS (net_weight + wastage_weight) STORED,

        -- Stone Information
        diamond_pieces INT DEFAULT 0,
        colored_stone_pieces INT DEFAULT 0,
        stone_details JSON,

        -- Design Information
        size VARCHAR(50),
        gender ENUM('male', 'female', 'unisex', 'kids') DEFAULT 'unisex',
        occasion VARCHAR(100),
        design_number VARCHAR(100),

        -- Pricing Information
        purchase_rate DECIMAL(12,2) DEFAULT 0.00,
        metal_amount DECIMAL(15,2) GENERATED ALWAYS AS (chargeable_weight * purchase_rate) STORED,
        making_charges DECIMAL(12,2) DEFAULT 0.00,
        stone_charges DECIMAL(12,2) DEFAULT 0.00,
        other_charges DECIMAL(12,2) DEFAULT 0.00,
        total_cost DECIMAL(15,2) GENERATED ALWAYS AS (metal_amount + making_charges + stone_charges + other_charges) STORED,

        -- Selling Price Calculation
        margin_percentage DECIMAL(5,2) DEFAULT 0.00,
        selling_price DECIMAL(12,2) DEFAULT 0.00,
        mrp DECIMAL(12,2) DEFAULT 0.00,

        -- Stock Information
        stock_quantity INT DEFAULT 1,
        reserved_quantity INT DEFAULT 0,
        available_quantity INT GENERATED ALWAYS AS (stock_quantity - reserved_quantity) STORED,
        min_stock_level INT DEFAULT 0,

        -- Location Information
        location VARCHAR(100),

        -- Status Information
        status ENUM('active', 'sold', 'reserved', 'damaged', 'repair', 'lost', 'stolen', 'inactive') DEFAULT 'active',
        condition_status ENUM('new', 'excellent', 'good', 'fair', 'poor') DEFAULT 'new',

        -- Additional Information
        images JSON,
        tags JSON,

        -- Compliance Information
        hsn_code VARCHAR(20),
        country_of_origin VARCHAR(100) DEFAULT 'India',
        requires_hallmarking BOOLEAN DEFAULT FALSE,
        is_hallmarked BOOLEAN DEFAULT FALSE,

        -- Business Rules
        is_customizable BOOLEAN DEFAULT FALSE,
        is_returnable BOOLEAN DEFAULT TRUE,
        return_days INT DEFAULT 7,
        warranty_period_months INT DEFAULT 0,

        -- Audit
        created_by VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_item_code (item_code),
        INDEX idx_barcode (barcode),
        INDEX idx_name (name),
        INDEX idx_category (category_id),
        INDEX idx_metal_type (metal_type),
        INDEX idx_purity (purity),
        INDEX idx_status (status),
        INDEX idx_location (location),
        INDEX idx_selling_price (selling_price),
        INDEX idx_stock_quantity (stock_quantity),
        INDEX idx_created_at (created_at)
      )
    `)

    console.log('\n🎉 Complete Professional Database Schema Created Successfully!')

    // Step 3: Seed comprehensive sample data
    console.log('\n🌱 Step 3: Seeding comprehensive sample data...')

    // Business Settings
    console.log('   Setting up business configuration...')
    await connection.execute(`
      INSERT INTO business_settings (
        business_name, business_type, address_line1, address_line2, city, state,
        postal_code, country, phone, email, website, gst_number, pan_number,
        bank_name, bank_account_number, bank_ifsc, currency, timezone,
        financial_year_start, cgst_rate, sgst_rate, igst_rate,
        default_making_charge_percentage, default_wastage_percentage, default_margin_percentage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'JJ Jewellers Premium Collection', 'retail', '123 Gold Street', 'Jewelry Market Complex',
      'Mumbai', 'Maharashtra', '400001', 'India', '+91-22-********',
      '<EMAIL>', 'www.jjjewellers.com', '27**********1Z5',
      '**********', 'HDFC Bank', '********901234', 'HDFC0001234',
      'INR', 'Asia/Kolkata', 'april', 1.50, 1.50, 3.00, 15.00, 8.00, 25.00
    ])

    console.log('\n🎉 Complete Professional System Implementation Successful!')

  } catch (error) {
    console.error('\n❌ Complete professional creation failed:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n📡 Database connection closed')
    }
  }
}

// Run the complete professional creation
createCompleteProfessionalDirect().catch(console.error)
