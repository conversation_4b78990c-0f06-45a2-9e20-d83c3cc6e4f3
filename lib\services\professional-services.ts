// Professional Services Layer for Jewelry Management System
// Comprehensive business logic and data access layer

import { DatabaseConnection } from './database'
import { ValidationError, BusinessLogicError } from '../utils/errors'
import { randomUUID } from 'crypto'

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

export interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'super_admin' | 'admin' | 'manager' | 'sales_staff' | 'accountant' | 'viewer'
  permissions?: any
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}

export interface Customer {
  id: string
  customerCode?: string
  customerType: 'individual' | 'business'
  title?: 'Mr' | 'Mrs' | 'Ms' | 'Dr' | 'Prof'
  firstName: string
  lastName?: string
  businessName?: string
  phone: string
  alternatePhone?: string
  email?: string
  dateOfBirth?: Date
  anniversaryDate?: Date
  gender?: 'male' | 'female' | 'other'
  addressLine1?: string
  addressLine2?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  gstNumber?: string
  panNumber?: string
  aadharNumber?: string
  creditLimit: number
  creditDays: number
  totalPurchases: number
  totalOutstanding: number
  loyaltyPoints: number
  preferredContact: 'phone' | 'email' | 'sms' | 'whatsapp'
  notes?: string
  isActive: boolean
  lastVisit?: Date
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface InventoryItem {
  id: string
  itemCode: string
  barcode?: string
  name: string
  description?: string
  categoryId?: string
  subcategoryId?: string
  metalType: 'gold' | 'silver' | 'platinum' | 'diamond' | 'other'
  purity?: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  diamondWeight: number
  diamondPieces: number
  stoneDetails?: any
  size?: string
  gender: 'male' | 'female' | 'unisex' | 'kids'
  occasion?: string
  designNumber?: string
  supplierId?: string
  purchaseRate: number
  makingCharges: number
  stoneCharges: number
  otherCharges: number
  totalCost: number
  marginPercentage: number
  sellingPrice: number
  mrp: number
  stockQuantity: number
  minStockLevel: number
  maxStockLevel: number
  location?: string
  status: 'active' | 'sold' | 'reserved' | 'damaged' | 'repair' | 'inactive'
  images?: any
  tags?: any
  hsnCode?: string
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface Sale {
  id: string
  invoiceNumber: string
  invoiceDate: Date
  customerId?: string
  saleType: 'cash' | 'credit' | 'exchange' | 'scheme'
  subtotal: number
  discountPercentage: number
  discountAmount: number
  taxableAmount: number
  cgstRate: number
  sgstRate: number
  igstRate: number
  cgstAmount: number
  sgstAmount: number
  igstAmount: number
  totalTax: number
  roundOff: number
  grandTotal: number
  exchangeDeduction: number
  finalAmount: number
  paymentMethod: 'cash' | 'card' | 'upi' | 'bank_transfer' | 'cheque' | 'mixed'
  paymentStatus: 'pending' | 'partial' | 'paid' | 'overdue'
  dueDate?: Date
  status: 'draft' | 'confirmed' | 'delivered' | 'cancelled' | 'returned'
  notes?: string
  termsConditions?: string
  salesPersonId?: string
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface SaleItem {
  id: string
  saleId: string
  inventoryId?: string
  itemCode?: string
  itemName: string
  description?: string
  category?: string
  metalType: 'gold' | 'silver' | 'platinum' | 'diamond' | 'other'
  purity?: string
  grossWeight: number
  stoneWeight: number
  netWeight: number
  ratePerGram: number
  metalAmount: number
  makingCharges: number
  stoneCharges: number
  otherCharges: number
  totalAmount: number
  discountPercentage: number
  discountAmount: number
  finalAmount: number
  hsnCode?: string
  createdAt: Date
  updatedAt: Date
}

// ============================================================================
// USER SERVICE
// ============================================================================

export class UserService {
  private db: DatabaseConnection

  constructor(db: DatabaseConnection) {
    this.db = db
  }

  async createUser(userData: Partial<User>): Promise<User> {
    // Validate required fields
    if (!userData.username || !userData.email || !userData.firstName) {
      throw new ValidationError('Username, email, and first name are required')
    }

    // Check if username or email already exists
    const existingUser = await this.db.query(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [userData.username, userData.email]
    )

    if (existingUser.length > 0) {
      throw new BusinessLogicError('Username or email already exists')
    }

    const userId = randomUUID()
    const user: User = {
      id: userId,
      username: userData.username,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName || '',
      phone: userData.phone,
      role: userData.role || 'sales_staff',
      permissions: userData.permissions,
      isActive: userData.isActive !== false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await this.db.query(`
      INSERT INTO users (
        id, username, email, password_hash, first_name, last_name, phone, role,
        permissions, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      user.id, user.username, user.email, '$2b$10$defaulthash', // Should use proper password hashing
      user.firstName, user.lastName, user.phone, user.role,
      JSON.stringify(user.permissions), user.isActive, user.createdAt, user.updatedAt
    ])

    return user
  }

  async getUserById(id: string): Promise<User | null> {
    const users = await this.db.query(
      'SELECT * FROM users WHERE id = ? AND is_active = TRUE',
      [id]
    )

    if (users.length === 0) return null

    return this.mapRowToUser(users[0])
  }

  async getUserByUsername(username: string): Promise<User | null> {
    const users = await this.db.query(
      'SELECT * FROM users WHERE username = ? AND is_active = TRUE',
      [username]
    )

    if (users.length === 0) return null

    return this.mapRowToUser(users[0])
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const user = await this.getUserById(id)
    if (!user) {
      throw new BusinessLogicError('User not found')
    }

    const updateFields = []
    const updateValues = []

    if (updates.email) {
      updateFields.push('email = ?')
      updateValues.push(updates.email)
    }
    if (updates.firstName) {
      updateFields.push('first_name = ?')
      updateValues.push(updates.firstName)
    }
    if (updates.lastName !== undefined) {
      updateFields.push('last_name = ?')
      updateValues.push(updates.lastName)
    }
    if (updates.phone !== undefined) {
      updateFields.push('phone = ?')
      updateValues.push(updates.phone)
    }
    if (updates.role) {
      updateFields.push('role = ?')
      updateValues.push(updates.role)
    }
    if (updates.isActive !== undefined) {
      updateFields.push('is_active = ?')
      updateValues.push(updates.isActive)
    }

    updateFields.push('updated_at = ?')
    updateValues.push(new Date())
    updateValues.push(id)

    await this.db.query(`
      UPDATE users SET ${updateFields.join(', ')} WHERE id = ?
    `, updateValues)

    return await this.getUserById(id) as User
  }

  async listUsers(filters?: { role?: string; isActive?: boolean }): Promise<User[]> {
    let query = 'SELECT * FROM users WHERE 1=1'
    const params = []

    if (filters?.role) {
      query += ' AND role = ?'
      params.push(filters.role)
    }

    if (filters?.isActive !== undefined) {
      query += ' AND is_active = ?'
      params.push(filters.isActive)
    }

    query += ' ORDER BY created_at DESC'

    const rows = await this.db.query(query, params)
    return rows.map(row => this.mapRowToUser(row))
  }

  private mapRowToUser(row: any): User {
    return {
      id: row.id,
      username: row.username,
      email: row.email,
      firstName: row.first_name,
      lastName: row.last_name,
      phone: row.phone,
      role: row.role,
      permissions: row.permissions ? JSON.parse(row.permissions) : null,
      isActive: row.is_active,
      lastLogin: row.last_login,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }
}

// ============================================================================
// CUSTOMER SERVICE
// ============================================================================

export class CustomerService {
  private db: DatabaseConnection

  constructor(db: DatabaseConnection) {
    this.db = db
  }

  async createCustomer(customerData: Partial<Customer>): Promise<Customer> {
    // Validate required fields
    if (!customerData.firstName || !customerData.phone) {
      throw new ValidationError('First name and phone are required')
    }

    // Check if phone already exists
    const existingCustomer = await this.db.query(
      'SELECT id FROM customers WHERE phone = ?',
      [customerData.phone]
    )

    if (existingCustomer.length > 0) {
      throw new BusinessLogicError('Customer with this phone number already exists')
    }

    const customerId = randomUUID()
    const customerCode = await this.generateCustomerCode()

    const customer: Customer = {
      id: customerId,
      customerCode,
      customerType: customerData.customerType || 'individual',
      title: customerData.title,
      firstName: customerData.firstName,
      lastName: customerData.lastName,
      businessName: customerData.businessName,
      phone: customerData.phone,
      alternatePhone: customerData.alternatePhone,
      email: customerData.email,
      dateOfBirth: customerData.dateOfBirth,
      anniversaryDate: customerData.anniversaryDate,
      gender: customerData.gender,
      addressLine1: customerData.addressLine1,
      addressLine2: customerData.addressLine2,
      city: customerData.city,
      state: customerData.state,
      postalCode: customerData.postalCode,
      country: customerData.country || 'India',
      gstNumber: customerData.gstNumber,
      panNumber: customerData.panNumber,
      aadharNumber: customerData.aadharNumber,
      creditLimit: customerData.creditLimit || 0,
      creditDays: customerData.creditDays || 0,
      totalPurchases: 0,
      totalOutstanding: 0,
      loyaltyPoints: 0,
      preferredContact: customerData.preferredContact || 'phone',
      notes: customerData.notes,
      isActive: customerData.isActive !== false,
      createdBy: customerData.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await this.db.query(`
      INSERT INTO customers (
        id, customer_code, customer_type, title, first_name, last_name, business_name,
        phone, alternate_phone, email, date_of_birth, anniversary_date, gender,
        address_line1, address_line2, city, state, postal_code, country,
        gst_number, pan_number, aadhar_number, credit_limit, credit_days,
        total_purchases, total_outstanding, loyalty_points, preferred_contact,
        notes, is_active, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      customer.id, customer.customerCode, customer.customerType, customer.title,
      customer.firstName, customer.lastName, customer.businessName,
      customer.phone, customer.alternatePhone, customer.email,
      customer.dateOfBirth, customer.anniversaryDate, customer.gender,
      customer.addressLine1, customer.addressLine2, customer.city, customer.state,
      customer.postalCode, customer.country, customer.gstNumber, customer.panNumber,
      customer.aadharNumber, customer.creditLimit, customer.creditDays,
      customer.totalPurchases, customer.totalOutstanding, customer.loyaltyPoints,
      customer.preferredContact, customer.notes, customer.isActive,
      customer.createdBy, customer.createdAt, customer.updatedAt
    ])

    return customer
  }

  async getCustomerById(id: string): Promise<Customer | null> {
    const customers = await this.db.query(
      'SELECT * FROM customers WHERE id = ?',
      [id]
    )

    if (customers.length === 0) return null

    return this.mapRowToCustomer(customers[0])
  }

  async searchCustomers(searchTerm: string): Promise<Customer[]> {
    const customers = await this.db.query(`
      SELECT * FROM customers 
      WHERE (first_name LIKE ? OR last_name LIKE ? OR phone LIKE ? OR email LIKE ?)
      AND is_active = TRUE
      ORDER BY first_name, last_name
      LIMIT 50
    `, [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`])

    return customers.map(row => this.mapRowToCustomer(row))
  }

  private async generateCustomerCode(): Promise<string> {
    const result = await this.db.query(
      'SELECT COUNT(*) as count FROM customers'
    )
    const count = result[0].count + 1
    return `CUST${String(count).padStart(6, '0')}`
  }

  private mapRowToCustomer(row: any): Customer {
    return {
      id: row.id,
      customerCode: row.customer_code,
      customerType: row.customer_type,
      title: row.title,
      firstName: row.first_name,
      lastName: row.last_name,
      businessName: row.business_name,
      phone: row.phone,
      alternatePhone: row.alternate_phone,
      email: row.email,
      dateOfBirth: row.date_of_birth,
      anniversaryDate: row.anniversary_date,
      gender: row.gender,
      addressLine1: row.address_line1,
      addressLine2: row.address_line2,
      city: row.city,
      state: row.state,
      postalCode: row.postal_code,
      country: row.country,
      gstNumber: row.gst_number,
      panNumber: row.pan_number,
      aadharNumber: row.aadhar_number,
      creditLimit: parseFloat(row.credit_limit),
      creditDays: row.credit_days,
      totalPurchases: parseFloat(row.total_purchases),
      totalOutstanding: parseFloat(row.total_outstanding),
      loyaltyPoints: row.loyalty_points,
      preferredContact: row.preferred_contact,
      notes: row.notes,
      isActive: row.is_active,
      lastVisit: row.last_visit,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }
}

// ============================================================================
// INVENTORY SERVICE
// ============================================================================

export class InventoryService {
  private db: DatabaseConnection

  constructor(db: DatabaseConnection) {
    this.db = db
  }

  async createInventoryItem(itemData: Partial<InventoryItem>): Promise<InventoryItem> {
    // Validate required fields
    if (!itemData.name || !itemData.metalType) {
      throw new ValidationError('Name and metal type are required')
    }

    const itemId = randomUUID()
    const itemCode = itemData.itemCode || await this.generateItemCode(itemData.metalType)

    // Check if item code already exists
    const existingItem = await this.db.query(
      'SELECT id FROM inventory WHERE item_code = ?',
      [itemCode]
    )

    if (existingItem.length > 0) {
      throw new BusinessLogicError('Item code already exists')
    }

    const item: InventoryItem = {
      id: itemId,
      itemCode,
      barcode: itemData.barcode,
      name: itemData.name,
      description: itemData.description,
      categoryId: itemData.categoryId,
      subcategoryId: itemData.subcategoryId,
      metalType: itemData.metalType,
      purity: itemData.purity,
      grossWeight: itemData.grossWeight || 0,
      stoneWeight: itemData.stoneWeight || 0,
      netWeight: (itemData.grossWeight || 0) - (itemData.stoneWeight || 0),
      diamondWeight: itemData.diamondWeight || 0,
      diamondPieces: itemData.diamondPieces || 0,
      stoneDetails: itemData.stoneDetails,
      size: itemData.size,
      gender: itemData.gender || 'unisex',
      occasion: itemData.occasion,
      designNumber: itemData.designNumber,
      supplierId: itemData.supplierId,
      purchaseRate: itemData.purchaseRate || 0,
      makingCharges: itemData.makingCharges || 0,
      stoneCharges: itemData.stoneCharges || 0,
      otherCharges: itemData.otherCharges || 0,
      totalCost: (itemData.purchaseRate || 0) + (itemData.makingCharges || 0) + (itemData.stoneCharges || 0) + (itemData.otherCharges || 0),
      marginPercentage: itemData.marginPercentage || 0,
      sellingPrice: itemData.sellingPrice || 0,
      mrp: itemData.mrp || 0,
      stockQuantity: itemData.stockQuantity || 1,
      minStockLevel: itemData.minStockLevel || 0,
      maxStockLevel: itemData.maxStockLevel || 100,
      location: itemData.location,
      status: itemData.status || 'active',
      images: itemData.images,
      tags: itemData.tags,
      hsnCode: itemData.hsnCode,
      createdBy: itemData.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await this.db.query(`
      INSERT INTO inventory (
        id, item_code, barcode, name, description, category_id, subcategory_id,
        metal_type, purity, gross_weight, stone_weight, diamond_weight, diamond_pieces,
        stone_details, size, gender, occasion, design_number, supplier_id,
        purchase_rate, making_charges, stone_charges, other_charges, margin_percentage,
        selling_price, mrp, stock_quantity, min_stock_level, max_stock_level,
        location, status, images, tags, hsn_code, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      item.id, item.itemCode, item.barcode, item.name, item.description,
      item.categoryId, item.subcategoryId, item.metalType, item.purity,
      item.grossWeight, item.stoneWeight, item.diamondWeight, item.diamondPieces,
      JSON.stringify(item.stoneDetails), item.size, item.gender, item.occasion,
      item.designNumber, item.supplierId, item.purchaseRate, item.makingCharges,
      item.stoneCharges, item.otherCharges, item.marginPercentage, item.sellingPrice,
      item.mrp, item.stockQuantity, item.minStockLevel, item.maxStockLevel,
      item.location, item.status, JSON.stringify(item.images), JSON.stringify(item.tags),
      item.hsnCode, item.createdBy, item.createdAt, item.updatedAt
    ])

    return item
  }

  async getInventoryItemById(id: string): Promise<InventoryItem | null> {
    const items = await this.db.query(
      'SELECT * FROM inventory WHERE id = ?',
      [id]
    )

    if (items.length === 0) return null

    return this.mapRowToInventoryItem(items[0])
  }

  async searchInventory(filters: {
    searchTerm?: string
    metalType?: string
    purity?: string
    status?: string
    categoryId?: string
    minPrice?: number
    maxPrice?: number
    limit?: number
  }): Promise<InventoryItem[]> {
    let query = 'SELECT * FROM inventory WHERE 1=1'
    const params = []

    if (filters.searchTerm) {
      query += ' AND (name LIKE ? OR item_code LIKE ? OR description LIKE ?)'
      params.push(`%${filters.searchTerm}%`, `%${filters.searchTerm}%`, `%${filters.searchTerm}%`)
    }

    if (filters.metalType) {
      query += ' AND metal_type = ?'
      params.push(filters.metalType)
    }

    if (filters.purity) {
      query += ' AND purity = ?'
      params.push(filters.purity)
    }

    if (filters.status) {
      query += ' AND status = ?'
      params.push(filters.status)
    }

    if (filters.categoryId) {
      query += ' AND category_id = ?'
      params.push(filters.categoryId)
    }

    if (filters.minPrice) {
      query += ' AND selling_price >= ?'
      params.push(filters.minPrice)
    }

    if (filters.maxPrice) {
      query += ' AND selling_price <= ?'
      params.push(filters.maxPrice)
    }

    query += ' ORDER BY created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)
    }

    const rows = await this.db.query(query, params)
    return rows.map(row => this.mapRowToInventoryItem(row))
  }

  async updateInventoryItem(id: string, updates: Partial<InventoryItem>): Promise<InventoryItem> {
    const item = await this.getInventoryItemById(id)
    if (!item) {
      throw new BusinessLogicError('Inventory item not found')
    }

    const updateFields = []
    const updateValues = []

    // Add all updatable fields
    const updatableFields = [
      'name', 'description', 'category_id', 'subcategory_id', 'purity',
      'gross_weight', 'stone_weight', 'diamond_weight', 'diamond_pieces',
      'size', 'gender', 'occasion', 'design_number', 'supplier_id',
      'purchase_rate', 'making_charges', 'stone_charges', 'other_charges',
      'margin_percentage', 'selling_price', 'mrp', 'stock_quantity',
      'min_stock_level', 'max_stock_level', 'location', 'status'
    ]

    for (const field of updatableFields) {
      const camelField = this.snakeToCamel(field)
      if (updates[camelField] !== undefined) {
        updateFields.push(`${field} = ?`)
        updateValues.push(updates[camelField])
      }
    }

    if (updateFields.length === 0) {
      return item
    }

    updateFields.push('updated_at = ?')
    updateValues.push(new Date())
    updateValues.push(id)

    await this.db.query(`
      UPDATE inventory SET ${updateFields.join(', ')} WHERE id = ?
    `, updateValues)

    return await this.getInventoryItemById(id) as InventoryItem
  }

  private async generateItemCode(metalType: string): Promise<string> {
    const prefix = metalType === 'gold' ? 'G' : metalType === 'silver' ? 'S' : 'O'
    const result = await this.db.query(
      'SELECT COUNT(*) as count FROM inventory WHERE metal_type = ?',
      [metalType]
    )
    const count = result[0].count + 1
    return `${prefix}${String(count).padStart(5, '0')}`
  }

  private snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  private mapRowToInventoryItem(row: any): InventoryItem {
    return {
      id: row.id,
      itemCode: row.item_code,
      barcode: row.barcode,
      name: row.name,
      description: row.description,
      categoryId: row.category_id,
      subcategoryId: row.subcategory_id,
      metalType: row.metal_type,
      purity: row.purity,
      grossWeight: parseFloat(row.gross_weight),
      stoneWeight: parseFloat(row.stone_weight),
      netWeight: parseFloat(row.net_weight),
      diamondWeight: parseFloat(row.diamond_weight),
      diamondPieces: row.diamond_pieces,
      stoneDetails: row.stone_details ? JSON.parse(row.stone_details) : null,
      size: row.size,
      gender: row.gender,
      occasion: row.occasion,
      designNumber: row.design_number,
      supplierId: row.supplier_id,
      purchaseRate: parseFloat(row.purchase_rate),
      makingCharges: parseFloat(row.making_charges),
      stoneCharges: parseFloat(row.stone_charges),
      otherCharges: parseFloat(row.other_charges),
      totalCost: parseFloat(row.total_cost),
      marginPercentage: parseFloat(row.margin_percentage),
      sellingPrice: parseFloat(row.selling_price),
      mrp: parseFloat(row.mrp),
      stockQuantity: row.stock_quantity,
      minStockLevel: row.min_stock_level,
      maxStockLevel: row.max_stock_level,
      location: row.location,
      status: row.status,
      images: row.images ? JSON.parse(row.images) : null,
      tags: row.tags ? JSON.parse(row.tags) : null,
      hsnCode: row.hsn_code,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }
}

// ============================================================================
// SALES SERVICE
// ============================================================================

export class SalesService {
  private db: DatabaseConnection

  constructor(db: DatabaseConnection) {
    this.db = db
  }

  async createSale(saleData: Partial<Sale>, items: Partial<SaleItem>[]): Promise<Sale> {
    // Validate required fields
    if (!items || items.length === 0) {
      throw new ValidationError('At least one sale item is required')
    }

    const saleId = randomUUID()
    const invoiceNumber = await this.generateInvoiceNumber()

    // Calculate totals
    const subtotal = items.reduce((sum, item) => sum + (item.finalAmount || 0), 0)
    const discountAmount = saleData.discountAmount || 0
    const taxableAmount = subtotal - discountAmount
    const cgstRate = saleData.cgstRate || 1.5
    const sgstRate = saleData.sgstRate || 1.5
    const cgstAmount = (taxableAmount * cgstRate) / 100
    const sgstAmount = (taxableAmount * sgstRate) / 100
    const totalTax = cgstAmount + sgstAmount
    const grandTotal = taxableAmount + totalTax + (saleData.roundOff || 0)
    const finalAmount = grandTotal - (saleData.exchangeDeduction || 0)

    const sale: Sale = {
      id: saleId,
      invoiceNumber,
      invoiceDate: saleData.invoiceDate || new Date(),
      customerId: saleData.customerId,
      saleType: saleData.saleType || 'cash',
      subtotal,
      discountPercentage: saleData.discountPercentage || 0,
      discountAmount,
      taxableAmount,
      cgstRate,
      sgstRate,
      igstRate: saleData.igstRate || 0,
      cgstAmount,
      sgstAmount,
      igstAmount: 0,
      totalTax,
      roundOff: saleData.roundOff || 0,
      grandTotal,
      exchangeDeduction: saleData.exchangeDeduction || 0,
      finalAmount,
      paymentMethod: saleData.paymentMethod || 'cash',
      paymentStatus: saleData.paymentStatus || 'paid',
      dueDate: saleData.dueDate,
      status: saleData.status || 'confirmed',
      notes: saleData.notes,
      termsConditions: saleData.termsConditions,
      salesPersonId: saleData.salesPersonId,
      createdBy: saleData.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Start transaction
    await this.db.query('START TRANSACTION')

    try {
      // Insert sale
      await this.db.query(`
        INSERT INTO sales (
          id, invoice_number, invoice_date, customer_id, sale_type, subtotal,
          discount_percentage, discount_amount, cgst_rate, sgst_rate, igst_rate,
          round_off, exchange_deduction, payment_method, payment_status,
          due_date, status, notes, terms_conditions, sales_person_id,
          created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        sale.id, sale.invoiceNumber, sale.invoiceDate, sale.customerId, sale.saleType,
        sale.subtotal, sale.discountPercentage, sale.discountAmount, sale.cgstRate,
        sale.sgstRate, sale.igstRate, sale.roundOff, sale.exchangeDeduction,
        sale.paymentMethod, sale.paymentStatus, sale.dueDate, sale.status,
        sale.notes, sale.termsConditions, sale.salesPersonId, sale.createdBy,
        sale.createdAt, sale.updatedAt
      ])

      // Insert sale items
      for (const itemData of items) {
        const saleItemId = randomUUID()
        await this.db.query(`
          INSERT INTO sale_items (
            id, sale_id, inventory_id, item_code, item_name, description,
            category, metal_type, purity, gross_weight, stone_weight,
            rate_per_gram, making_charges, stone_charges, other_charges,
            discount_percentage, discount_amount, hsn_code, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          saleItemId, saleId, itemData.inventoryId, itemData.itemCode,
          itemData.itemName, itemData.description, itemData.category,
          itemData.metalType, itemData.purity, itemData.grossWeight,
          itemData.stoneWeight, itemData.ratePerGram, itemData.makingCharges,
          itemData.stoneCharges, itemData.otherCharges, itemData.discountPercentage,
          itemData.discountAmount, itemData.hsnCode, new Date(), new Date()
        ])

        // Update inventory if item is linked
        if (itemData.inventoryId) {
          await this.db.query(`
            UPDATE inventory
            SET stock_quantity = stock_quantity - 1, status = 'sold', updated_at = ?
            WHERE id = ? AND stock_quantity > 0
          `, [new Date(), itemData.inventoryId])
        }
      }

      await this.db.query('COMMIT')
      return sale

    } catch (error) {
      await this.db.query('ROLLBACK')
      throw error
    }
  }

  async getSaleById(id: string): Promise<Sale | null> {
    const sales = await this.db.query(
      'SELECT * FROM sales WHERE id = ?',
      [id]
    )

    if (sales.length === 0) return null

    return this.mapRowToSale(sales[0])
  }

  async getSaleItems(saleId: string): Promise<SaleItem[]> {
    const items = await this.db.query(
      'SELECT * FROM sale_items WHERE sale_id = ? ORDER BY created_at',
      [saleId]
    )

    return items.map(row => this.mapRowToSaleItem(row))
  }

  private async generateInvoiceNumber(): Promise<string> {
    const currentYear = new Date().getFullYear()
    const financialYear = `${currentYear}-${String(currentYear + 1).slice(-2)}`

    const result = await this.db.query(`
      SELECT current_number FROM bill_sequences
      WHERE sequence_type = 'sales_invoice' AND financial_year = ?
    `, [financialYear])

    let currentNumber = 1
    if (result.length > 0) {
      currentNumber = result[0].current_number + 1
      await this.db.query(`
        UPDATE bill_sequences
        SET current_number = ?, updated_at = ?
        WHERE sequence_type = 'sales_invoice' AND financial_year = ?
      `, [currentNumber, new Date(), financialYear])
    } else {
      await this.db.query(`
        INSERT INTO bill_sequences (id, sequence_type, prefix, current_number, financial_year)
        VALUES (?, 'sales_invoice', 'INV', 1, ?)
      `, [randomUUID(), financialYear])
    }

    return `INV/${financialYear}/${String(currentNumber).padStart(4, '0')}`
  }

  private mapRowToSale(row: any): Sale {
    return {
      id: row.id,
      invoiceNumber: row.invoice_number,
      invoiceDate: row.invoice_date,
      customerId: row.customer_id,
      saleType: row.sale_type,
      subtotal: parseFloat(row.subtotal),
      discountPercentage: parseFloat(row.discount_percentage),
      discountAmount: parseFloat(row.discount_amount),
      taxableAmount: parseFloat(row.taxable_amount),
      cgstRate: parseFloat(row.cgst_rate),
      sgstRate: parseFloat(row.sgst_rate),
      igstRate: parseFloat(row.igst_rate),
      cgstAmount: parseFloat(row.cgst_amount),
      sgstAmount: parseFloat(row.sgst_amount),
      igstAmount: parseFloat(row.igst_amount),
      totalTax: parseFloat(row.total_tax),
      roundOff: parseFloat(row.round_off),
      grandTotal: parseFloat(row.grand_total),
      exchangeDeduction: parseFloat(row.exchange_deduction),
      finalAmount: parseFloat(row.final_amount),
      paymentMethod: row.payment_method,
      paymentStatus: row.payment_status,
      dueDate: row.due_date,
      status: row.status,
      notes: row.notes,
      termsConditions: row.terms_conditions,
      salesPersonId: row.sales_person_id,
      createdBy: row.created_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }

  private mapRowToSaleItem(row: any): SaleItem {
    return {
      id: row.id,
      saleId: row.sale_id,
      inventoryId: row.inventory_id,
      itemCode: row.item_code,
      itemName: row.item_name,
      description: row.description,
      category: row.category,
      metalType: row.metal_type,
      purity: row.purity,
      grossWeight: parseFloat(row.gross_weight),
      stoneWeight: parseFloat(row.stone_weight),
      netWeight: parseFloat(row.net_weight),
      ratePerGram: parseFloat(row.rate_per_gram),
      metalAmount: parseFloat(row.metal_amount),
      makingCharges: parseFloat(row.making_charges),
      stoneCharges: parseFloat(row.stone_charges),
      otherCharges: parseFloat(row.other_charges),
      totalAmount: parseFloat(row.total_amount),
      discountPercentage: parseFloat(row.discount_percentage),
      discountAmount: parseFloat(row.discount_amount),
      finalAmount: parseFloat(row.final_amount),
      hsnCode: row.hsn_code,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }
  }
}

// Export all services
export const createProfessionalServices = (db: DatabaseConnection) => ({
  userService: new UserService(db),
  customerService: new CustomerService(db),
  inventoryService: new InventoryService(db),
  salesService: new SalesService(db)
})
